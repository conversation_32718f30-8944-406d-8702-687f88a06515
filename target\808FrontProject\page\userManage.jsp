<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@taglib prefix="s" uri="/struts-tags"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>用户信息管理</title>

<link rel="stylesheet" type="text/css"
	href="/808FrontProject/css/user.css">

<script type="text/javascript" src="/808FrontProject/js/lib.jquery.js"></script>

<script type="text/javascript">

    $(function(){
    	 var roleCreateAllowed="<%=session.getAttribute("roleCreateAllowed")%>"; 
    	 if(roleCreateAllowed==0)
    		 $("#addButton").attr("disabled",true);
    });
    
	function userAdd() {
		$("#modifyUserInfoForm").attr("action",
				"/808FrontProject/user/addAction.action").submit();
	}

	function userEdit() {
		var userName = $('input:radio[name="selectedUserName"]:checked').val();
		if (undefined == userName || userName == "") {
			alert("请在单选按钮中选择要编辑的用户名称！");
			return false;
		}

		var path = "/808FrontProject/user/editAction.action?userName="
				+ userName;
		$("#modifyUserInfoForm").attr("action", path).submit();
	}

	function userDelete() {
		var userName = $('input:radio[name="selectedUserName"]:checked').val();
		if (undefined == userName || userName == "") {
			alert("请在单选按钮中选择要刪除的用户名称！");
			return false;
		}
		if (window.confirm("确定要删除当前用户信息吗？")) {
			var path = "/808FrontProject/user/deleteAction.action?userName="
					+ userName;
			$("#modifyUserInfoForm").attr("action", path).submit();
		} else {
			return false;
		}
	}
</script>
</head>

<body>
	<span class="title1">用户信息管理</span>
	<s:form action="queryAction" theme="simple" namespace="/user">
		<table class="tableCommon">
			<tr>
				<td width="150px">用户名称</td>
				<td><s:textfield name="userInfo.name" /></td>
				<!-- 
				<td width="100px">状态</td>
				<td><s:select name="userInfo.state"
						list="#{1:'正常',2:'暂停',3:'作废' }" listKey="value" listValue="value"
						headerKey="0" headerValue="请选择"></s:select></td>
				 -->
				<td width="150px">用户角色</td>
				<td><s:select name="userInfo.roleName"
						list="#{1:'超级管理员',2:'管理员',3:'操作员' }" listKey="value"
						listValue="value" headerKey="0" headerValue="请选择"></s:select></td>
				<td style="text-align: center;"><input class="buttonStyle"
					type="submit" name="" value="查询" /></td>
			</tr>
		</table>
	</s:form>
	<s:form id="modifyUserInfoForm">
		<table class="tableCommon">
			<tr class="thSize thColor">
				<td width="5%">序号</td>
				<td>用户名称</td>
				<!-- 
				<td>状态</td>
				 -->
				<td>用户角色</td>
				<td>允许创建角色</td>
				<td>创建者</td>
				<td>选择</td>
			</tr>
			<s:iterator value="userInfoList" status="st">
				<tr <s:if test="#st.even">bgcolor="#ECF5FF"</s:if>>
					<td style="padding-left:0px;text-align:center"><s:property value="#st.index+1" /></td>
					<td><s:property value="name" /></td>
					<!-- 
					<td><s:property value="state" /></td>
					 -->
					<td><s:property value="roleName" /></td>
					<td><s:if test="createRoleAllowed==0">
			                                    不允许  
                    </s:if> <s:else> 
                                                                             允许 
                    </s:else></td>
					<td><s:property value="parentUserName" /></td>
					<td><input class="radioStyle" type="radio"
						name="selectedUserName" value='<s:property value="name" />' /></td>
				</tr>
			</s:iterator>
			<tr>
				<td colspan="7" style="text-align: center;"><input
					class="buttonStyle" type="button" id="addButton" value="添加" onclick="userAdd()" />&nbsp;
					<input class="buttonStyle" type="button" value="编辑"
					onclick="userEdit()" />&nbsp; <input class="buttonStyle"
					type="button" value="删除" onclick="userDelete()" /></td>
			</tr>
		</table>
	</s:form>

</body>
</html>