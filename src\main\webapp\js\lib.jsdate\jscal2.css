/* CSS */

.DynarchCalendar {
  border: 1px solid #aaa;
  -moz-user-select: none;
  -webkit-user-select: none;
  user-select: none;
  background: #660000;
  font: 12px;
  line-height: 14px;
  position: relative;
  cursor: default;
}

.DynarchCalendar table {
  border-collapse: collapse;
  font: 11px;
  line-height: 14px;
}

.DynarchCalendar-topBar {
  border-bottom: 1px solid #aaa;
  background: #ddd;
  padding: 5px 0 0 0;
}

table.DynarchCalendar-titleCont {
  font-size: 13px; 
  font-weight: bold;
  color: #444;
  text-align: center;
  z-index: 9;
  position: relative;
  margin-top: -6px;
}

.DynarchCalendar-title div {
  padding: 5px 17px;
  
}
.DynarchCalendar-hover-title div {
  background-color: #fff;
  border: 1px solid #000;
  padding: 4px 16px;
  background-image: url("img/drop-down.gif");
  background-repeat: no-repeat;
  background-position: 100% 50%;
}
.DynarchCalendar-pressed-title div {
  border: 1px solid #000;
  padding: 4px 16px;
  background-color: #777;
  color: #fff;
  background-image: url("img/drop-up.gif");
  background-repeat: no-repeat;
  background-position: 100% 50%;
}


.DynarchCalendar-bottomBar {
  border-top: 1px solid #aaa;
  background-color: #ddd;
  padding: 2px;
  position: relative;
  text-align: center;
  font-size:13px;
}

.DynarchCalendar-bottomBar-today {
  padding: 2px 15px;
  font-weight:bold;
  color:#090;
}

.DynarchCalendar-hover-bottomBar-today {
  border: 1px solid #000;
  background-color: #fff;
  padding: 1px 14px;
}
.DynarchCalendar-pressed-bottomBar-today {
  border: 1px solid #000;
  background-color: #fff;
  color: #fff;
  padding: 1px 14px;
}

.DynarchCalendar-body {
  position: relative;
  overflow: hidden;
  padding-top: 5px;
  padding-bottom: 5px;
}

.DynarchCalendar-first-col { padding-left: 5px; }
.DynarchCalendar-last-col { padding-right: 5px; }

.DynarchCalendar-animBody-backYear {
  position: absolute;
  top: -100%;
  left: 0;
}
.DynarchCalendar-animBody-back {
  position: absolute;
  top: 5px;
  left: -100%;
}
.DynarchCalendar-animBody-fwd {
  position: absolute;
  top: 5px;
  left: 100%;
}
.DynarchCalendar-animBody-now {
  position: absolute;
  top: 5px;
  left: 0;
}
.DynarchCalendar-animBody-fwdYear {
  position: absolute;
  top: 100%;
  left: 0;
}

.DynarchCalendar-dayNames {
  padding-left: 5px;
  padding-right: 5px;
}

.DynarchCalendar-dayNames div { 
  font-weight: bold; 
  color: #444;
}

.DynarchCalendar-navBtn {
  position: absolute;
  top: 5px;
  z-index: 10;
}

.DynarchCalendar-navBtn div {
  background-repeat: no-repeat;
  background-position: 50% 50%;
  height: 15px;
  width: 16px;
  padding: 1px;
}
.DynarchCalendar-hover-navBtn div {
  border: 1px solid #000;
  padding: 0;
  background-color: #fff;
}
.DynarchCalendar-navDisabled {
  opacity: 0.3;
  filter: alpha(opacity=30);
}
.DynarchCalendar-pressed-navBtn div {
  border: 1px solid #000;
  padding: 0;
  background-color: #777;
  color: #fff;
}

.DynarchCalendar-prevMonth {
  left: 25px;
}

.DynarchCalendar-nextMonth {
  left: 100%;
  margin-left: -43px;
}

.DynarchCalendar-prevYear {
  left: 5px;
}

.DynarchCalendar-nextYear {
  left: 100%;
  margin-left: -23px;
}

.DynarchCalendar-prevMonth div {
  background-image: url("img/nav-left.gif");
}

.DynarchCalendar-nextMonth div {
  background-image: url("img/nav-right.gif");
}

.DynarchCalendar-prevYear div {
  background-image: url("img/nav-left-x2.gif");
}

.DynarchCalendar-nextYear div {
  background-image: url("img/nav-right-x2.gif");
}

.DynarchCalendar-menu {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: #ddd;
  overflow: hidden;
  opacity: 0.85;
  filter: alpha(opacity=85);
}

.DynarchCalendar-menu table td div {
  text-align: center;
  font-weight: bold;
  padding: 3px 5px;
  font-size:13px;
}
.DynarchCalendar-menu table td div.DynarchCalendar-menu-month {
  width: 4em;
  text-align: center;
}
.DynarchCalendar-menu table td div.DynarchCalendar-hover-navBtn {
  border: 1px solid #000;
  padding: 2px 4px;
  background-color: #fff;
  color: #000;
}
.DynarchCalendar-menu table td div.DynarchCalendar-pressed-navBtn {
  border: 1px solid #000;
  padding: 2px 4px;
  background-color: #777;
  color: #fff !important;
}

.DynarchCalendar-menu-year {
  text-align: center;
  font-size: 13px ;
  color:#900;
  font-weight: bold;
}

.DynarchCalendar-menu-sep {
  height: 1px; 
  line-height: 1px;
  overflow: hidden;
  border-top: 1px solid #888;
  background: #fff;
  margin-top: 4px; margin-bottom: 3px;
}

.DynarchCalendar-time td { font-weight: bold; font-size: 13px; }
.DynarchCalendar-time-hour, .DynarchCalendar-time-minute { padding: 1px 3px; }
.DynarchCalendar-time-down { background: url("img/drop-down.gif") no-repeat 50% 50%; width: 11px; height: 8px; opacity: 0.5; }
.DynarchCalendar-time-up { background: url("img/drop-up.gif") no-repeat 50% 50%; width: 11px; height: 8px; opacity: 0.5; }
.DynarchCalendar-time-sep { padding: 0 2px; }
.DynarchCalendar-hover-time { background-color: #444; color: #fff; opacity: 1; }
.DynarchCalendar-pressed-time { background-color: #000; color: #fff; opacity: 1; }
.DynarchCalendar-time-am { padding: 1px; width: 2.5em; text-align: center; }

/* body */

.DynarchCalendar-hover-week { background-color: #ddd; }

.DynarchCalendar-dayNames div, .DynarchCalendar-day, .DynarchCalendar-weekNumber {
  width: 1.3em;
  padding: 3px 4px;
  text-align: center;
  font-size:13px;
}
.DynarchCalendar-weekNumber {
  border-right: 1px solid #aaa;
  margin-right: 4px;
  width: 2em !important;
  padding-right: 8px !important;
}

.DynarchCalendar-day {
  text-align: right; 
  color: #222;
  font-size:13px;
}
.DynarchCalendar-day-othermonth { color: #888; }
.DynarchCalendar-weekend { color: #c22; }
.DynarchCalendar-day-today { color: #090; font-weight: bold; }

.DynarchCalendar-day-disabled {
  opacity: 0.5;
}

.DynarchCalendar-hover-date {
  padding: 2px 3px;
  background-color: #eef;
  border: 1px solid #88c;
  margin: 0 !important;
  color: #000;
}

.DynarchCalendar-day-othermonth.DynarchCalendar-hover-date { border-color: #aaa; color: #888; }

.DynarchCalendar-dayNames .DynarchCalendar-weekend { color: #c22; }
.DynarchCalendar-day-othermonth.DynarchCalendar-weekend { color: #d88; }

.DynarchCalendar-day-selected {
  padding: 2px 3px;
  margin: 1px;
  background-color: #aaa;
  color: #000 !important;
}
.DynarchCalendar-day-today.DynarchCalendar-day-selected { background-color: #999; }

/* focus */

.DynarchCalendar-focusLink {
  position: absolute;
  opacity: 0;
  filter: alpha(opacity=0);
}

.DynarchCalendar-focused {
  border-color: #000;
}

.DynarchCalendar-focused .DynarchCalendar-topBar, .DynarchCalendar-focused .DynarchCalendar-bottomBar {
  background-color: #ccc;
  border-color: #336;
}

.DynarchCalendar-focused .DynarchCalendar-hover-week {
  background-color: #ccc;
}

.DynarchCalendar-tooltip {
  position: absolute;
  top: 100%;
  width: 100%;
}

.DynarchCalendar-tooltipCont {
  margin: 0 5px 0 5px;
  border: 1px solid #aaa;
  border-top: 0;
  padding: 3px 6px;
  background: #ddd;
}

.DynarchCalendar-focused .DynarchCalendar-tooltipCont {
  background: #ccc;
  border-color: #000;
}

@media print {
  .DynarchCalendar-day-selected {
    padding: 2px 3px;
    border: 1px solid #000;
    margin: 0 !important;
  }
}
