// Added by <PERSON> Ch<PERSON> [S]---------------------------------------------------------
// Date: 2016-04-20
//initializing the text for the form
function initFormText(obj, val, initVal) {
	if (obj.value == "" || obj.value == initVal) {
		obj.value = val;
	}
}

// resize the height of left panel
function resizePanelHeight(divId) {
	// alert("Calling resizePanelHeight();");
	var offsize = 120;
	var winHeight = getWinHeight();
	var h = winHeight - offsize;
	var tabCont = $("#" + divId);
	tabCont.css("height", h);
}

function getWinHeight() {
	var winHeight = 0;
	if (window.innerHeight) {
		winHeight = window.innerHeight;
	} else if ((document.body) && (document.body.clientHeight)) {
		winHeight = document.body.clientHeight;
	}
	return winHeight;
}

function getWinWidth() {
	// alert("Calling the getWinWidth()!");
	var winWidth = 0;
	if (window.innerWidth) {
		winWidth = window.innerWidth;
	} else if ((document.body) && (document.body.clientWidth)) {
		winWidth = document.body.clientWidth;
	}
	return winWidth;
}

// validating the input date
function dateValidation(dateStr) {
	// alert("Calling the dateValidation()!");
	// format for the string 'dateStr': yyyy-mm-dd
	var res = dateStr
			.match(/((^((1[8-9]\d{2})|([2-9]\d{3}))(-)(10|12|0?[13578])(-)(3[01]|[12][0-9]|0?[1-9])$)|(^((1[8-9]\d{2})|([2-9]\d{3}))(-)(11|0?[469])(-)(30|[12][0-9]|0?[1-9])$)|(^((1[8-9]\d{2})|([2-9]\d{3}))(-)(0?2)(-)(2[0-8]|1[0-9]|0?[1-9])$)|(^([2468][048]00)(-)(0?2)(-)(29)$)|(^([3579][26]00)(-)(0?2)(-)(29)$)|(^([1][89][0][48])(-)(0?2)(-)(29)$)|(^([2-9][0-9][0][48])(-)(0?2)(-)(29)$)|(^([1][89][2468][048])(-)(0?2)(-)(29)$)|(^([2-9][0-9][2468][048])(-)(0?2)(-)(29)$)|(^([1][89][13579][26])(-)(0?2)(-)(29)$)|(^([2-9][0-9][13579][26])(-)(0?2)(-)(29)$))/);
	if (res == null)
		return false;
	else
		return true;
}

// validating the input short-time
function timeValidation(timeStr) {
	// format for the string 'timeStr': hh:mm
	var res = timeStr.match(/^(\d{1,2})(:)?(\d{1,2})$/);
	if (res == null) {
		return false;
	}
	if (res[1] > 24 || res[3] > 60) {
		return false
	}
	return true;
}

// validating the input full-time
function fullTimeValidation(timeStr) {
	// format for the string 'timeStr': hh:mm:ss
	var res = timeStr.match(/^(\d{1,2})(:)?(\d{1,2})\2(\d{1,2})$/);
	if (res == null) {
		return false;
	}
	if (res[1] > 24 || res[3] > 60 || res[4] > 60) {
		return false
	}
	return true;
}

// Compare the input dates
// if the former was earlier than the later, return true.
function dateCompare(startDate, endDate) {
	// format for the string 'startDate' and 'endDate': yyyy-mm-dd hh:mm:ss
	if (startDate.length > 0 && endDate.length > 0) {
		// get the date
		var startDateTemp = startDate.split(" ");
		var endDateTemp = endDate.split(" ");

		var arrStartDate = startDateTemp[0].split("-");
		var arrEndDate = endDateTemp[0].split("-");

		// get the time
		var arrStartTime = startDateTemp[1].split(":");
		var arrEndTime = endDateTemp[1].split(":");

		var allStartDate = new Date(arrStartDate[0], arrStartDate[1],
				arrStartDate[2], arrStartTime[0], arrStartTime[1], 0);
		var allEndDate = new Date(arrEndDate[0], arrEndDate[1], arrEndDate[2],
				arrEndTime[0], arrEndTime[1], 0);

		if (allStartDate.getTime() >= allEndDate.getTime()) {
			return false;
		} else {
			return true;
		}
	} else {
		return false;
	}
}

function subArray(arr, s, e) {
	// alert("Calling the subArray()!");
	var len = arr.length;
	var min = s <= e ? s : e;
	var max = s > e ? s : e;
	if (min < 0 || max >= len) {
		alert("子数组区间[" + min + "," + max + "]不合法！");
		return false;
	}
	var res = [];
	for (var i = min; i <= max; i++) {
		res[i - min] = arr[i];
	}
	return res;
}

function getMin(arr) {
	var len = arr.length;
	var min = 999999999;
	for (var i = 0; i < len; i++) {
		min = min > arr[i] ? arr[i] : min;
	}
	return min;
}

function getMax(arr) {
	var len = arr.length;
	var max = -999999999;
	for (var i = 0; i < len; i++) {
		max = max < arr[i] ? arr[i] : max;
	}
	return max;
}

function getAzimuth(angle) {
	var ang = Math.ceil(angle / 45) * 45;
	// alert("Angle= " + ang);
	var azi = "";
	switch (ang) {
	case 0:
		azi = "正北";
		break;
	case 45:
		azi = "东北";
		break;
	case 90:
		azi = "正东";
		break;
	case 135:
		azi = "东南";
		break;
	case 180:
		azi = "正南";
		break;
	case 225:
		azi = "西南";
		break;
	case 270:
		azi = "正西";
		break;
	case 315:
		azi = "西北";
		break;
	default:
		azi = "正北";
	}
	return azi;
}

// 获取当前的x坐标值
function pageX(elem) {
	return elem.offsetParent ? (elem.offsetLeft + pageX(elem.offsetParent))
			: elem.offsetLeft;
}
// 获取当前的Y坐标值
function pageY(elem) {
	return elem.offsetParent ? (elem.offsetTop + pageY(elem.offsetParent))
			: elem.offsetTop;
}

function getPageX(elem) {
	return pageX(elem);
}
// 获取当前的Y坐标值
function getPageY(elem) {
	return pageY(elem);
}

// 获取DIV层的最大z-index值
function getZIndexMax() {
	var divs = document.getElementsByTagName("div");
	var max = 0;
	for (var i = 0; i < divs.length; i++) {
		max = Math.max(max, divs[i].style.zIndex || 0);
	}
	return max;
}
function parseParametersFromURL(pName) {
	var reg = new RegExp("(^|&)" + pName + "=([^&]*)(&|$)", "i");
	var r = window.location.search.substr(1).match(reg);
	if (r != null)
		return (r[2]);
	return null;
}

function encodeUTF8(str) {
	// alert("Calling the encodeUTF8()!");
	var temp = "", rs = "";
	for (var i = 0, len = str.length; i < len; i++) {
		temp = str.charCodeAt(i).toString(16);
		rs += "\\u" + new Array(5 - temp.length).join("0") + temp;
	}
	return rs;
}

function decodeUTF8(str) {
	// alert("Calling the decodeUTF8()!");
	return str.replace(/(\\u)(\w{4}|\w{2})/gi, function($0, $1, $2) {
		return String.fromCharCode(parseInt($2, 16));
	});
}

function getDateInterval(dateFormat1, dateFormat2) {
	var oDate1 = new Date(dateFormat1);
	var oDate2 = new Date(dateFormat2);
	// var oDate1 = new Date("2016-8-1 0:0:0");
	// var oDate2 = new Date("2016-8-2 10:30:0");
	// var oDate1 = new Date(2016, 8, 1, 0, 0, 0);
	// var oDate2 = new Date(2016, 8, 2, 10, 30, 0);
	var nTime = (oDate2.getTime() - oDate1.getTime()) / 1000;
	var day = Math.floor(nTime / 86400);
	var hour = Math.floor(nTime % 86400 / 3600);
	var minute = Math.floor(nTime % 86400 % 3600 / 60);
	var dateInterval = [];
	dateInterval[0] = day;
	dateInterval[1] = hour;
	dateInterval[2] = minute;
	// alert(day + "," + hour + "," + minute);
	return dateInterval;
}

// 获取控件左绝对位置
function getAbsoluteLeft(obj) {
	var oLeft = obj.offsetLeft;
	var oParent = null;
	while (obj.offsetParent != null) {
		oParent = obj.offsetParent;
		oLeft += oParent.offsetLeft;
		obj = oParent;
	}
	return oLeft;
}

// 获取控件上绝对位置
function getAbsoluteTop(obj) {
	var oTop = obj.offsetTop;
	var oParent = null;
	while (obj.offsetParent != null) {
		oParent = obj.offsetParent;
		oTop += oParent.offsetTop; // Add parent top position
		obj = oParent;
	}
	return oTop;
}

function delRowsFromTable(tableObj, isRemoveTh) {
	var tab = tableObj;
	var th;
	var rowNum = tab.rows.length;
	if (rowNum > 0) {
		th = tab.rows[0];
	}
	for (var i = 0; i < rowNum; i++) {
		tab.deleteRow(i);
		rowNum = rowNum - 1;
		i = i - 1;
	}
	if (!isRemoveTh) {
		tab.appendChild(th);
		// alert(tab.rows.length);
	}
}
// Added by Dawn Che [E]--------------------------------------------------------

// Added by Cao Xinliang [S] ---------------------------------------------------
function changeTimeToString(time) {
	var timeString = "";
	// 初始化时间
	var year = time.getFullYear();
	var month = time.getMonth() + 1;
	var day = time.getDate();
	var hour = time.getHours();
	var minute = time.getMinutes();
	var second = time.getSeconds();

	timeString = year + "-";
	if (month >= 10) {
		timeString = timeString + month + "-";
	} else {
		timeString = timeString + "0" + month + "-";
	}
	if (day >= 10) {
		timeString = timeString + day;
	} else {
		timeString = timeString + "0" + day;
	}
	if (hour >= 10) {
		timeString = timeString + " " + hour;
	} else {
		timeString = timeString + " 0" + hour;
	}
	if (minute >= 10) {
		timeString = timeString + ":" + minute;
	} else {
		timeString = timeString + ":0" + minute;
	}
	if (second >= 10) {
		timeString = timeString + ":" + second;
	} else {
		timeString = timeString + ":0" + second;
	}
	return timeString;
}

function getFormatDate() {
	var date = new Date();
	var seperator1 = "-";
	var seperator2 = ":";
	var month = date.getMonth() + 1;
	var strDate = date.getDate();
	if (month >= 1 && month <= 9) {
		month = "0" + month;
	}
	if (strDate >= 0 && strDate <= 9) {
		strDate = "0" + strDate;
	}
	var currentdate = date.getFullYear() + seperator1 + month + seperator1
			+ strDate + " " + date.getHours() + seperator2 + date.getMinutes()
			+ seperator2 + date.getSeconds();
	return currentdate;
}

// Added by Cao Xinliang [E] ---------------------------------------------------

// Added by Dawn [S] -----------------------------------------------------------
function openModal(url) {
	// 1. 创建遮罩层
    const overlay = document.createElement('div');
    overlay.style.position = 'fixed';
    overlay.style.top = '0';
    overlay.style.left = '0';
    overlay.style.width = '100%';
    overlay.style.height = '100%';
    overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
    overlay.style.zIndex = '1000';
    overlay.style.display = 'flex';
    overlay.style.justifyContent = 'center';
    overlay.style.alignItems = 'center';

    // 2. 创建可拖动和调整大小的模态容器
    const modal = document.createElement('div');
    modal.style.width = '80%';
    modal.style.height = '80%';
    modal.style.backgroundColor = 'white';
    modal.style.borderRadius = '8px';
    modal.style.position = 'absolute';
    modal.style.overflow = 'hidden';
    modal.style.resize = 'both';

    // 3. 创建顶部拖动条（30px高）
    const dragHandle = document.createElement('div');
    dragHandle.style.height = '30px';
    dragHandle.style.width = '100%';
    dragHandle.style.backgroundColor = '#EEE';
    dragHandle.style.cursor = 'move';
    dragHandle.style.position = 'absolute';
    dragHandle.style.top = '0';
    dragHandle.style.left = '0';
    dragHandle.style.zIndex = '10'; // 确保在iframe上方
    dragHandle.setAttribute('title', '点击拖动窗口');

    // 4. 创建 iframe
    const iframe = document.createElement('iframe');
    iframe.src = url;
    iframe.style.width = '100%';
    iframe.style.height = 'calc(100% - 30px)'; // 减去拖动条高度
    iframe.style.border = 'none';
    iframe.style.marginTop = '30px'; // 为拖动条留出空间
    iframe.setAttribute('allowfullscreen', 'true');
    iframe.setAttribute("sandbox","allow-scripts allow-same-origin allow-modals");

    // 5. 关闭按钮
    const closeButton = document.createElement('button');
    closeButton.textContent = '×';
    closeButton.setAttribute("title","点击关闭窗口");
    closeButton.setAttribute("style","color:#000");
    closeButton.style.position = 'absolute';
    closeButton.style.top = '4px';
    closeButton.style.right = '10px';
    closeButton.style.background = 'transparent';
    closeButton.style.border = 'none';
    closeButton.style.fontSize = '24px';
    closeButton.style.cursor = 'pointer';
    closeButton.style.zIndex = '11'; // 确保在拖动条上方

    // 拖动功能变量
    let isDragging = false;
    let offsetX, offsetY;

    // 鼠标在拖动条上按下 - 开始拖动
    dragHandle.addEventListener('mousedown', (e) => {
        isDragging = true;
        offsetX = e.clientX - modal.getBoundingClientRect().left;
        offsetY = e.clientY - modal.getBoundingClientRect().top;
        dragHandle.style.cursor = 'grabbing';
        e.preventDefault();
    });

    // 鼠标移动 - 处理拖动
    document.addEventListener('mousemove', (e) => {
        if (!isDragging) return;
        
        // 计算新位置（限制在视窗内）
        let x = e.clientX - offsetX;
        let y = e.clientY - offsetY;
        
        // 边界检查
        const modalRect = modal.getBoundingClientRect();
        const maxX = window.innerWidth - modalRect.width;
        const maxY = window.innerHeight - modalRect.height;
        
        x = Math.max(0, Math.min(x, maxX));
        y = Math.max(0, Math.min(y, maxY));
        
        // 应用新位置
        modal.style.left = `${x}px`;
        modal.style.top = `${y}px`;
    });

    // 鼠标释放 - 结束拖动
    document.addEventListener('mouseup', () => {
        if (isDragging) {
            isDragging = false;
            dragHandle.style.cursor = 'move';
        }
    });

    closeButton.onclick = () => document.body.removeChild(overlay);

    // 组装元素
    modal.appendChild(dragHandle);
    modal.appendChild(closeButton);
    modal.appendChild(iframe);
    overlay.appendChild(modal);
    document.body.appendChild(overlay);

    // 点击遮罩层关闭
    /*
    overlay.addEventListener('click', (e) => {
        if (e.target === overlay) {
            document.body.removeChild(overlay);
        }
    });
    */
}
