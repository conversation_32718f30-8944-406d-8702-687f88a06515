<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@taglib prefix="s" uri="/struts-tags"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>司机报表</title>
<link rel="stylesheet" type="text/css"
	href="/808FrontProject/css/user.css">

<!-- date component [S] -->
<script type="text/javascript"
	src="/808FrontProject/js/lib.jsdate/jscal2.js"></script>
<script type="text/javascript"
	src="/808FrontProject/js/lib.jsdate/en.js"></script>
<link rel="stylesheet" type="text/css"
	href="/808FrontProject/js/lib.jsdate/jscal2.css">
<link rel="stylesheet" type="text/css"
	href="/808FrontProject/js/lib.jsdate/border-radius.css">
<link rel="stylesheet" type="text/css"
	href="/808FrontProject/js/lib.jsdate/steel.css">
<!-- date component [E] -->

<script type="text/javascript" src="/808FrontProject/js/lib.jquery.js"></script>
<script type="text/javascript" src="/808FrontProject/js/tools.js"></script>

<!-- chart component [s] -->
<link rel="stylesheet" type="text/css"
	href="/808FrontProject/js/lib.mechart/chart.css">
<script src="/808FrontProject/js/lib.mechart/d3.min.js" charset="utf-8"></script>
<script src="/808FrontProject/js/lib.mechart/chart.js" charset="utf-8"></script>
<!-- chart component [e] -->

<script type="text/javascript">
	var y1 = [];
	var y2 = [];
	var x = [];

	$(window)
			.load(
					function() {

						//date componenent
						var cal = Calendar.setup({
							onSelect : function(cal) {
								cal.hide()
							},
							showTime : true
						});
						cal.manageFields("date1Btn", "searchDate1",
								"%Y-%m-%d %H:%M");
						cal.manageFields("date2Btn", "searchDate2",
								"%Y-%m-%d %H:%M");
						
						// Attaining the current date and initializing [S]
						var curDate = getFormatDate().substring(0, 10);
						document.getElementById("searchDate1").value = curDate
								+ " 00:00";
						document.getElementById("searchDate2").value = curDate
								+ " 23:59";
						// Attaining the current date and initializing [E]

						selectAllGroup();
						//select vehicle
						$("#selectGroup").change(
										function() {
											var groupName = $("#selectGroup")
													.val();
											if (groupName != "选择车队") {
												$.ajax({
															url : "/808FrontProject/common/selectVehicleAction",
															data : {
																"groupName" : groupName
															},
															type : "post",
															dataType : "json",
															success : function(
																	data) {
																var htmlContent = "<option>全部车辆</option>";
																for (var i = 0; i < data.length; i++) {
																	htmlContent += "<option>"
																			+ data[i].carNumber
																			+ "</option>";
																}

																$("#selectVehicle").html(htmlContent);
																$("#selectDriver").attr("disabled","disabled");
																$("#selectDriver").val("选择司机");
															}
														});
											}else{
												selectAllVehicle();
												$("#selectDriver").removeAttr("disabled");
											}
										});
						
						
						selectAllVehicle();
						$("#selectVehicle").change(function(){
						
							if($("#selectVehicle").val()!="全部车辆"){
								if($("#selectVehicle").val()!="选择车辆"){
									$("#selectDriver").attr("disabled","disabled");
									$("#selectDriver").val("选择司机");
								}else if($("#selectVehicle").val()=="选择车辆"){
									$("#selectDriver").removeAttr("disabled");
								}
							}
						
						}
						);
						
						selectAllDriver();
						
						$("#selectDriver").change(function(){
							if($("#selectDriver").val()!="选择司机"){
								$("#selectGroup").attr("disabled","disabled");
								$("#selectVehicle").attr("disabled","disabled");
								$("#selectGroup").val("选择车队");
								$("#selectVehicle").val("选择车辆");
							}else{
								$("#selectGroup").removeAttr("disabled");
								$("#selectVehicle").removeAttr("disabled");
							}
						});


					});
	
	//select All vehicle
	function selectAllVehicle(){
	$.ajax({
		url : "/808FrontProject/common/selectAllVehicleAction",
		type : "post",
		dataType : "json",
		success : function(data) {
			var htmlContent = "<option>选择车辆</option>";
			for (var i = 0; i < data.length; i++)
				htmlContent += "<option>" + data[i].carNumber
						+ "</option>";
			$("#selectVehicle").html(htmlContent);
		}
	});
	}
	
	//select All vehicleGroup
	function selectAllGroup(){
	$.ajax({
		url : "/808FrontProject/common/selectAllGroupAction",
		type : "post",
		dataType : "json",
		success : function(data) {
			var htmlContent = "<option>选择车队</option>";
			for (var i = 0; i < data.length; i++)
				htmlContent += "<option>" + data[i].name
						+ "</option>";
			$("#selectGroup").html(htmlContent);
		}
	});
	}
	
	//select All Driver
	function selectAllDriver(){
	$.ajax({
		url : "/808FrontProject/common/selectAllDriverAction",
		type : "post",
		dataType : "json",
		success : function(data) {
			var htmlContent = "<option>选择司机</option>";
			for (var i = 0; i < data.length; i++)
				htmlContent += "<option>" + data[i].driverName
						+ "</option>";
			$("#selectDriver").html(htmlContent);
		}
	});	
	}

	function getDriverReport() {

		var groupName = $("#selectGroup").val();
		var carNumber = $("#selectVehicle").val();
		var driverName=$("#selectDriver").val();
		var startTime = $("#searchDate1").val();
		var endTime = $("#searchDate2").val();

		if ((groupName == "选择车队"&&driverName=="选择司机")||(carNumber=="全部车辆"&&driverName=="选择司机")) {
			alert("至少选择车辆或者司机中的一项");
			return;
		}

		if (carNumber!= "全部车辆"&&carNumber!=null&&carNumber!="选择车辆") {
			$.ajax({
						url : "/808FrontProject/report/getDriverReportByCarNumber.action",
						data : {
							"carNumber" : carNumber,
							"startTime" : startTime,
							"endTime" : endTime
						},
						type : "post",
						dataType : "json",
						success : function(data) {
							if(data!=null){
								
								clearChartData();
								
								var htmlContent = '<tr class="thSize thColor"><td>编号</td><td>车牌号码</td>	<td>司机姓名</td><td>驾驶开始时间</td><td>结束时间</td><td>驾驶时长</td><td>驾驶里程</td></tr>';
								for (var i = 0; i < data.length; i++) {
									htmlContent += "<tr><td>" + (i + 1) + "</td>";
									htmlContent += "<td>" + data[i].carNumber+ "</td>";
									htmlContent += "<td>" + data[i].driverName+ "</td>";
									htmlContent += "<td>" + data[i].startTime+ "</td>";
									if(data[i].endTime!=null)
									    htmlContent += "<td>" + data[i].endTime + "</td>";
									else
									    htmlContent += "<td></td>";	
									htmlContent += "<td>" + data[i].driveTimeString+ "</td>";
									htmlContent += "<td>" + data[i].driveDistance+"</td></tr>";
								}
								$("#resultSpan").html(htmlContent);
								renderChart();
								
						   }
						}
					});
		} else if(driverName!="选择司机"){
			$.ajax({
				url : "/808FrontProject/report/getDriverReportByDriverName.action",
				data : {
					"driverName" : driverName,
					"startTime" : startTime,
					"endTime" : endTime
				},
				type : "post",
				dataType : "json",
				success : function(data) {
					if(data!=null){
					
					clearChartData();
					
					var htmlContent = '<tr class="thSize thColor"><td>编号</td><td>车牌号码</td>	<td>司机姓名</td><td>驾驶开始时间</td><td>结束时间</td><td>驾驶时长</td><td>驾驶里程</td></tr>';
					for (var i = 0; i < data.length; i++) {
						htmlContent += "<tr><td>" + (i + 1) + "</td>";
						htmlContent += "<td>" + data[i].carNumber+ "</td>";
						htmlContent += "<td>" + data[i].driverName+ "</td>";
						htmlContent += "<td>" + data[i].startTime+ "</td>";
						if(data[i].endTime!=null)
						    htmlContent += "<td>" + data[i].endTime + "</td>";
						else
						    htmlContent += "<td></td>";	
						htmlContent += "<td>" + data[i].driveTimeString+ "</td>";
						htmlContent += "<td>" + data[i].driveDistance+"</td></tr>";
					}
					$("#resultSpan").html(htmlContent);

					renderChart();
					
					}
				}
	});
			
		}

	}

	//绘制图表 [s] --------------------------------------------
	$(window).resize(function() {
		renderChart();
	});

	function renderChart() {
		var canvasId = "canvasId";
		var jqObj = $("#" + canvasId);
		var w = getWinWidth();
		var h = 200;
		var offset = 50;
		if (y1.length > 0 && y2.length > 0) {
			jqObj.height(h);
			jqObj.width(w - offset);
			var chart = new MapeyeChart(canvasId);
			chart.dualAxisLineDyn(y1, y2, x, "行驶里程", "最大速度", "里程 (km)",
					"速度 (km/h)", "序号");
		}
	}
	function clearChartData() {
		//alert("Calling the clearData()!");
		y1 = [];
		y2 = [];
		x = [];
		var obj=document.getElementById("canvasId");
		obj.innerHTML="暂无数据";
		obj.style.height="30px";
	}

	//绘制图表 [e] --------------------------------------------
</script>
</head>
<body>
	<span class="title1">司机报表</span>
	<table class="tableCommon">

		<tr>
			<td>选择车队 <select id="selectGroup"></select>
			&nbsp;&nbsp;选择车辆 <select id="selectVehicle"></select></td>
			<td  colspan=2>&nbsp;&nbsp;选择司机<select id="selectDriver"></select></td>
		</tr>
		<tr>
			<td>起始时间 <input type="text" id='searchDate1'
				title="时间格式：yyyy-mm-dd hh:mm" /> <input id="date1Btn" type="button"
				style="height: 25px; width: 30px;" value="..." />
			</td>

			<td>结束时间 <input type="text" id='searchDate2'
				title="时间格式：yyyy-mm-dd hh:mm" /> <input id="date2Btn" type="button"
				style="height: 25px; width: 30px;" value="..." />
			</td>
			<td style="padding-left: 0px; text-align: center; width: 10%"><input
				type="button" class="buttonStyle" value="查询"
				onclick="getDriverReport();" /></td>
		</tr>

	</table>
	

	<table class="tableCommon">
		<tr>
			<td><div id="canvasId">暂无记录</div></td>
		</tr>
	</table>

 
	<table class="tableCommon" id="resultSpan">
		<tr class="thSize thColor">
			<td>编号</td>
			<td>车牌号码</td>
			<td>司机姓名</td>
			<td>驾驶开始时间</td>
			<td>结束时间</td>
			<td>驾驶时长</td>
			<td>驾驶里程</td>
		</tr>
		<tr>
			<td colspan=11>暂无记录</td>
		</tr>
	</table>
</body>
</html>