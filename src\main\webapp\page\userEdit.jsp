<%@page import="cn.edu.ntu.entity.common.UserInfo"%>
<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@taglib prefix="s" uri="/struts-tags"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>编辑用户信息</title>
<link rel="stylesheet" type="text/css"
	href="/808FrontProject/css/user.css">

<script type="text/javascript" src="/808FrontProject/js/lib.jquery.js"></script>

<!-- treeview component -->
<link rel="stylesheet"
	href="/808FrontProject/js/lib.jtree/jquery.treeview.css" />
<link rel="stylesheet" href="/808FrontProject/js/lib.jtree/screen.css" />
<script src="/808FrontProject/js/lib.jtree/jquery.cookie.js"></script>
<script src="/808FrontProject/js/lib.jtree/jquery.treeview.js"></script>

<!-- dynatree component -->
<link rel='stylesheet' type='text/css'
	href='/808FrontProject/css/ui.dynatree.css'>
<script type="text/javascript" src='/808FrontProject/js/jquery-ui.js'></script>
<script type="text/javascript"
	src='/808FrontProject/js/jquery.cookie.js'></script>
<script type="text/javascript"
	src='/808FrontProject/js/jquery.dynatree.js'></script>
<script type="text/javascript"
	src="/808FrontProject/js/jquery-migrate-1.2.1.js"></script>


<script type="text/javascript">
	$(function() {
		/**
		$("#black, #gray").treeview({
			persist : "cookie",
			cookieId : "treeview-black"
		});
		
		$("#vehicleGroupTreeUl").treeview();
        */

		$("#vehicleGroupTree")
				.dynatree(
						{
							checkbox : true,
							selectMode : 2,
							initAjax : {
//								url : "/808FrontProject/common/showUserRelateGroupTreeAction",
                                url : "/808FrontProject/common/showGroupTreeAction",
								dataType : "json",
								type : "post"
							},
							onSelect : function(select, dtNode) {
								var selNodes = dtNode.tree.getSelectedNodes();
								var seltitles = $.map(selNodes, function(node) {
									return node.data.title;
								});
								var selTitleString = seltitles.join(",");
								$("#vehicleGroupName").val(selTitleString);

							}
						});

		setTimeout("selectVehicleGroup()", 2500);
		
		$("select[name='userInfo.roleName']").change(function(){
			var roleName=$("select[name='userInfo.roleName']").val();
			
			if(roleName=="超级管理员"){
				$("select[name='userInfo.createRoleAllowed']").val(1);
				$("select[name='userInfo.createRoleAllowed']").attr("disabled","disabled");
			}else if(roleName=="管理员"||roleName=="请选择"){
				$("select[name='userInfo.createRoleAllowed']").val(1);
				$("select[name='userInfo.createRoleAllowed']").removeAttr("disabled");
			}else {
				$("select[name='userInfo.createRoleAllowed']").val(0);
				$("select[name='userInfo.createRoleAllowed']").attr("disabled","disabled");
			}
		});
		
		if($("#userModifyFlag").val()==1){
			$("#userName").focus(function(){
				this.blur();
			});
			
		}else{
			$("#userName").blur(function(){
				validateUserName(this);
			});
		}

	});
	/**
	
	function buildTreeFromJson(data){
		var treeData=data[0];
		var treeContent='<ul id="vehicleGroupTreeUl" class="filetree">'+'<li><span class="folder">'+treeData.title+'</span></li>';
		if(treeData.children!=null){
			treeContent+='<ul>';
			for(var i=0;i<treeData.children.length;i++){
				treeContent+='<li><span class="folder">'+treeData.children[i].title+'</span></li>';
			}
			treeContent+='</ul>';
		}
		treeContent+='</ul>';
		$("#vehicleGroupTree").html(treeContent);
	}	
	
	
	*/
	
	function selectVehicleGroup() {

		var vehicleGroupName = $("#vehicleGroupName").val();

		if (vehicleGroupName != "") {
			var vehicleGroupSelections = vehicleGroupName.split(",");
			for (var i = 0; i < vehicleGroupSelections.length; i++) {
				$("#vehicleGroupTree").dynatree("getTree").selectKey(
						vehicleGroupSelections[i]);
			}
		}
	}
	
	function checkPassword(obj) {
		var pwd=$(obj).val();
		if (pwd.length < 8) {
			alert("请输入至少8位密码！");
		}
	}

	function store() {
		var uRole = $("select[name='userInfo.roleName']");
		if (uRole.find("option:selected").val() == 0) {
			alert("请选择用户角色！");
			uRole.focus();
			return false;
		}

		var uName = $("input[name='userInfo.name']");
		if (uName.val() == "") {
			alert("请输入用户名称！");
			uName.focus();
			return false;
		}

		var uStatus = $("select[name='userInfo.state']");
		if (uStatus.find("option:selected").val() == 0) {
			alert("请选择用户状态！");
			uStatus.focus();
			return false;
		}

		var uPass = $("input[name='userInfo.password']");
		var uCPass = $("input[name='comfirmedUserPassword']");
		if (uPass.val() == "") {
			alert("请输入密码！");
			uPass.focus();
			return false;
		}
		if (uCPass.val() == "") {
			alert("请输入确认密码！");
			uCPass.focus();
			return false;
		}
		/*
		if (uPass.val() != uCPass.val()) {
			alert("请确认后输入密码与前密码一致！");
			uCPass.val("");
			uCPass.focus();
			return false;
		}
		*/

		if ($("#userModifyFlag").val() == 0)
			$("#modifyUserInfoForm").attr("action",	"/808FrontProject/user/addStoreAction.action").submit();
		else
			$("#modifyUserInfoForm").attr("action",	"/808FrontProject/user/editStoreAction.action").submit();

	}
	
	function validateUserName(field){
		if($.trim(field.value)==""){
			$("#validationResultImage").attr("src","/808FrontProject/image/false.png");
			$("#storeButton").attr("disabled","disabled");
		}else{
			$.ajax({
				url:"/808FrontProject/common/validateUserNameAction",
			    data:{
			    	userName:$.trim(field.value)
			    },
			    type:"post",
			    dataType:"json",
			    success:function(data){
			    	if(data=="用户名已存在"){
						$("#validationResultImage").attr("src","/808FrontProject/image/false.png");
						$("#storeButton").attr("disabled","disabled");
					}else{
						$("#validationResultImage").attr("src","/808FrontProject/image/true.png");
						$("#storeButton").removeAttr("disabled");
					}
			    }
			
			});
		}
		
	}
	
	
</script>
</head>

<body>
	<span class="title1">用户信息管理</span>
	<s:form id="modifyUserInfoForm" theme="simple">
		<s:if test='null == userInfo.name'>
			<s:hidden id="userModifyFlag" value="0">
			</s:hidden>
		</s:if>
		<s:else>
			<s:hidden id="userModifyFlag" value="1">
			</s:hidden>
		</s:else>
		<s:hidden id="vehicleGroupName" name="userInfo.vehicleGroupName"></s:hidden>
		<table class="tableCommon">
			<tr>
				<td colspan="4" class="title2 thColor" id="userTag">
				    <s:if test="null == userInfo">
                                                                                   添加用户信息
                    </s:if> <s:else>
                                                                                   编辑用户信息
                    </s:else></td>
			</tr>
			<tr>
				<td>用户角色</td>
				<s:if test='"超级管理员"== userInfo.roleName'>
			        <td><s:select name="userInfo.roleName"
						list="#{1:'超级管理员',2:'管理员',3:'操作员' }" listKey="value"
						listValue="value" headerKey="0" headerValue="请选择" id="roleName"></s:select> *
				    </td>
		        </s:if>
		        <s:else>
		             <td><s:select name="userInfo.roleName"
						list="#{1:'管理员',2:'操作员' }" listKey="value"
						listValue="value" headerKey="0" headerValue="请选择" id="roleName"></s:select> *
				    </td>
         		</s:else>
				<td width="150px">用户名称</td>
				<td width="550px"><s:textfield name="userInfo.name" id="userName"/> *<img id="validationResultImage" 
						src="/808FrontProject/image/blank.png"></td>
			</tr>
			<!-- 
			<tr>
				<td width="150px">状态</td>
				<td><s:select name="userInfo.state"
						list="#{1:'正常',2:'暂停',3:'作废' }" listKey="value" listValue="value"
						headerKey="0" headerValue="请选择"></s:select> *</td>
			</tr>
			  -->
			<tr>
				<td>输入密码</td>
				<td><s:password name="userInfo.password" showPassword="true" onblur="checkPassword(this);" /> *</td>
				<td>确认密码</td>
				<td><s:password name="userInfo.confirmedPassword" showPassword="true" onblur="checkConfirmedPassword(this);" /> *</td>
			</tr>
			<tr>
			    <!--  
				<td>是否允许重复登录</td>
				<td><s:select name="userInfo.reloginAllowed"
						list="#{0:'否',1:'是' }" listKey="key" listValue="value"></s:select>
				</td>
				-->
				<td>是否允许创建角色</td>
				<td><s:select name="userInfo.createRoleAllowed"
						list="#{0:'否',1:'是' }" listKey="key" listValue="value" ></s:select>
				</td>
				<td>是否接收平台查岗</td>
				<td><s:select name="userInfo.inspectionAllowed"
						list="#{0:'否',1:'是' }" listKey="key" listValue="value"></s:select>
				</td>
			</tr>
			<!--  
			<tr>
				<td>报警声音开关</td>
				<td colspan="3"><s:select name="userInfo.alarmAllowed"
						list="#{0:'否',1:'是' }" listKey="key" listValue="value"></s:select>
					&nbsp;&nbsp;说明：此处设置进入页面时, 报警声音默认开启还是关闭, 每次进入页面时都会按照此设置控制报警声音</td>
			</tr>
			-->
			
			<tr>
			    <!-- 
				<td height="300px">用户功能</td>
				<td>
					<div id="level1AdministratorUserFunctionDiv"
						style="width: 100%; height: 300px; font-size: 14px; overflow-y: auto; line-height: 18px;">
						<ul id="black" class="treeview-black">
							<li><span>WEB功能菜单</span>
								<ul>
									<li class="closed"><span>用户管理</span>
										<ul>
											<li><span>用户信息管理</span></li>
										</ul></li>
									<li class="closed"><span>基础资料</span>
										<ul>
											<li><span>车辆信息管理</span></li>
											<li><span>车辆分组管理</span></li>
											<li><span>司乘人员管理</span></li>
											<li><span>数据导入</span></li>
											<li><span>图层管理</span></li>
										</ul></li>
									<li class="closed"><span>统计报表</span>
										<ul>
											<li><span>汽车行驶记录仪</span></li>
											<li><span>多媒体查询</span></li>
											<li class="closed"><span>报警事件</span>
												<ul>
													<li><span>报警查询</span></li>
													<li><span>报警统计</span></li>
													<li><span>事件明细</span></li>
													<li><span>超速报警详情统计(终端需支持超速恢复)</span></li>
													<li><span>超速分析统计(终端需支持超速恢复)</span></li>
													<li><span>超速报警明细(终端需支持超速恢复)</span></li>
												</ul></li>
											<li class="closed"><span>上下线情况</span>
												<ul>
													<li><span>车辆上线率统计</span></li>
													<li><span>不上线车辆查询</span></li>
													<li><span>车辆上下线查询</span></li>
													<li><span>当前车辆在线查询</span></li>
												</ul></li>
											<li class="closed"><span>轨迹分析</span>
												<ul>
													<li><span>轨迹连续性分析</span></li>
												</ul></li>
											<li class="closed"><span>车辆运营</span>
												<ul>
													<li><span>里程统计</span></li>
													<li><span>装卸料查询</span></li>
													<li><span>装卸料明细查询</span></li>
													<li><span>正反转查询</span></li>
													<li><span>正反转明细查询</span></li>
													<li><span>车辆日里程统计查询</span></li>
													<li><span>车辆停靠报表</span></li>
													<li><span>调度消息明细报表</span></li>
													<li><span>单车调度消息统计报表</span></li>
													<li><span>企业调度消息统计报表</span></li>
												</ul></li>
											<li class="closed"><span>用户报表</span>
												<ul>
													<li><span>用户在线时长统计</span></li>
													<li><span>用户在线情况查询</span></li>
												</ul></li>
										</ul></li>
									<li class="closed"><span>监控面板</span>
										<ul>
											<li class="closed"><span>工具栏</span>
												<ul>
													<li><span>拖动</span></li>
													<li><span>测距</span></li>
													<li><span>创建线路</span></li>
													<li><span>创建区域</span></li>
													<li><span>设置地图中心</span></li>
													<li><span>返回地图中心</span></li>
													<li><span>绘制圆形区域</span></li>
													<li><span>绘制多边形区域</span></li>
												</ul></li>
											<li class="closed"><span>控制命令</span>
												<ul>
													<li><span>点名</span></li>
													<li><span>拍照</span></li>
													<li><span>发送调度信息</span></li>
												</ul></li>
											<li class="closed"><span>监控命令</span>
												<ul>
													<li><span>定时监控</span></li>
													<li><span>定距监控</span></li>
													<li><span>临时监控</span></li>
												</ul></li>
											<li class="closed"><span>区域管理</span>
												<ul>
													<li><span>配置区域</span></li>
													<li><span>清除所有区域</span></li>
												</ul></li>
											<li class="closed"><span>线路管理</span>
												<ul>
													<li><span>配置线路</span></li>
													<li><span>清除所有线路</span></li>
												</ul></li>
											<li class="closed"><span>综合命令</span>
												<ul>
													<li><span>终端关机</span></li>
													<li><span>关闭所有无线通信</span></li>
													<li><span>发送监听命令</span></li>
													<li><span>透明传输</span></li>
													<li><span>终端RSA公钥设计</span></li>
													<li><span>平台RSA公钥设计</span></li>
													<li><span>信息服务下发</span></li>
													<li class="closed"><span>配置终端参数</span>
														<ul>
															<li class="closed"><span>基本参数设置</span>
																<ul>
																	<li><span>终端心跳发送间隔</span></li>
																	<li><span>TCP设置</span></li>
																	<li><span>UDP设置</span></li>
																	<li><span>SMS设置</span></li>
																	<li><span>服务器设置</span></li>
																	<li><span>位置汇报策略</span></li>
																	<li><span>位置汇报方案</span></li>
																	<li><span>休眠设置</span></li>
																	<li><span>驾驶员未登录设置</span></li>
																	<li><span>缺省时间汇报设置</span></li>
																	<li><span>拐点补传角度</span></li>
																	<li><span>电子围栏半径设置</span></li>
																	<li><span>车辆里程表读数</span></li>
																	<li><span>车辆所在的省域ID</span></li>
																	<li><span>车辆所在的市域ID</span></li>
																	<li><span>机动车号牌</span></li>
																	<li><span>车牌颜色</span></li>
																	<li><span>设置禁行时段</span></li>
																	<li><span>夜间行驶时间设置</span></li>
																	<li><span>夜间超速值预警百分比设置</span></li>
																</ul></li>
															<li class="closed"><span>报警参数设置</span>
																<ul>
																	<li><span>疲劳驾驶设置</span></li>
																	<li><span>路段限速设置</span></li>
																	<li><span>报警多媒体设置</span></li>
																	<li><span>紧急报警汇报设置</span></li>
																	<li><span>报警屏蔽字</span></li>
																	<li><span>最长停车时间</span></li>
																	<li><span>加油通知参数设置</span></li>
																</ul></li>
															<li class="closed"><span>终端电话设置</span>
																<ul>
																	<li><span>接听设置</span></li>
																	<li><span>通话时间设置</span></li>
																	<li><span>监控平台电话号码</span></li>
																	<li><span>复位电话号码</span></li>
																	<li><span>恢复出厂设置电话号码</span></li>
																	<li><span>监控平台SMS电话号码</span></li>
																	<li><span>接收终端SMS文本报警号码</span></li>
																	<li><span>监听电话号码</span></li>
																	<li><span>监管平台特权短信号码</span></li>
																</ul></li>
															<li class="closed"><span>视频设置</span>
																<ul>
																	<li><span>图像/视频质量</span></li>
																	<li><span>亮度</span></li>
																	<li><span>对比度</span></li>
																	<li><span>饱和度</span></li>
																	<li><span>色度</span></li>
																	<li><span>定时拍照时间间隔设置</span></li>
																	<li><span>定时拍照时间范围设置</span></li>
																</ul></li>
														</ul></li>
													<li><span>汽车行驶记录仪</span></li>
													<li><span>智能断油电</span></li>
													<li><span>立即断油电</span></li>
													<li><span>恢复油电路</span></li>
													<li><span>无线升级</span></li>
													<li><span>开门</span></li>
													<li><span>关门</span></li>
													<li><span>开锁</span></li>
													<li><span>关锁</span></li>
													<li><span>链路管理</span></li>
													<li><span>设置疲劳驾驶</span></li>
													<li><span>终端事件</span></li>
													<li><span>信息点播菜单设置</span></li>
													<li><span>问题信息</span></li>
													<li><span>电话本</span></li>
													<li><span>路段限速设置</span></li>
													<li><span>3G视频播放</span></li>
													<li><span>趟次/行程记录</span></li>
													<li><span>查询终端属性</span></li>
													<li><span>发送终端升级包命令</span></li>
													<li><span>禁行时段设置</span></li>
													<li><span>夜间限速设置</span></li>
												</ul></li>
											<li class="closed"><span>信息显示设置</span>
												<ul>
													<li><span>报警信息显示</span></li>
													<li><span>报警督办信息显示</span></li>
													<li><span>政府平台通知信息显示</span></li>
													<li><span>综合信息显示</span></li>
													<li><span>不在线车辆查询</span></li>
												</ul></li>
										</ul></li>
									<li class="closed"><span>车辆监控</span>
										<ul>
											<li><span>视频</span></li>
											<li><span>拍照</span></li>
											<li><span>实时轨迹</span></li>
											<li><span>历史轨迹</span></li>
											<li><span>点名</span></li>
										</ul></li>
								</ul></li>
						</ul>
					</div>
					<div id="level2AdministratorUserFunctionDiv"
						style="width: 100%; height: 300px; font-size: 14px; overflow-y: auto; line-height: 18px;">
						<ul id="black" class="treeview-black">
							<li><span>WEB功能菜单</span>
								<ul>
									<li class="closed"><span>基础资料</span>
										<ul>
											<li><span>车辆信息管理</span></li>
											<li><span>车辆分组管理</span></li>
										</ul></li>
									<li class="closed"><span>统计报表</span>
										<ul>
											<li><span>汽车行驶记录仪</span></li>
											<li class="closed"><span>报警事件</span>
												<ul>
													<li><span>报警查询</span></li>
												</ul></li>
											<li class="closed"><span>上下线情况</span>
												<ul>
													<li><span>车辆上线率统计</span></li>
												</ul></li>
											<li class="closed"><span>车辆运营</span>
												<ul>
													<li><span>里程统计</span></li>
												</ul></li>
										</ul></li>
									<li class="closed"><span>监控面板</span>
										<ul>
											<li class="closed"><span>工具栏</span>
												<ul>
													<li><span>拖动</span></li>
													<li><span>测距</span></li>
													<li><span>创建线路</span></li>
													<li><span>创建区域</span></li>
													<li><span>设置地图中心</span></li>
													<li><span>返回地图中心</span></li>
												</ul></li>
											<li class="closed"><span>控制命令</span>
												<ul>
													<li><span>点名</span></li>
													<li><span>拍照</span></li>
													<li><span>发送调度信息</span></li>
												</ul></li>
											<li class="closed"><span>监控命令</span>
												<ul>
													<li><span>定时监控</span></li>
													<li><span>定距监控</span></li>
												</ul></li>
											<li class="closed"><span>区域管理</span>
												<ul>
													<li><span>配置区域</span></li>
													<li><span>清除所有区域</span></li>
												</ul></li>
											<li class="closed"><span>线路管理</span>
												<ul>
													<li><span>配置线路</span></li>
													<li><span>清除所有线路</span></li>
												</ul></li>
										</ul></li>
								</ul></li>
						</ul>
					</div>
					<div id="operatorUserFunctionDiv"
						style="width: 100%; height: 300px; font-size: 14px; overflow-y: auto; line-height: 18px;">
						<ul id="black" class="treeview-black">
							<li><span>WEB功能菜单</span>
								<ul>
									<li class="closed"><span>基础资料</span>
										<ul>
											<li><span>司乘人员管理</span></li>
										</ul></li>
									<li class="closed"><span>统计报表</span>
										<ul>
											<li><span>汽车行驶记录仪</span></li>
											<li><span>多媒体查询</span></li>
											<li class="closed"><span>报警事件</span>
												<ul>
													<li><span>报警查询</span></li>
													<li><span>报警统计</span></li>
													<li><span>事件明细</span></li>
													<li><span>超速报警详情统计(终端需支持超速恢复)</span></li>
													<li><span>超速分析统计(终端需支持超速恢复)</span></li>
													<li><span>超速报警明细(终端需支持超速恢复)</span></li>
												</ul></li>
											<li class="closed"><span>车辆运营</span>
												<ul>
													<li><span>里程统计</span></li>
													<li><span>车辆日里程统计查询</span></li>
													<li><span>调度消息明细报表</span></li>
													<li><span>单车调度消息统计报表</span></li>
													<li><span>企业调度消息统计报表</span></li>
												</ul></li>
										</ul></li>
									<li class="closed"><span>监控面板</span>
										<ul>
											<li class="closed"><span>工具栏</span>
												<ul>
													<li><span>拖动</span></li>
													<li><span>测距</span></li>
													<li><span>创建线路</span></li>
													<li><span>创建区域</span></li>
													<li><span>设置地图中心</span></li>
													<li><span>返回地图中心</span></li>
													<li><span>绘制圆形区域</span></li>
													<li><span>绘制多边形区域</span></li>
												</ul></li>
											<li class="closed"><span>控制命令</span>
												<ul>
													<li><span>点名</span></li>
													<li><span>拍照</span></li>
													<li><span>发送调度信息</span></li>
												</ul></li>
											<li class="closed"><span>监控命令</span>
												<ul>
													<li><span>临时监控</span></li>
												</ul></li>
											<li class="closed"><span>区域管理</span>
												<ul>
													<li><span>配置区域</span></li>
													<li><span>清除所有区域</span></li>
												</ul></li>
											<li class="closed"><span>线路管理</span>
												<ul>
													<li><span>配置线路</span></li>
													<li><span>清除所有线路</span></li>
												</ul></li>
											<li class="closed"><span>综合命令</span>
												<ul>
													<li class="closed"><span>配置终端参数</span>
														<ul>
															<li class="closed"><span>报警参数设置</span>
																<ul>
																	<li><span>疲劳驾驶设置</span></li>
																	<li><span>路段限速设置</span></li>
																</ul></li>
														</ul></li>
													<li><span>汽车行驶记录仪</span></li>
													<li><span>路段限速设置</span></li>
													<li><span>禁行时段设置</span></li>
													<li><span>夜间限速设置</span></li>
												</ul></li>
											<li class="closed"><span>信息显示设置</span>
												<ul>
													<li><span>报警信息显示</span></li>
													<li><span>政府平台通知信息显示</span></li>
													<li><span>综合信息显示</span></li>
													<li><span>不在线车辆查询</span></li>
												</ul></li>
										</ul></li>
								</ul></li>
						</ul>
					</div>
				</td>
				-->
				<td >绑定车辆组</td>
				<td colspan=3>
					<div id="vehicleGroupTree"
						style="width: 100%; height: 300px; font-size: 14px; overflow-y: auto; line-height: 22px;">
					</div>
				</td>
			</tr>
			<tr>
				<td height="35px">备注</td>
				<td colspan="3"><s:textfield name="userInfo.note"
						style="width:530px;" /></td>
			</tr>
			<tr>
				<td colspan="4" style="padding-left: 0px; text-align: center;">
					<input class="buttonStyle" type="button" id="storeButton" value="保存"
					onclick="store();" />&nbsp; <input class="buttonStyle"
					type="button" value="返回" onclick="history.go(-1);" />
				</td>
			</tr>
		</table>
	</s:form>

</body>
</html>