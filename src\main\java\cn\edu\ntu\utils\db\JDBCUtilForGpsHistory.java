package cn.edu.ntu.utils.db;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Properties;
import javax.sql.DataSource;
import org.apache.commons.dbcp2.BasicDataSourceFactory;

import cn.edu.ntu.utils.others.ConstantUtils;

// Modified by <PERSON>
// Date: 2016-4-19

public class JDBCUtilForGpsHistory {

	private static DataSource ds;
	private static String databaseName;
	private static Connection conn;

	public static void setDatabase(String databaseName) {
		JDBCUtilForGpsHistory.databaseName = databaseName;
	}

	public static Connection getConn() {
			Properties p = new Properties();
			try {
				p.load(JDBCUtilForGpsHistory.class.getClassLoader().getResourceAsStream("datasource.properties"));
				String databaseIp=ConstantUtils.getStringConstant("databaseIp");
				String url = "jdbc:mysql://"+databaseIp+":3306"+ "/" + databaseName+"?useUnicode=true&characterEncoding=utf8";
				p.setProperty("url", url);
				ds = BasicDataSourceFactory.createDataSource(p);
				conn = ds.getConnection();
			} catch (Exception e) {
				e.printStackTrace();
			}
		return conn;
	}

	public static void close(ResultSet rs, PreparedStatement pstmt, Connection conn) {
		if (null != rs) {
			try {
				rs.close();
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
		if (null != pstmt) {
			try {
				pstmt.close();
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
		if (null != conn) {
			try {
				conn.close();
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
	}

	public static void close() {
		try {
			conn.close();
			conn = null;
		} catch (SQLException e) {
			e.printStackTrace();
		}
	}
}
