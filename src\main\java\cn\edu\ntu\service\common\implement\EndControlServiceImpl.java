package cn.edu.ntu.service.common.implement;

import java.nio.ByteBuffer;
import java.nio.charset.Charset;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.edu.ntu.dao.common.implement.VehicleInfoDaoImpl;
import cn.edu.ntu.dao.common.interfaces.AreaSettingDao;
import cn.edu.ntu.dao.common.interfaces.EndControlDao;
import cn.edu.ntu.dao.common.interfaces.VehicleInfoDao;
import cn.edu.ntu.entity.common.AreaSetting;
import cn.edu.ntu.entity.common.CommandInfo;
import cn.edu.ntu.entity.common.PolygonAreaSetting;
import cn.edu.ntu.entity.common.RectangleAreaSetting;
import cn.edu.ntu.entity.common.RoundAreaSetting;
import cn.edu.ntu.entity.common.ShotCommandParams;
import cn.edu.ntu.entity.common.ShotSetting;
import cn.edu.ntu.entity.common.StoreShotSettingResult;
import cn.edu.ntu.entity.common.VehicleInfo;
import cn.edu.ntu.service.common.interfaces.CommandInfoService;
import cn.edu.ntu.service.common.interfaces.EndControlService;
import cn.edu.ntu.service.common.interfaces.ShotSettingRedisService;
import cn.edu.ntu.utils.others.CommunicateWithPlatformUtil;
import javassist.tools.framedump;

/**
 *
 * 
 * <AUTHOR>
 * @date 2016��10��26�� ����9:58:30 
 */
@Service
public class EndControlServiceImpl implements EndControlService{
	
	@Autowired
	private CommandInfoService commandInfoService;
	@Autowired
	private ShotSettingRedisService shotSettingRedisService;
	
	@Autowired
	private VehicleInfoDao vehicleInfoDao;
	@Autowired
	private EndControlDao endControlDao;
	@Autowired
	private AreaSettingDao areaSettingDao;
	

	@Override
	public  CommandInfo setReportInterval(int reportInterval,int reportLastTime, String carNumber){
		String simCardString=getSimcardByCarNumber(carNumber);
		int commandSerialNumber=commandInfoService.getCommandSerialNumber(simCardString);
		byte[] bytes=buildSetReportIntervalCommand(reportInterval,reportLastTime,simCardString,commandSerialNumber+1);
		CommandInfo commandInfo=new CommandInfo();
		if(CommunicateWithPlatformUtil.sendSocketPackage(bytes)){
			commandInfoService.addCommand(simCardString,commandSerialNumber+1);
			commandInfo.setSimCardString(simCardString);
			commandInfo.setCommandSerialNumber(commandSerialNumber+1);
		}
		return commandInfo;
	}
	
	@Override
	public CommandInfo positionEnquire(String carNumber) {
		String simCardString=getSimcardByCarNumber(carNumber);
		int commandSerialNumber=commandInfoService.getCommandSerialNumber(simCardString);
		byte[] bytes=buildPositionEnquireCommand(simCardString,commandSerialNumber+1);
		CommandInfo commandInfo=new CommandInfo();
		if(CommunicateWithPlatformUtil.sendSocketPackage(bytes)){
			commandInfoService.addCommand(simCardString,commandSerialNumber+1);
			commandInfo.setSimCardString(simCardString);
			commandInfo.setCommandSerialNumber(commandSerialNumber+1);
		}
		return commandInfo;
	}
	
	@Override
	public  CommandInfo textMessageDelivery(String carNumber, String textMessage) {
		String simCardString=getSimcardByCarNumber(carNumber);
		int commandSerialNumber=commandInfoService.getCommandSerialNumber(simCardString);
		byte[] bytes=buildTextMessageDeliveryCommand(simCardString,commandSerialNumber+1,textMessage);
		CommandInfo commandInfo=new CommandInfo();
		if(CommunicateWithPlatformUtil.sendSocketPackage(bytes)){
			commandInfoService.addCommand(simCardString, commandSerialNumber+1);
			commandInfo.setSimCardString(simCardString);
			commandInfo.setCommandSerialNumber(commandSerialNumber+1);
		}
		return commandInfo;
	}
	
	@Override
	public CommandInfo immediatelyPhoto(String carNumber,ShotCommandParams params) {
		
		List<ShotSetting> shotSettingList = shotSettingRedisService.list();
		for (ShotSetting shotSetting : shotSettingList) {
			if (shotSetting.getCarNumber().equals(carNumber)) {
				if (shotSetting.getStartTime().equals("") && shotSetting.getShotInterval() != 0) {
					if (params.getShotInterval() != 0 && params.getShotNumber() != 0) {
						int nextShotInterval = params.getShotInterval() * params.getShotNumber();
						shotSetting.setNextRunInterval(nextShotInterval);
					} else {
						shotSetting.setNextRunInterval(-9999);
					}
					shotSetting.setRunningIntervalStatus(false);
					shotSetting.setImmediatelyRunIntervalStatus(true);
					break;
				}
			}
		}
		if(shotSettingList.size()>0)
		    shotSettingRedisService.reStore(shotSettingList);
		
		String simCardString=getSimcardByCarNumber(carNumber);
	    int commandSerialNumber=commandInfoService.getCommandSerialNumber(simCardString);
	    byte[] bytes=buildImmediatelyPhotoCommand(simCardString,commandSerialNumber+1,params);
	    CommandInfo commandInfo=new CommandInfo();
	    if(CommunicateWithPlatformUtil.sendSocketPackage(bytes)){
	    	commandInfoService.addCommand(simCardString, commandSerialNumber+1);
	    	commandInfo.setSimCardString(simCardString);
	    	commandInfo.setCommandSerialNumber(commandSerialNumber+1);
	    }
	    return commandInfo;
	}
	
	@Override
	public StoreShotSettingResult fixedIntervalPhoto(String carNumber, int accStatus, int shotInterval) {
		
		
		boolean booleanResult= endControlDao.storeFixedIntervalPhotoSetting(carNumber,accStatus,shotInterval);
		StoreShotSettingResult storeShotSettingResult=new StoreShotSettingResult();
		storeShotSettingResult.setCarNumber(carNumber);
		int result;
		if(booleanResult)
			result=1;
		else
			result=0;
		storeShotSettingResult.setResult(result);
		return storeShotSettingResult;
		
	}
	
	@Override
	public void fixedTimePhoto(String carNumber, String startTimeString) {
		
		endControlDao.storeFixedTimePhotoSetting(carNumber,startTimeString);
	}

	
	@Override
	public void nFixedTimePhoto(String carNumbers, String startTimeString) {
		String[] carNumberArray = carNumbers.split(",");

		for (int i = 0; i < carNumberArray.length; i++)
			fixedTimePhoto(carNumberArray[i], startTimeString);

	}
	
	@Override
	public List<StoreShotSettingResult> nFixedIntervalPhoto(String carNumbers, int accStatus, int shotInterval) {
		
		String[] carNumberArray = carNumbers.split(",");

		List<StoreShotSettingResult> settingResultList=new ArrayList<StoreShotSettingResult>();
		
		for (int i = 0; i < carNumberArray.length; i++){
			StoreShotSettingResult storeShotSettingResult=fixedIntervalPhoto(carNumberArray[i], accStatus, shotInterval);
		    settingResultList.add(storeShotSettingResult);
		}
		
		return settingResultList;
	}
	
	@Override
	public List<ShotSetting> queryFixedTimePhotoSetting(String userName) {
		
		List<VehicleInfo> vehicleInfoList=vehicleInfoDao.listVehiclesByUserName(userName);
		
		List<ShotSetting> allShotSettingList=shotSettingRedisService.list();
		
        List<ShotSetting> shotSettingList=new ArrayList<ShotSetting>();		
		for(VehicleInfo vehicleInfo:vehicleInfoList){
			for(ShotSetting shotSetting:allShotSettingList){
				if(shotSetting!=null&&vehicleInfo.getCarNumber().equals(shotSetting.getCarNumber())){
					if(!shotSetting.getStartTime().equals("")&&shotSetting.getShotInterval()==0){
						shotSettingList.add(shotSetting);
					}
				}
			}
			
		}
		
		return shotSettingList;
	}
	
	@Override
	public List<ShotSetting> queryFixedIntervalPhotoSetting(String userName) {

        List<VehicleInfo> vehicleInfoList=vehicleInfoDao.listVehiclesByUserName(userName);
		
		List<ShotSetting> allShotSettingList=shotSettingRedisService.list();
		
        List<ShotSetting> shotSettingList=new ArrayList<ShotSetting>();		
		for(VehicleInfo vehicleInfo:vehicleInfoList){
			for(ShotSetting shotSetting:allShotSettingList){
				if(vehicleInfo.getCarNumber().equals(shotSetting.getCarNumber())){
					if(shotSetting.getStartTime().equals("")&&shotSetting.getShotInterval()!=0&&shotSetting.getNextRunInterval()!=-99999){
						shotSettingList.add(shotSetting);
					}
				}
			}
			
		}
		
		return shotSettingList;
	}


	@Override
	public void deleteFixedTimeShotSetting(String selectedItems) {
		
		String[] shotSettingItemArray=selectedItems.split(",");
		
		List<ShotSetting> list=shotSettingRedisService.list();
		
		for(String shotSettingItem:shotSettingItemArray){
			String[] shotSettingArray=shotSettingItem.split("_");
			ShotSetting deleteShotSetting=new ShotSetting();
			deleteShotSetting.setCarNumber(shotSettingArray[0]);
			deleteShotSetting.setStartTime(shotSettingArray[1]);
			
			for (ShotSetting shotSetting : list) {
				if (!shotSetting.getStartTime().equals("") && shotSetting.getShotInterval() == 0) {
					if (shotSetting.getCarNumber().equals(deleteShotSetting.getCarNumber())
							&& shotSetting.getStartTime().equals(deleteShotSetting.getStartTime())) {
						shotSettingRedisService.remove(shotSetting);
						break;
					}
				}
			}
			
		}
		
	}
	
	@Override
	public void deleteFixedIntervalShotSetting(String selectedItems) {
		String[] shotSettingItemArray=selectedItems.split(",");
		
		List<ShotSetting> list=shotSettingRedisService.list();
		
		for(String shotSettingItem:shotSettingItemArray){
			String[] shotSettingArray=shotSettingItem.split("_");
			ShotSetting deleteShotSetting=new ShotSetting();
			deleteShotSetting.setCarNumber(shotSettingArray[0]);
			deleteShotSetting.setStartTime(shotSettingArray[1]);
			
			for (ShotSetting shotSetting : list) {
				if (shotSetting.getStartTime().equals("") && shotSetting.getShotInterval() != 0) {
					if (shotSetting.getCarNumber().equals(deleteShotSetting.getCarNumber())) {
						shotSetting.setRunningIntervalStatus(true);
						shotSetting.setImmediatelyRunIntervalStatus(false);
						shotSetting.setNextRunInterval(-99999);
						break;
					}
				}
			}
			
		}
		
		shotSettingRedisService.reStore(list);
		
	}
	
	@Override
	public CommandInfo terminalReset(String carNumber) {
		String simCardString=getSimcardByCarNumber(carNumber);
		int commandSerialNumber=commandInfoService.getCommandSerialNumber(simCardString);
		byte[] bytes=buildTerminalResetCommand(commandSerialNumber+1,simCardString);
		CommandInfo commandInfo=new CommandInfo();
		if(CommunicateWithPlatformUtil.sendSocketPackage(bytes)){
			commandInfoService.addCommand(simCardString, commandSerialNumber+1);
			commandInfo.setSimCardString(simCardString);
			commandInfo.setCommandSerialNumber(commandSerialNumber+1);
		}
		return commandInfo;
	}
	
	@Override
	public  CommandInfo[] nPositionEnquire(String carNumbers) {
		
		String[] carNumberArray=carNumbers.split(",");
		
		CommandInfo[] commandInfos=new CommandInfo[carNumberArray.length];

		for(int i=0;i<carNumberArray.length;i++)
			commandInfos[i]=positionEnquire(carNumberArray[i]);
		
		return commandInfos;
		
	}
	
	@Override
	public  CommandInfo[] nImmediatelyPhoto(String carNumbers, ShotCommandParams params) {
        String[] carNumberArray=carNumbers.split(",");
        
		CommandInfo[] commandInfos=new CommandInfo[carNumberArray.length];

		for(int i=0;i<carNumberArray.length;i++)
			commandInfos[i]=immediatelyPhoto(carNumberArray[i],params);
		
		return commandInfos;
	}

	
	
	private static byte[] buildSetReportIntervalCommand(int reportInterval, int reportLastTime, String simCardString,int commandSerialNumber) {
		byte[] bytes=new byte[15];
		bytes[0]=0x7e;
		bytes[1]=0x09;
		bytes[2]=0x01;
		byte[] simCardBytes=convertSimCardString2Bytes(simCardString);
		System.arraycopy(simCardBytes, 0, bytes, 3, 6);
		bytes[9]=(byte)reportInterval;
		bytes[10]=(byte)((reportLastTime&0xff00)>>8);
		bytes[11]=(byte)(reportLastTime&0x00ff);
		bytes[12]=(byte)((commandSerialNumber&0xff00)>>8);
		bytes[13]=(byte)(commandSerialNumber&0x00ff);
		bytes[14]=0x7e;
		return bytes;
	}
	
    private static byte[] buildPositionEnquireCommand(String simCardString,int commandSerialNumber) {
		
		byte[] bytes=new byte[12];
		bytes[0]=0x7e;
		bytes[1]=0x09;
		bytes[2]=0x02;
		byte[] simCardBytes=convertSimCardString2Bytes(simCardString);
		System.arraycopy(simCardBytes, 0, bytes, 3, 6);
		bytes[9]=(byte)((commandSerialNumber&0xff00)>>8);
		bytes[10]=(byte)(commandSerialNumber&0x00ff);
		bytes[11]=0x7e;
		return bytes;
	}
    
	private static byte[] buildTextMessageDeliveryCommand(String simCardString,int commandSerialNumber,String textMessage) {
		
		ByteBuffer byteBuffer = Charset.forName("gbk").encode(textMessage);
	    byte [] textMessageBytes = byteBuffer.array() ;
	    int length=textMessageBytes.length;
	    
        byte[] bytes=new byte[12+length];
		bytes[0]=0x7e;
		bytes[1]=0x09;
		bytes[2]=0x03;
		byte[] simCardBytes=convertSimCardString2Bytes(simCardString);
		System.arraycopy(simCardBytes, 0, bytes, 3, 6);
		bytes[9]=(byte) ((commandSerialNumber&0xff00)>>8);
		bytes[10]=(byte) (commandSerialNumber&0x00ff);
		System.arraycopy(textMessageBytes,0, bytes, 11, length);
		bytes[11+length]=0x7e;
		return bytes;
	}
	
	private static byte[] buildImmediatelyPhotoCommand(String simCardString, int commandSerialNumber,ShotCommandParams params) {
		byte[] bytes=new byte[17];
		bytes[0]=0x7e;
		bytes[1]=0x09;
		bytes[2]=0x04;
		byte[] simCardBytes=convertSimCardString2Bytes(simCardString);
		System.arraycopy(simCardBytes, 0, bytes, 3, 6);
		bytes[9]=(byte)((commandSerialNumber&0xff00)>>8);
		bytes[10]=(byte)(commandSerialNumber&0x00ff);
		bytes[11]=(byte)params.getChannelId();
		bytes[12]=(byte)((params.getShotNumber()&0xff00)>>8);
		bytes[13]=(byte)(params.getShotNumber()&0x00ff);
		bytes[14]=(byte)((params.getShotInterval()&0xff00)>>8);
		bytes[15]=(byte)(params.getShotInterval()&0x00ff);
		bytes[16]=0x7e;
		return bytes;
	}

	private static byte[] buildTerminalResetCommand(int commandSerialNumber, String simCardString) {
		byte[] bytes=new byte[12];
		bytes[0]=0x7e;
		bytes[1]=0x09;
		bytes[2]=0x0d;
		byte[] simCardBytes=convertSimCardString2Bytes(simCardString);
		System.arraycopy(simCardBytes, 0,bytes, 3, 6);
		bytes[9]=(byte)((commandSerialNumber&0xff00)>>8);
		bytes[10]=(byte)(commandSerialNumber&0x00ff);
		bytes[11]=0x7e;
		return bytes;
	}
    
    private static byte[] convertSimCardString2Bytes(String simCardString){
		byte[] bytes=new byte[6];
		String startNumberString=simCardString.substring(0,1);
		byte startNumber=(byte) Integer.parseInt(startNumberString,16);
		bytes[0]=startNumber;
		for(int i=0;i<5;i++){
			String numberString=simCardString.substring(1+2*i,3+2*i);
			byte number=(byte)Integer.parseInt(numberString, 16);
			bytes[1+i]=number;
		}
		return bytes;
	}
	
	private  String getSimcardByCarNumber(String carNumber){
	    VehicleInfo vehicleInfo=vehicleInfoDao.getVehicleInfoByCarNumber(carNumber);
		return vehicleInfo.getSimCardNumber();
	}

	@Override
	public List<AreaSetting> queryFenceSetting(String name) {
		
		List<AreaSetting> areaSettingList=new ArrayList<AreaSetting>();
		List<RoundAreaSetting> roundAreaSettingList=areaSettingDao.queryRoundAreaSettingListByUserName(name);
		List<RectangleAreaSetting> rectangleAreaSettingList=areaSettingDao.queryRectanglAreaSettingListByUserName(name);
		List<PolygonAreaSetting> polygonAreaSettingList=areaSettingDao.queryPolygonAreaSettingListByUserName(name);
		areaSettingList.addAll(roundAreaSettingList);
		areaSettingList.addAll(rectangleAreaSettingList);
		areaSettingList.addAll(polygonAreaSettingList);
		return areaSettingList;
	}

	@Override
	public void bindFenceSetting(String carNumber, String areaName, int geometryType) {

		if(geometryType==1){
			RoundAreaSetting setting=areaSettingDao.queryRoundAreaSettingByAreaName(areaName);
			
			VehicleInfo vehicleInfo=vehicleInfoDao.getVehicleInfoByCarNumber(carNumber);
			
			byte[] bytes=buildBindRoundFenceSettingBytes(vehicleInfo.getSimCardNumber(),setting);
			
			CommunicateWithPlatformUtil.sendSocketPackage(bytes);
		}else if(geometryType==2){
			RectangleAreaSetting setting=areaSettingDao.queryRectangleAreaSettingByAreaName(areaName);
			
			VehicleInfo vehicleInfo=vehicleInfoDao.getVehicleInfoByCarNumber(carNumber);
			
			byte[] bytes=buildBindRectangleFenceSettingBytes(vehicleInfo.getSimCardNumber(),setting);
			
			CommunicateWithPlatformUtil.sendSocketPackage(bytes);
		}else if(geometryType==3){
			PolygonAreaSetting setting=areaSettingDao.queryPolygonAreaSettingListByAreaName(areaName);
			
			VehicleInfo vehicleInfo=vehicleInfoDao.getVehicleInfoByCarNumber(carNumber);
			
			byte[] bytes=buildBindPolygonFenceSettingBytes(vehicleInfo.getSimCardNumber(),setting);
			
			CommunicateWithPlatformUtil.sendSocketPackage(bytes);
		}
	}

	private byte[] buildBindPolygonFenceSettingBytes(String simCardNumber, PolygonAreaSetting setting) {
		byte[] bytes=new byte[33+8*setting.getNumberOfVertex()];
		bytes[0]=0x7e;
		bytes[1]=0x09;
		bytes[2]=0x12;
		byte[] simCardNumberArray=convertSimCardString2Bytes(simCardNumber);
		System.arraycopy(simCardNumberArray, 0, bytes, 3, 6);
		bytes[9]=(byte) ((setting.getAreaId()&0xff000000)>>24);
		bytes[10]=(byte)((setting.getAreaId()&0x00ff0000)>>16);
		bytes[11]=(byte)((setting.getAreaId()&0x0000ff00)>>8);
		bytes[12]=(byte)((setting.getAreaId()&0x000000ff));
		if(setting.getAreaType()==1){
			bytes[13]=11;
			bytes[14]=0;
		}
		else if(setting.getAreaType()==2){
			bytes[13]=35;
			bytes[14]=0;
		}
		byte[] startTimeBytes=convertDate2Bytes(setting.getStartTime());
		System.arraycopy(startTimeBytes, 0, bytes, 15, 6);
		byte[] endTimeBytes=convertDate2Bytes(setting.getEndTime());
		System.arraycopy(endTimeBytes, 0, bytes, 21, 6);
		bytes[27]=(byte) ((setting.getMaxSpeed()&0xff00)>>8);
		bytes[28]=(byte) (setting.getMaxSpeed()&0x00ff);
		bytes[29]=(byte) setting.getLastTimeOverSpeed();
		bytes[30]=(byte) ((setting.getNumberOfVertex()&0xff00)>>8);
		bytes[31]=(byte) (setting.getNumberOfVertex()&0x00ff);
		for(int i=0;i<setting.getNumberOfVertex();i++){
			float lat=setting.getLatArr().get(i);
			int latInteger=(int) (lat*1000000);
			bytes[32+8*i]=(byte) ((latInteger&0xff000000)>>24);
			bytes[33+8*i]=(byte)((latInteger&0x00ff0000)>>16);
			bytes[34+8*i]=(byte)((latInteger&0x0000ff00)>>8);
			bytes[35+8*i]=(byte)((latInteger&0x000000ff));
			float lng=setting.getLonArr().get(i);
			int lngInteger=(int)(lng*1000000);
			bytes[36+8*i]=(byte) ((lngInteger&0xff000000)>>24);
			bytes[37+8*i]=(byte) ((lngInteger&0x00ff0000)>>16);
			bytes[38+8*i]=(byte)((lngInteger&0x0000ff00)>>8);
			bytes[39+8*i]=(byte)(lngInteger&0x000000ff);
		}
		bytes[32+8*setting.getNumberOfVertex()]=0x7e;
		
		return bytes;
	}

	private byte[] buildBindRectangleFenceSettingBytes(String simCardNumber, RectangleAreaSetting setting) {
		byte[] bytes=new byte[49];
		bytes[0]=0x7e;
		bytes[1]=0x09;
		bytes[2]=0x12;
		byte[] simCardNumberArray=convertSimCardString2Bytes(simCardNumber);
		System.arraycopy(simCardNumberArray, 0, bytes, 3, 6);
		bytes[9]=0x01;
		bytes[10]=0x01;
		bytes[11]=(byte) ((setting.getAreaId()&0xff000000)>>24);
		bytes[12]=(byte)((setting.getAreaId()&0x00ff0000)>>16);
		bytes[13]=(byte)((setting.getAreaId()&0x0000ff00)>>8);
		bytes[14]=(byte)((setting.getAreaId()&0x000000ff));
		if(setting.getAreaType()==1){
			bytes[15]=11;
			bytes[16]=0;
		}
		else if(setting.getAreaType()==2){
			bytes[15]=35;
			bytes[16]=0;
		}
		int latOnLeftAndTopInteger=(int) (setting.getLatOnLeftAndTop()*1000000);
		bytes[17]=(byte) ((latOnLeftAndTopInteger&0xff000000)>>24);
		bytes[18]=(byte) ((latOnLeftAndTopInteger&0x00ff0000)>>16);
		bytes[19]=(byte) ((latOnLeftAndTopInteger&0x0000ff00)>>8);
		bytes[20]=(byte) (latOnLeftAndTopInteger&0x000000ff);
		int lngOnLeftAndTopInteger=(int) (setting.getLngOnLeftAndTop()*1000000);
		bytes[21]=(byte) ((lngOnLeftAndTopInteger&0xff000000)>>24);
		bytes[22]=(byte) ((lngOnLeftAndTopInteger&0x00ff0000)>>16);
		bytes[23]=(byte) ((lngOnLeftAndTopInteger&0x0000ff00)>>8);
		bytes[24]=(byte) (lngOnLeftAndTopInteger&0x000000ff);
		int latOnRightAndBottomInteger=(int) (setting.getLatOnRightAndBottom()*1000000);
		bytes[25]=(byte) ((latOnRightAndBottomInteger&0xff000000)>>24);
		bytes[26]=(byte) ((latOnRightAndBottomInteger&0x00ff0000)>>16);
		bytes[27]=(byte) ((latOnRightAndBottomInteger&0x0000ff00)>>8);
		bytes[28]=(byte) (latOnRightAndBottomInteger&0x000000ff);
		int lngOnRightAndBottomInteger=(int) (setting.getLngOnRightAndBottom()*1000000);
		bytes[29]=(byte) ((lngOnRightAndBottomInteger&0xff000000)>>24);
		bytes[30]=(byte) ((lngOnRightAndBottomInteger&0x00ff0000)>>16);
		bytes[31]=(byte) ((lngOnRightAndBottomInteger&0x0000ff00)>>8);
		bytes[32]=(byte) (lngOnRightAndBottomInteger&0x000000ff);
		byte[] startTimeBytes=convertDate2Bytes(setting.getStartTime());
		System.arraycopy(startTimeBytes, 0, bytes, 33, 6);
		byte[] endTimeBytes=convertDate2Bytes(setting.getEndTime());
		System.arraycopy(endTimeBytes, 0, bytes, 39, 6);
		bytes[45]=(byte) ((setting.getMaxSpeed()&0xff00)>>8);
		bytes[46]=(byte) (setting.getMaxSpeed()&0x00ff);
		bytes[47]=(byte) setting.getLastTimeOverSpeed();
		bytes[48]=0x7e;
		
		return bytes;
	}

	private byte[] buildBindRoundFenceSettingBytes(String simCardNumber, RoundAreaSetting setting) {
		byte[] bytes=new byte[45];
		bytes[0]=0x7e;
		bytes[1]=0x09;
		bytes[2]=0x10;
		byte[] simCardNumberArray=convertSimCardString2Bytes(simCardNumber);
		System.arraycopy(simCardNumberArray, 0, bytes, 3, 6);
		bytes[9]=0x01;
		bytes[10]=0x01;
		bytes[11]=(byte) ((setting.getAreaId()&0xff000000)>>24);
		bytes[12]=(byte)((setting.getAreaId()&0x00ff0000)>>16);
		bytes[13]=(byte)((setting.getAreaId()&0x0000ff00)>>8);
		bytes[14]=(byte)((setting.getAreaId()&0x000000ff));
		if(setting.getAreaType()==1){
			bytes[15]=11;
			bytes[16]=0;
		}
		else if(setting.getAreaType()==2){
			bytes[15]=35;
			bytes[16]=0;
		}
		int centerLatInterger=(int) (setting.getCenterLat()*1000000);
		bytes[17]=(byte) ((centerLatInterger&0xff000000)>>24);
		bytes[18]=(byte) ((centerLatInterger&0x00ff0000)>>16);
		bytes[19]=(byte) ((centerLatInterger&0x0000ff00)>>8);
		bytes[20]=(byte) (centerLatInterger&0x000000ff);
		int centerLngInterger=(int)(setting.getCenterLng()*1000000);
		bytes[21]=(byte) ((centerLngInterger&0xff000000)>>24);
		bytes[22]=(byte) ((centerLngInterger&0x00ff0000)>>16);
		bytes[23]=(byte) ((centerLngInterger&0x0000ff00)>>8);
		bytes[24]=(byte) (centerLngInterger&0x000000ff);
		bytes[25]=(byte) ((setting.getRadius()&0xff000000)>>24);
		bytes[26]=(byte) ((setting.getRadius()&0x00ff0000)>>16);
		bytes[27]=(byte) ((setting.getRadius()&0x0000ff00)>>8);
		bytes[28]=(byte) ((setting.getRadius()&0x000000ff));
	    byte[] startTimeBytes=convertDate2Bytes(setting.getStartTime());
		System.arraycopy(startTimeBytes, 0, bytes, 29, 6);
		byte[] endTimeBytes=convertDate2Bytes(setting.getEndTime());
		System.arraycopy(endTimeBytes, 0, bytes, 35, 6);
		bytes[41]=(byte) ((setting.getMaxSpeed()&0xff00)>>8);
		bytes[42]=(byte) (setting.getMaxSpeed()&0x00ff);
		bytes[43]=(byte) setting.getLastTimeOverSpeed();
		bytes[44]=0x7e;
		
		return bytes;
	}

	private byte[] convertDate2Bytes(Date time) {
		
		SimpleDateFormat simpleDateFormat=new SimpleDateFormat("yy-MM-dd-HH-mm-ss");
		String timeString=simpleDateFormat.format(time);
		String[] timeStringBytes=timeString.split("-");
		byte[] timeBytes=new byte[6];
		for(int i=0;i<6;i++){
			timeBytes[i]=(byte) Integer.parseInt(timeStringBytes[i], 16);
		}
		
		return timeBytes;
	}

	@Override
	public void unbindFenceSetting(String carNumber, String areaName, int geometryType) {
		if(geometryType==1){
			RoundAreaSetting setting=areaSettingDao.queryRoundAreaSettingByAreaName(areaName);
			
			VehicleInfo vehicleInfo=vehicleInfoDao.getVehicleInfoByCarNumber(carNumber);
			
			byte[] bytes=buildUnbindRoundFenceSettingBytes(vehicleInfo.getSimCardNumber(),setting);
			
			CommunicateWithPlatformUtil.sendSocketPackage(bytes);
		}else if(geometryType==2){
			RectangleAreaSetting setting=areaSettingDao.queryRectangleAreaSettingByAreaName(areaName);
			
			VehicleInfo vehicleInfo=vehicleInfoDao.getVehicleInfoByCarNumber(carNumber);
			
			byte[] bytes=buildUnbindRectangleFenceSettingBytes(vehicleInfo.getSimCardNumber(),setting);
			
			CommunicateWithPlatformUtil.sendSocketPackage(bytes);
		}else if(geometryType==3){
			PolygonAreaSetting setting=areaSettingDao.queryPolygonAreaSettingListByAreaName(areaName);
			
			VehicleInfo vehicleInfo=vehicleInfoDao.getVehicleInfoByCarNumber(carNumber);
			
			byte[] bytes=buildUnbindPolygonFenceSettingBytes(vehicleInfo.getSimCardNumber(),setting);
			
			CommunicateWithPlatformUtil.sendSocketPackage(bytes);
		}
	}

	private byte[] buildUnbindPolygonFenceSettingBytes(String simCardNumber, PolygonAreaSetting setting) {

		byte[] bytes=new byte[16];
		bytes[0]=0x7e;
		bytes[1]=0x09;
		bytes[2]=0x15;
		byte[] simcardNumberArray=convertSimCardString2Bytes(simCardNumber);
		System.arraycopy(simcardNumberArray, 0, bytes, 3, 6);
		bytes[10]=1;
		bytes[11]=(byte) ((setting.getAreaId()&0xff000000)>>24);
		bytes[12]=(byte) (((setting.getAreaId())&0x00ff0000)>>16);
		bytes[13]=(byte) ((setting.getAreaId()&0x0000ff00)>>8);
		bytes[14]=(byte) (setting.getAreaId()&0x000000ff);
		bytes[15]=0x7e;
		return bytes;
	}

	private byte[] buildUnbindRectangleFenceSettingBytes(String simCardNumber, RectangleAreaSetting setting) {
		byte[] bytes=new byte[16];
		bytes[0]=0x7e;
		bytes[1]=0x09;
		bytes[2]=0x13;
		byte[] simcardNumberArray=convertSimCardString2Bytes(simCardNumber);
		System.arraycopy(simcardNumberArray, 0, bytes, 3, 6);
		bytes[10]=1;
		bytes[11]=(byte) ((setting.getAreaId()&0xff000000)>>24);
		bytes[12]=(byte) (((setting.getAreaId())&0x00ff0000)>>16);
		bytes[13]=(byte) ((setting.getAreaId()&0x0000ff00)>>8);
		bytes[14]=(byte) (setting.getAreaId()&0x000000ff);
		bytes[15]=0x7e;
		return bytes;
	}

	private byte[] buildUnbindRoundFenceSettingBytes(String simCardNumber, RoundAreaSetting setting) {
		byte[] bytes=new byte[16];
		bytes[0]=0x7e;
		bytes[1]=0x09;
		bytes[2]=0x11;
		byte[] simcardNumberArray=convertSimCardString2Bytes(simCardNumber);
		System.arraycopy(simcardNumberArray, 0, bytes, 3, 6);
		bytes[10]=1;
		bytes[11]=(byte) ((setting.getAreaId()&0xff000000)>>24);
		bytes[12]=(byte) (((setting.getAreaId())&0x00ff0000)>>16);
		bytes[13]=(byte) ((setting.getAreaId()&0x0000ff00)>>8);
		bytes[14]=(byte) (setting.getAreaId()&0x000000ff);
		bytes[15]=0x7e;
		return bytes;
	}

	
}
