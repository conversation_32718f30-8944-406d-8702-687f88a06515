log4j.rootLogger  = info , mainAppender , console

log4j.logger.com.danga.MemCached = info 

log4j.appender.mainAppender  =  org.apache.log4j.RollingFileAppender
log4j.appender.mainAppender.File =  logs/808FrontProject.log
log4j.appender.mainAppender.Append = TRUE
log4j.appender.mainAppender.MaxFileSize = 4096KB
log4j.appender.mainAppender.MaxBackupIndex = 100
log4j.appender.mainAppender.Threshold = info
log4j.appender.mainAppender.layout  =  org.apache.log4j.PatternLayout
log4j.appender.mainAppender.layout.ConversionPattern = %d{yyyy-MM-dd HH:mm:ss.SSS}|[%t]|%p|%c{1}|%m%n

log4j.appender.console=org.apache.log4j.ConsoleAppender
log4j.appender.console.Target=System.out
log4j.appender.console.layout=org.apache.log4j.PatternLayout
log4j.appender.console.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss} %-5p [%t] %c %x - %m%n


