<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@taglib prefix="s" uri="/struts-tags"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">

<title>太平洋北斗位置云</title>
<meta http-equiv="pragma" content="no-cache">
<meta http-equiv="cache-control" content="no-cache">
<meta http-equiv="expires" content="0">
<meta http-equiv="keywords" content="太平洋,北斗,位置服务,云服务,车辆定位,车辆查询,车辆导航">
<meta http-equiv="description" content="太平洋北斗位置云">


<link rel="stylesheet" href="/808FrontProject/css/frame.css"
	type="text/css">
<link rel="stylesheet" href="/808FrontProject/css/submenu.css"
	type=text/css>
<link rel="stylesheet" type="text/css"
	href="/808FrontProject/css/searchres.css">

<script type="text/javascript" src="/808FrontProject/js/lib.jquery.js"></script>
<script type="text/javascript" src="/808FrontProject/js/lib.json2.js"></script>

<script type="text/javascript"
	src="http://api.map.baidu.com/api?v=2.0&ak=6ukSP6o4ff8u9SSnlVEKmiZC"></script>


<script type="text/javascript" src="/808FrontProject/js/frame.js"></script>
<script type="text/javascript" src="/808FrontProject/js/style.js"></script>
<script type="text/javascript" src="/808FrontProject/js/var.js"></script>

<script type="text/javascript" src="/808FrontProject/js/tools.js"></script>
<script type="text/javascript" src="/808FrontProject/js/baidumap.js"></script>

<script type="text/javascript" src="/808FrontProject/js/nav.js"></script>
<script type="text/javascript" src="/808FrontProject/js/hashMap.js"></script>
<script type="text/javascript" src="/808FrontProject/js/cookie.js"></script>
<script type="text/javascript" src="/808FrontProject/js/track.js"></script>
<script type="text/javascript" src="/808FrontProject/js/endParams.js"></script>
<script type="text/javascript" src="/808FrontProject/js/endControl.js"></script>
<script type="text/javascript"
	src="/808FrontProject/js/commandResult.js"></script>

<!-- date component [S] -->
<script type="text/javascript"
	src="/808FrontProject/js/lib.jsdate/jscal2.js"></script>
<script type="text/javascript"
	src="/808FrontProject/js/lib.jsdate/en.js"></script>
<link rel="stylesheet" type="text/css"
	href="/808FrontProject/js/lib.jsdate/jscal2.css">
<link rel="stylesheet" type="text/css"
	href="/808FrontProject/js/lib.jsdate/border-radius.css">
<link rel="stylesheet" type="text/css"
	href="/808FrontProject/js/lib.jsdate/steel.css">
<!-- date component [E] -->

<!-- treeview component [S] -->
<link rel="stylesheet"
	href="/808FrontProject/js/lib.jtree/jquery.treeview.css" />
<link rel="stylesheet" href="/808FrontProject/js/lib.jtree/screen.css" />
<script src="/808FrontProject/js/lib.jtree/jquery.cookie.js"></script>
<script src="/808FrontProject/js/lib.jtree/jquery.treeview.js"></script>
<!-- treeview component [E] -->

<!-- context menu [s] -->
<script type="text/javascript" src="/808FrontProject/js/shortcut.js"></script>
<link rel="stylesheet" href="/808FrontProject/css/shortcut.css"
	type="text/css">
<!-- context menu [e] -->

<!-- dynamic win component [s] -->
<link rel="stylesheet" type="text/css"
	href="/808FrontProject/js/lib.mewin/win.css">
<script type="text/javascript"
	src="/808FrontProject/js/lib.mewin/winfull.js"></script>
<!-- dynamic win component [e] -->

<!-- dynamic img component [s] -->
<link rel="stylesheet" type="text/css"
	href="/808FrontProject/js/lib.meimg/img.css">
<script type="text/javascript"
	src="/808FrontProject/js/lib.meimg/img.js"></script>
<!-- dynamic win component [e] -->

<!-- interactive dialog [s] -->
<link rel="stylesheet"
	href="/808FrontProject/js/lib.artDialog/ui-dialog.css" />
<script src="/808FrontProject/js/lib.artDialog/dialog-plus-min.js"></script>
<!-- interactive dialog [e] -->

<!-- dynamic table component [s] -->
<link rel="stylesheet" type="text/css"
	href="/808FrontProject/js/lib.metable/table.css">
<script type="text/javascript"
	src="/808FrontProject/js/lib.metable/table.js" charset="utf-8"></script>
<!-- dynamic table component [e] -->

<style type="text/css">
body {
	font-family: 宋体;
}

input {
	font-size: 14px;
}

.thColor {
	background-color: #ACD6FF;
}

.commonFontSize {
	font-size: 14px;
}

#rightFrameCanvas {
	display: none;
}

.thStyle {
	font-weight: bold;
	background-color: #ACD6FF;
	height: 25px;
	font-size: 13px;
	text-align: center;
}

#posDiv {
	position: absolute;
	top: 0px;
	right: 0px;
	height: 0px;
	width: 0px;
	background-color: #fff;
	z-index: 1;
	font-size: 14px;
	line-height: 30px;
	padding-left: 5px;
}

#cmdTable td {
	text-align: center
}

#multiMediaTable td {
	text-align: center
}

#alertDiv {
	position: absolute;
	bottom: 0px;
	right: 0px;
	height: 0px;
	width: 0px;
	z-index: 1;
	font-size: 14px;
	line-height: 25px;
	padding-left: 5px;
}

.tableStyle {
	border-collapse: collapse;
	border-spacing: 0px;
	font-size: 13px;
	text-align: center;
	border-color: #666;
}

.warningInfoSpanDivStyle {
	height: 112px;
	width: 100%;
	overflow: auto;
}

#warningInfoSpanDiv a:hover {
	color: #666666;
	text-decoration: underline;
}

#warningInfoSpanDiv a:link {
	color: #000;
	text-decoration: none;
}

#warningInfoSpanDiv a:visited {
	color: #000;
	text-decoration: none;
}
</style>
<script type="text/javascript">
	var mewin;
	var mapnotewin;
	var mapMain;
	var monitorTable;
	//var warnTable;

	//counter for time 
	function runTimer() {
		var maxTime = 10;//sec
		timerIdGV = window.setInterval(function() {
			if (maxTime > 0) {
				$("#posDiv").text(maxTime + "秒后刷新");
				--maxTime;
			} else {
				maxTime = 10;
				refresh();
			}
		}, 1000);
	}

	function killTimer() {
		if ("undefined" != typeof timerIdGV) {
			window.clearInterval(timerIdGV);
		}
		$("#posDiv").text("秒后刷新");
	}
	
	function refresh() {

		multiAjax(monitoredArray, "refresh");
		
	}

	function showPosDiv() {
		$("#posDiv").show();// 轨迹
		$("#posDiv").css("height", "30px");
		$("#posDiv").css("width", "90px");
	}

	function showAlertDiv() {
		$("#alertDiv").show();// 轨迹
		$("#alertDiv").css("height", "25px");
		$("#alertDiv").css("width", "100px");
	}

	$(window).load(function() {
		document.getElementById("rightFrameCanvas").style.display = "block";
		initShortcut();

		//loading mywin [s] ----------------
		mewin = new MapeyeWin("myWin", "winName", "winForm");
		mewin.init();
		mewin.setImgPath("/808FrontProject/js/");
		//loading mywin [e] ----------------

		mapnotewin = new MapeyeWin("mapNote", "noteName", "noteForm");
		mapnotewin.init();
		mapnotewin.setImgPath("/808FrontProject/js/");

		mapMain = new BaiduMap("mapMain");
		mapMain.init();
		mapMain.locationByBrowser();

		//屏蔽鼠标右键
		$(document).bind("contextmenu", function(e) {
			return false;
		});

		runTimer();
		runRefreshPhoto();
		showPosDiv();
		showAlertDiv();

		showAllTree();

		initDivWidthInNoteForm();

		monitorTable = new MapeyeTable("monitorInfoSpanDiv", "monitorTable");
		monitorTable.setInitColWidth("original");
		monitorTable.setWordCompress(false);
		//moinotrTable.setWidthOffset(7);
		monitorTable.init();
		monitorTable.setImgPath("/808FrontProject/js/");

		/**
		warnTable = new MapeyeTable("warningInfoSpanDiv", "warnTable");
		warnTable.setInitColWidth("original");
		warnTable.setWordCompress(false);
		warnTable.init();
		warnTable.setImgPath("/808FrontProject/js/");
		 **/

	});

	$(window).resize(function() {
		//adjust the layout of mewin
		mewin.resizeWinLayout();
		mapnotewin.resizeWinLayout();

		initDivWidthInNoteForm();
		monitorTable.resizeTable();
		//warnTable.resizeTable();
	});

	function pageAdd(name, url) {
		var p = $("#menuParameterL1").val();
		//参数p为打开新页面要传入的参数，传入前必须进行UTF8编码，传入后必须进行UTF8解码
		var code = "<iframe src='"
				+ url
				+ "?p="
				+ encodeUTF8(p)
				+ "' width=100% height=100%  frameborder=0  border=2><\/iframe>";
		mewin.winAdd(name + "_" + p, code);
	}

	//当显示群发命令结果至页面
	function pagesAdd(name, url) {
		var p = $("#menuParameterL2").val();
		var code = "<iframe src='"
				+ url
				+ "?p="
				+ encodeUTF8(p)
				+ "' width=100% height=100%  frameborder=0  border=2><\/iframe>";
		mewin.winAdd(name + "_" + p, code);
	}

	function efencePageAdd(name, url) {
		var code = "<iframe src='"
				+ url
				+ "' width=100% height=100%  frameborder=0  border=2><\/iframe>";
		mewin.winAdd(name, code);
	}

	//submenu function [s] ---------------------------------------
	function displaySubMenu(li) {
		var subMenu = li.getElementsByTagName("ul")[0];
		if (typeof (subMenu) != "undefined") {
			subMenu.style.display = "block";
			subMenu.style.zIndex = 2;
		}
	}

	function hideSubMenu(li) {
		var subMenu = li.getElementsByTagName("ul")[0];
		if (typeof (subMenu) != "undefined") {
			subMenu.style.display = "none";
		}
	}
	//submenu function [e] ---------------------------------------

	//unload event
	$(window).unload(function() {
		document.getElementById("rightFrameCanvas").style.display = "none";
	});
</script>

</head>

<body>
	<div class="topFrame" id="topFrame">
		<!-- Top Frame [S] -->
		<table width=100% height="55px" style="border-collapse: collapse;"
			border=0 class="commonFontSize">
			<tr>
				<td style="text-align: left; width: 350px"><img height="50px"
					width="350px" src="/808FrontProject/image/tpy.jpg"></td>
				<td style="text-align: right">
					<ul id="navigation" style="right: 0px">
						<li onmouseover="displaySubMenu(this)"
							onmouseout="hideSubMenu(this)"><a href="#"
							onClick="efencePageAdd('电子围栏','/808FrontProject/page/electricFence.jsp')">电子围栏</a></li>
						<s:if test='userInfo!=null&userInfo.roleName!="操作员"'>
							<li onmouseover="displaySubMenu(this)"
								onmouseout="hideSubMenu(this)"><a href="#">信息管理</a>
								<ul>
									<li><a href="/808FrontProject/user/listAction.action"
										target="blank">用户管理</a></li>
									<li><a href="/808FrontProject/vehicle/listAction.action"
										target="blank">车辆信息</a></li>
									<li><a
										href="/808FrontProject/vehicleGroup/listAction.action"
										target="blank">车辆分组</a></li>
									<li><a href="/808FrontProject/driver/listAction.action"
										target="blank">司机信息管理</a></li>

								</ul></li>
						</s:if>
						<li onmouseover="displaySubMenu(this)"
							onmouseout="hideSubMenu(this)"><a href="#">统计报表</a>
							<ul>
								<li><a href="/808FrontProject/page/reportDrive.jsp"
									target="blank">行驶报表</a></li>
								<li><a href="/808FrontProject/page/reportMileage.jsp"
									target="blank">里程报表</a></li>
								<li><a href="/808FrontProject/page/reportVelocity.jsp"
									target="blank">速度报表</a></li>
								<!--  	<li><a href="/808FrontProject/page/reportIgnition.jsp"
									target="blank">点火报表</a></li>
						-->
								<li><a href="/808FrontProject/page/reportPark.jsp"
									target="blank">停车报表</a></li>
								<li><a href="/808FrontProject/page/reportWarning.jsp"
									target="blank">报警报表</a></li>
								<li><a href="/808FrontProject/page/reportDriver.jsp"
									target="blank">司机报表</a></li>
							</ul></li>
						<!-- 					<li onmouseover="displaySubMenu(this)"
							onmouseout="hideSubMenu(this)"><a
							href="/808FrontProject/page/userInfo.jsp" target="blank">个人资料</a></li>
	 -->
						<li onmouseover="displaySubMenu(this)"
							onmouseout="hideSubMenu(this)"><a
							href="/808FrontProject/user/loginOutAction.action">安全退出</a></li>
					</ul>
				</td>
			</tr>
		</table>
		<!-- Top Frame [E] -->
	</div>

	<!-- Shortcut Menu [s] -->
	<div id="shortcutMenuL1" class="shortcutMenu">
		<div>群发操作 >></div>
		<div
			onclick="pageAdd('车辆信息','/808FrontProject/page/showVehicleInfo.jsp')">车辆信息</div>
		<div onClick="tempMonitor();">临时跟踪</div>
		<div
			onClick="pageAdd('重点监控','/808FrontProject/page/focusMonitor.jsp')">重点监控</div>
		<div
			onClick="pageAdd('历史轨迹','/808FrontProject/page/trackHistory.jsp')">历史轨迹</div>
		<div onclick="positionEnquire()">车辆点名</div>
		<div onclick="photoCapture()">拍照监控</div>
		<div onclick="photoQuery()">拍照查询</div>
		<div onclick="photoSetting()">拍照设置</div>
		<div onclick="videoMonitor()">视频监控</div>
		<div onclick="videoHuifang()">视频回放</div>
		<div onclick="fenceSetting()">围栏绑定</div>
		<div>配置终端 >></div>
		<div>终端控制 >></div>
		<div onclick="textDelivery()">发送调度</div>
		<input type="hidden" id="menuParameterL1" /> <input type="hidden"
			id="menuParameterL2" />
	</div>
	<div id="shortcutMenuL2_QF" class="shortcutMenu">
		<!-- 群发命令 -->
		<div onclick="nTempMonitor();">临时跟踪</div>
		<div onclick="nPositionEnquire();">车辆点名</div>
		<div onClick="nPhotoCapture();">拍照监控</div>
	</div>
	<div id="shortcutMenuL2_ZD" class="shortcutMenu">
		<!-- 配置终端 -->
		<div onclick="serverParam()">服务器参数设置</div>
		<div onclick="icCardAuthenParam()">道路运输证IC卡认证</div>
		<div onClick="posParam()">位置汇报参数设置</div>
		<div onClick="terminalParam()">终端电话参数设置</div>
		<div onClick="overspeedParam()">超速参数设置</div>
		<div onClick="fatigueDrivingParam()">疲劳驾驶参数设置</div>
		<div onClick="licenseParam()">车牌号码参数设置</div>
	</div>
	<div id="shortcutMenuL2_ZK" class="shortcutMenu">
		<!-- 终端控制 -->
		<div onclick="terminalReset()">终端复位</div>
	</div>

	<!-- Shortcut Menu [e] -->

	<div class="pageShowCanvas" id="index_main">
		<!-- Main Frame [S] -->
		<div id="leftFrameCanvas">
			<!-- Left Frame [S]-->
			<table style="padding: 2px; width: 100%; height: 100%">
				<s:if test="userInfo!=null">
					<tr>
						<td height="30px">
							<form style="margin-top: 2px; width: 100%; min-height: 30px;">
								<select id="keyWordType" style="width: 60px; font-size: 13px">
									<option>车牌</option>
									<option>SIM卡</option>
									<!-- 
								<option>驾驶员</option>
								 -->
									<option>车组</option>
								</select> <input type='text' id='searchText'
									onFocus='initFormText(this,"","请输入...")'
									onBlur='initFormText(this,"请输入...","请输入...")'
									style='width: 140px; font-size: 13px' value='请输入...' />
							</form>
						</td>
						<td><img id="searchImg" src='../image/search-s.png'
							style='width: 20px; height: 20px; cursor: pointer; padding: 1px; margin-top: 0px; vertical-align: middle;'
							onmouseover="styleImgMOver(this,'/808FrontProject/image/search-sh.png','点击查找')"
							onmouseout="styleImgMOut(this,'/808FrontProject/image/search-s.png')"
							onclick="showSearchedTree();" /> <img
							src="/808FrontProject/image/refreshd.png"
							style="width: 18px; height: 20px; vertical-align: middle; cursor: pointer"
							onmouseover="styleImgMOver(this,'/808FrontProject/image/refreshh.png','手动刷新')"
							onmouseout="styleImgMOut(this,'/808FrontProject/image/refreshd.png')"
							onclick="showAllTree();" /></td>
					</tr>
				</s:if>
				<tr>
					<td colspan="2">
						<div
							style="width: 100%; height: 100%; overflow: auto; margin-bottom: 20px; border: 0px solid #F00">
							<div id="vehicleList" style="vertical-align: top"></div>
						</div>
					</td>
				</tr>
			</table>
			<!-- Left Frame [S]-->
			
		</div>

		<div class="middle_shousuo">
			<!--Middle Frame [S]-->
			<a href="#" class="open_right" id="open_right" style="display: none;"
				onClick="openleft();" title="显示控制面板"></a> <a href="javascript:;"
				onClick="closeleft();" class="open_left" id="open_left"
				title="隐藏控制面板"></a>
			<!--Middle Frame [E]-->
		</div>


		<div id="rightFrameCanvas">
			<!--Right Frame [S]-->

			<!-- dynamic windows [s]-->
			<div id="myWin" style="height: 95%; width: 100%">
				<ul id="winName">
					<li>地图</li>
				</ul>
				<div id="winForm">
					<div style="overflow-y: none">
						<table width="100%" height="100%" border=0>
							<tr>
								<td>
									<div id="mapMain" style="width: 100%; height: 100%"></div>
									<div id="posDiv">
										<!-- for vehicles refresh -->
										秒后刷新
									</div>
								</td>
							</tr>
							<tr>
								<td height="150px">
									<div id="mapNote" style="height: 100%; width: 100%;">
										<div id="noteForm" style="height: 120px;">
											<div style="height: 120px; overflow: auto"
												id="monitorInfoSpanDiv">
												<table width="1500px" id="monitorTable" border=1>
													<tr>
														<td width="80px">车牌号</td>
														<td width="100px">手机号</td>
														<td width="100px">分组</td>
														<td width="150px">接收时间</td>
														<td width="150px">定位时间</td>
														<td>报警标志</td>
														<td>ACC状态</td>
														<td>定位状态</td>
														<td>速度</td>
														<td>方向</td>
														<td width="255px">地址</td>
														<td>经度</td>
														<td>纬度</td>
														<td>里程</td>
														<td>油量</td>
														<td>当班司机</td>
														<td>当班里程</td>
														<td>行驶时间</td>

													</tr>

												</table>
											</div>
											<div>
												<table width="100%">
													<tr>
														<td class="thColor" width="20px"><img
															id="settingTable" src='../image/settingd.png'
															style='width: 20px; height: 20px; cursor: pointer; padding: 1px; margin-top: 0px; vertical-align: middle;'
															onmouseover="styleImgMOver(this,'/808FrontProject/image/settingh.png','设置过滤报警类型')"
															onmouseout="styleImgMOut(this,'/808FrontProject/image/settingd.png')"
															onclick="filterWarningType();" /></td>
														<td rowspan=2>
															<div id="warningInfoSpanDiv"
																class="warningInfoSpanDivStyle">
																<table width="100%" id="warnTable" border=1
																	class="tableStyle">
																	<tr class="thStyle">
																		<td width="80px">车牌号</td>
																		<td>报警时间</td>
																		<td>报警内容</td>
																		<td>报警次数</td>
																		<td>车主信息</td>
																		<td>操作</td>
																	</tr>
																</table>
															</div>
														</td>
													</tr>
													<tr>
														<td class="thColor"><img id="settingTable"
															src='../image/soundd.png'
															style='width: 20px; height: 20px; cursor: pointer; padding: 1px; margin-top: 0px; vertical-align: middle;'
															title="点击关闭报警声音" onclick="controlSound(this);" /></td>
													</tr>
												</table>

											</div>
											<div style="overflow: auto">
												<table width="100%" id="cmdTable" class="tableStyle"
													border=1>
													<tr class="thStyle">
														<td>车牌</td>
														<td>命令</td>
														<td>上传时间</td>
														<td>执行结果</td>
													</tr>
												</table>
											</div>
											<div style="overflow: auto">
												<table width="100%" id="multiMediaTable" class="tableStyle"
													border=1>
													<tr class="thStyle">
														<td>车牌</td>
														<td>事件类型</td>
														<td>多媒体类型</td>
														<td>文件格式</td>
														<td>多媒体生成时间</td>
													</tr>
												</table>
											</div>
											<div style="overflow: auto">
												<table width="100%" id="infoTable" class="tableStyle"
													border=1>
													<tr class="thStyle">
														<td>信息类型</td>
														<td>上传时间</td>
														<td>信息内容</td>
													</tr>

												</table>
											</div>
										</div>
										<ul id="noteName">
											<li>监控信息</li>
											<li>报警</li>
											<li>命令结果</li>
											<li>多媒体</li>
											<li>综合信息</li>
										</ul>
									</div>
									<div id="alertDiv">
										状态提示 <img style="float: right" width="20" height="20"
											title="正常" src="/808FrontProject/image/alert-green.gif"
											style="vertical-align:middle" />
									</div>
								</td>
							</tr>

						</table>
					</div>
				</div>


			</div>
			<!-- dynamic windows [e]-->



			<!--Right Frame [E]-->
		</div>
		<!-- Main Frame [E] -->
	</div>
</body>

</html>
