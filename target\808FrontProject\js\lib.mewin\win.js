function MapeyeWin(t,e,i){this.winId=t;this.winNameId=e;this.winFormId=i;var n=document.getElementById(t);var s=document.getElementById(e);var r=document.getElementById(i);n.setAttribute("width",n.offsetWidth);s.className="winName";r.className="winForm";this.getChildNodes=function(t){var e=t.children;var i=new Array;for(var n=0,s=e.length;n<s;n++){if(e[n].nodeType==1){i.push(e[n])}}return i};var o=this.getChildNodes(s);var h=this.getChildNodes(r);for(var l=0;l<o.length;l++){o[l].setAttribute("onclick","new MapeyeWin('"+t+"','"+e+"','"+i+"').winClick(this)")}var a=100;var d=30;var u="";var c="./lib.mewin/";this.setImgPath=function(t){c=t+c};this.init=function(){this.resizeWinLayout();this.setWinStyle();this.setFWinStyle();if(o.length>0){u=o[0].innerHTML}};this.getWinNameFocus=function(){return u};this.winClick=function(t){o=this.getChildNodes(s);h=this.getChildNodes(r);this.change(t);u=t.innerHTML};this.setFWinStyle=function(){o=this.getChildNodes(s);h=this.getChildNodes(r);var t=o.length;var e=h.length;if(t>0){o[0].className="winNameFocus"}if(e>0){h[0].className="winFormFocus"}};this.setLWinStyle=function(){o=this.getChildNodes(s);h=this.getChildNodes(r);var t=o.length;var e=h.length;if(t>0){o[t-1].className="winNameFocus"}if(e>0){h[e-1].className="winFormFocus"}};this.setCurWinStyle=function(t){t.className="winNameFocus"};this.setWinStyle=function(){o=this.getChildNodes(s);h=this.getChildNodes(r);var t=o.length;for(var e=0;e<t;e++){o[e].className="winNameDefault"}var i=h.length;for(var n=0;n<i;n++){h[n].className="winFormDefault"}};this.cmdCloseStyle=function(t,e,i){if(e=="onmouseover"){t.src=i+"/closehover.png"}if(e=="onmouseout"){t.src=i+"/closedefault.png"}};this.winAdd=function(n,o){var h=document.createElement("li");h.setAttribute("id","name_"+n);h.setAttribute("onclick","new MapeyeWin('"+t+"','"+e+"','"+i+"').winClick(this)");h.setAttribute("width",a+"px");if(n.length<5)h.innerHTML=n;else h.innerHTML="<a title='"+n+"'>"+n.substring(0,4)+"..."+"</a>";s.appendChild(h);var l=document.createElement("img");l.setAttribute("src",c+"/closedefault.png");l.setAttribute("width","22px");l.setAttribute("height","16px");l.setAttribute("onmouseover","new MapeyeWin('"+t+"','"+e+"','"+i+"').cmdCloseStyle(this,'onmouseover','"+c+"')");l.setAttribute("onmouseout","new MapeyeWin('"+t+"','"+e+"','"+i+"').cmdCloseStyle(this,'onmouseout','"+c+"')");l.setAttribute("onclick","new MapeyeWin('"+t+"','"+e+"','"+i+"').winDel('"+n+"',event)");l.setAttribute("style","display: inline; float: right;");l.setAttribute("title","关闭");h.appendChild(l);var d=document.createElement("div");d.setAttribute("id","form_"+n);d.innerHTML=o;r.appendChild(d);this.resizeWinLayout();this.setWinStyle();this.setLWinStyle()};this.winDel=function(t,e){e.cancelBubble=true;var i=document.getElementById("name_"+t);s.removeChild(i);var n=document.getElementById("form_"+t);r.removeChild(n);this.resizeWinLayout();this.setWinStyle();this.setFWinStyle()};this.setObjWidth=function(t){n.setAttribute("width",t)};this.resizeWinLayout=function(){var t=this.getObjWidth(n);o=this.getChildNodes(s);var e=o.length;var i=t;var r=1;var h=i-r;if(e*a>h){var l=parseInt(h/e);for(var d=0;d<e;d++){o[d].style.width=l+"px"}}else{for(var d=0;d<e;d++){o[d].style.width=a+"px"}}};this.change=function(t){this.setWinStyle();this.setCurWinStyle(t);for(var e=0;e<o.length;e++){if(o[e]==t){h[e].className="winFormFocus"}else{h[e].className="winFormDefault"}}};this.getWinHeight=function(){var t=0;if(window.innerHeight){t=window.innerHeight}else if(document.body&&document.body.clientHeight){t=document.body.clientHeight}return t};this.getWinWidth=function(){var t=0;if(window.innerWidth){t=window.innerWidth}else if(document.body&&document.body.clientWidth){t=document.body.clientWidth}return t};this.getObjWidth=function(t){var e=t.getAttribute("width");if(e!=null){if(e.indexOf("px")>0){e=e.substring(0,e.indexOf("px"))}if(e.indexOf("%")>0){e=e.substring(0,e.indexOf("%"));e=parseInt(this.getObjWidth(t.parentNode))*parseInt(e)/100}}else{e=t.style.width;if(e==""){e=t.offsetWidth}else{if(e.indexOf("px")>0){e=e.substring(0,e.indexOf("px"))}if(e.indexOf("%")>0){e=e.substring(0,e.indexOf("%"));e=parseInt(this.getObjWidth(t.parentNode))*parseInt(e)/100}}}return e}}