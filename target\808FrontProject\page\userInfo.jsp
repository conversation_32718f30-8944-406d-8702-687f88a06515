<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@taglib prefix="s" uri="/struts-tags"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>个人信息</title>

<link rel="stylesheet" type="text/css"
	href="/808FrontProject/css/user.css">

<script type="text/javascript" src="/808FrontProject/js/lib.jquery.js"></script>
<script type="text/javascript">
	$(function(){
		var userName="<%=session.getAttribute("accountName")%>";
		$("#userName").html(userName);
		
		$("#modifyButton").click(function(){
			var userPassword=$("#password").val();
			$.ajax({
				url:"/808FrontProject/common/modifyUserInfoAction",
				data:{
					"userName":userName,
					"userPassword":userPassword
				},
				type:"post"
			});
			window.opener=null;
			window.open('','_self');
			window.close();
			
		});
		
		$("#cancleButton").click(function(){
			window.opener=null;
			window.open('','_self');
			window.close();
		});
	});
	

</script>
</head>

<body>
	<span class="title1">个人信息</span>
	<s:form id="modifyUserInfoForm" theme="simple">
	<table class="tableCommon">
		<tr>
			<td class="" >用户名</td>
			<td colspan="3" id="userName"></td>
		</tr>
		<tr>
			<td>输入密码</td>
			<td><input type="password" id="password"></td>
			
			<td>确认密码</td>
			<td><input type="password"></td>
	
		</tr>
		
		<tr>
			<td colspan="4" style="text-align:center"><input type="button" value="修改" class="buttonStyle" id="modifyButton">&nbsp;<input type="button" value="取消" class="buttonStyle" id="cancleButton"></td>
		</tr>
		
	</table>
    </s:form>

</body>
</html>