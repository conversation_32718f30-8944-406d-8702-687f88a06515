function BaiduMap(mapId) {

	this.mapId = mapId;

	this.ak = "6ukSP6o4ff8u9SSnlVEKmiZC";

	var map = new BMap.Map(mapId);// 在指定的容器内创建地图实例

	var animationId;

	var imgPath = "/808FrontProject/image/";

	this.setImgPath = function(picPath) {
		imgPath = picPath + imgPath;
	}

	// 初始化地图
	this.init = function() {
		var point = new BMap.Point(120.901, 32.081);
		map.centerAndZoom(point, 10);
		map.setDefaultCursor("crosshair");// 设置地图默认的鼠标指针样式
		map.enableScrollWheelZoom();// 启用滚轮放大缩小，默认禁用。

		// map.addEventListener("zoomend", parseResults);
		// map.addEventListener("moveend", parseResults);

		// 添加卫片
		var mapType1 = new BMap.MapTypeControl({
			mapTypes : [ BMAP_NORMAL_MAP, BMAP_HYBRID_MAP ],
			anchor : BMAP_ANCHOR_TOP_LEFT
		});
		map.addControl(mapType1); // 2D图，卫星图
		var opts = {
			offset : new BMap.Size(25, 40)
		};
		map.addControl(new BMap.NavigationControl(opts));

		// 添加比例尺
		// var scaleControl = new BMap.ScaleControl({
		// anchor : BMAP_ANCHOR_TOP_LEFT,
		// offset: new BMap.Size(90, 5)
		// });// 左上角，添加比例尺
		// map.addControl(scaleControl);
	}

	this.getMap = function() {
		return map;
	}

	this.addMarkersWithLabel = function(latArr, lonArr, labelArr, infoArr,
			optArr) {
		// Desc: add the marker with the label to the map
		for (var i = 0; i < latArr.length; i++) {
			this.addMarkerWithLabel(latArr[i], lonArr[i], labelArr[i],
					infoArr[i], optArr[i]);
		}
	}

	this.addMarkerWithLabel = function(lat, lon, label, info, opt) {
		var point = new BMap.Point(lon, lat);
		var myIcon;
		if (opt == "vector") {
			myIcon = new BMap.Symbol(BMap_Symbol_SHAPE_POINT, {
				size : 10,
				scale : 2,// scalor for the map
				fillColor : red,// 
				fillOpacity : 0.4,// 
				strokeWeight : 1,
			});
		} else if (opt == "offline") {
			myIcon = new BMap.Icon(imgPath + "/carDefault.png", new BMap.Size(
					30, 30), {});
		} else if (opt == "online") {
			myIcon = new BMap.Icon(imgPath + "/carHover.png", new BMap.Size(30,
					30), {});
		} else {
			myIcon = new BMap.Symbol(BMap_Symbol_SHAPE_POINT, {
				size : 3,
				scale : 1,// scalor for the map
				fillColor : '#FF0000',// 
				fillOpacity : 0.5,// 
				strokeWeight : 1,
			});
		}

		var marker = new BMap.Marker(point, {
			icon : myIcon

		});
		map.addOverlay(marker);

		// add the label to the marker
		var mlabel = new BMap.Label(label, {
			offset : new BMap.Size(25, 5)
		// offsize: the first argu. for the horizontal, the second for the
		// vertical
		// 7, 5 | 18,10
		});

		mlabel.setStyle({
			color : "#000",
			fontWeight : "bold",
			fontSize : "12px",
			height : "13px",
			lineHeight : "13px",
			background : "none",
			border : "1px",
		});
		marker.setLabel(mlabel);
		marker.addEventListener("click", function() {
			var opts = {
				width : 200,
				height : 225,
			};
			var infoWindow = new BMap.InfoWindow(info, opts);
			map.openInfoWindow(infoWindow, point);
		});
	}

	var markerArray = [];
	this.showMarker = function(index) {
		if (index > -1 && index < markerArray.length) {
			markerArray[index].show();
		}
	}
	this.hideMarker = function(index) {
		if (index > -1 && index < markerArray.length) {
			markerArray[index].hide();
		}
	}
	this.showMarkerAll = function() {
		for (var i = 0; i < markerArray.length; i++) {
			markerArray[i].show();
		}
	}
	this.hideMarkerAll = function() {
		for (var i = 0; i < markerArray.length; i++) {
			markerArray[i].hide();
		}
	}
	this.delMarkerAll = function() {
		for (var i = 0; i < markerArray.length; i++) {
			map.removeOverlay(markerArray[i]);
		}
		markerArray = [];// clear the array
	}

	this.addMarkersWithLabelAdvanced = function(latArr, lonArr, labelArr,
			infoArr, optArr) {
		// Desc: add the marker with the label to the map
		for (var i = 0; i < latArr.length; i++) {
			markerArray[i] = this.addMarkerWithLabelAdvanced(latArr[i],
					lonArr[i], labelArr[i], infoArr[i], optArr[i]);
		}
	}

	this.addMarkerWithLabelAdvanced = function(lat, lon, label, info, opt) {
		var point = new BMap.Point(lon, lat);
		var myIcon;
		if (opt == "vector") {
			myIcon = new BMap.Symbol(BMap_Symbol_SHAPE_POINT, {
				size : 10,
				scale : 2,// scalor for the map
				fillColor : red,// 
				fillOpacity : 0.4,// 
				strokeWeight : 1,
			});
		} else if (opt == "offline") {
			myIcon = new BMap.Icon(imgPath + "/carDefault.png", new BMap.Size(
					30, 30), {});
		} else if (opt == "online") {
			myIcon = new BMap.Icon(imgPath + "/carHover.png", new BMap.Size(30,
					30), {});
		}

		var marker = new BMap.Marker(point, {
			icon : myIcon

		});
		map.addOverlay(marker);

		// add the label to the marker
		var mlabel = new BMap.Label(label, {
			offset : new BMap.Size(25, 5)
		// offsize: the first argu. for the horizontal, the second for the
		// vertical
		// 7, 5 | 18,10
		});

		mlabel.setStyle({
			color : "#000",
			fontWeight : "bold",
			fontSize : "12px",
			height : "13px",
			lineHeight : "13px",
			background : "none",
			border : "1px",
		});
		marker.setLabel(mlabel);
		marker.addEventListener("click", function() {
			var opts = {
				width : 200,
				height : 225,
			};
			var infoWindow = new BMap.InfoWindow(info, opts);
			map.openInfoWindow(infoWindow, point);
		});
		marker.hide();
		return marker;
	}

	this.createIcon = function(angle) {
		// Desc.: select the icon based on the input azimuth angle
		var a = parseInt(Math.ceil(angle / 22.5) * 22.5);
		var imgUrl = imgPath + "/arrows/arrow_" + a + ".png";
		var icon = new BMap.Icon(imgUrl, new BMap.Size(22, 22));
		return icon;
	}

	this.addArrowMarkersWithLabel = function(latArr, lonArr, angleArr,
			labelArr, infoArr) {
		// Desc: add the arrow marker with the label to the map
		for (var i = 0; i < latArr.length; i++) {
			this.addArrowMarkerWithLabel(latArr[i], lonArr[i], angleArr[i],
					labelArr[i], infoArr[i]);
		}
	}

	this.addArrowMarkerWithLabel = function(lat, lon, angle, label, info) {
		// Desc: add the arrow marker with the label to the map
		var point = new BMap.Point(lon, lat);
		var iconArrow = this.createIcon(angle);
		var marker = new BMap.Marker(point, {
			icon : iconArrow
		});
		map.addOverlay(marker);

		// add the label to the marker
		var mlabel = new BMap.Label(label, {
			offset : new BMap.Size(25, -5)
		// offsize: the first argu. for the horizontal,
		// the second for the vertical.
		});

		mlabel.setStyle({
			color : "#000",
			fontWeight : "bold",
			fontSize : "12px",
			height : "13px",
			lineHeight : "13px",
			background : "#fff",
			border : "1px",
		});
		marker.setLabel(mlabel);
		marker.addEventListener("click", function() {
			var opts = {
				width : 200,
				height : 200,
			};
			var infoWindow = new BMap.InfoWindow(info, opts);
			map.openInfoWindow(infoWindow, point);
		});
	}

	this.addPolyLine = function(latArr, lonArr, lineColor, lineSize) {
		// Desc: add the polyline to the map with the input arrays of 'lat' and
		// 'lon'
		// alert("add polyline");
		var len = latArr.length;
		var ptArr = [];
		for (var i = 0; i < len; i++) {
			ptArr[i] = new BMap.Point(lonArr[i], latArr[i]);
		}
		var polyline = new BMap.Polyline(ptArr, {
			strokeColor : lineColor,
			strokeWeight : lineSize,
			strokeOpacity : 0
		}); // create the polyline
		map.addOverlay(polyline); // add the polyline to the map
	}

	this.addPolyLineAnimation = function(latArr, lonArr, lineColor, lineSize) {
		// Desc: add the animated polyline to the map
		var lyr1 = getOverlaysLen();
		var len = latArr.length;
		var n = 0;
		animationId = setInterval(function() {
			if (n < len) {
				var lats = subArray(latArr, 0, n);
				var lons = subArray(lonArr, 0, n);
				var lyr2 = getOverlaysLen();
				delLastOverlays(lyr2 - lyr1); // clean the last overlays.
				addPolyLine(lats, lons, lineColor, lineSize);
				++n;
			} else {
				n = 0;
				var lyr2 = getOverlaysLen();
				delLastOverlays(lyr2 - lyr1); // clean the last overlays.
			}

		}, 300);
	}

	this.addPolygon = function(latArr, lonArr, lineColor, lineSize) {
		var len = latArr.length;
		var ptArr = [];
		for (var i = 0; i < len; i++) {
			ptArr[i] = new BMap.Point(lonArr[i], latArr[i]);
		}
		var polygon = new BMap.Polygon(ptArr, {
			strokeColor : lineColor,
			strokeWeight : lineSize,
			strokeOpacity : 0.8,
			fillColor : "#ffffff",
			fillOpacity : 0.5,
		}); 
		map.addOverlay(polygon); 
	}

	this.resizeView = function(latArr, lonArr) {
		var len = latArr.length;
		var offset = 0;
		var latMax = getMax(latArr) + offset;// n
		var latMin = getMin(latArr) - offset;// s
		var lonMax = getMax(lonArr) + offset;// e
		var lonMin = getMin(lonArr) - offset;// w
		// alert(latMax + ", " + latMin + ", " + lonMax + ", " + lonMin);
		var b = new BMap.Bounds(new BMap.Point(lonMin, latMin), new BMap.Point(
				lonMax, latMax));// sw西南,ne东北
		map.setViewport(b);
	}

	this.centerView = function(lat, lon) {
		var pt = new BMap.Point(lon, lat);
		var zoom = map.getZoom();
		map.centerAndZoom(pt, zoom);
	}

	this.moveView = function(lat, lon) {
		// 当给定点接近视图边缘时，进行视图平移
		// get the current boundary
		var bs = map.getBounds(); // 获取可视区域
		var bssw = bs.getSouthWest(); // 可视区域左下角
		var bsne = bs.getNorthEast(); // 可视区域右上角
		var latS = bssw.lat;
		var lonW = bssw.lng;
		var latN = bsne.lat;
		var lonE = bsne.lng;

		var latSD = lat - latS;
		var latND = latN - lat;
		var lonWD = lon - lonW;
		var lonED = lonE - lon;
		var tol = 0.001;// 1d=100000m,0.001d=100m
		// alert("Current visible view:" + bssw.lng + "," + bssw.lat + " - " +
		// bsne.lng + ","+ bsne.lat);
		if (latSD <= tol || latND <= tol || lonWD <= tol || lonED <= tol) {
			// move south, north, west, east
			this.centerView(lat, lon);
		}
	}

	this.delOverlays = function() {
		// Desc: delete all overlayers
		var allOverlay = map.getOverlays();
		for (var i = 0; i < allOverlay.length; i++)
			map.removeOverlay(allOverlay[i]);
	}

	this.delLastOverlays = function(n) {
		// Desc: delete the last N overlayers
		var allOverlay = map.getOverlays();
		var len = allOverlay.length;
		for (var i = len - n; i < len; i++)
			map.removeOverlay(allOverlay[i]);
	}

	this.getOverlaysLen = function() {
		// Desc: get the length of overlayers
		var allOverlay = map.getOverlays();
		var len = allOverlay.length;
		return len;
	}

	/**
	 * this.parseResultsOnMap = function(data) { // alert("Calling the
	 * parseResultsOnMap()!"); this.delOverlays();
	 * 
	 * var lons = []; var lats = []; var carNumbers = []; var times = []; var
	 * speeds = []; var directions = []; var infos = []; var lonsWithOffset =
	 * []; var latsWithOffset = [];
	 * 
	 * for (var i = 0; i < data.length; i++) { if (data[i] != null) {
	 * lonsWithOffset[i] = data[i].lonWithOffset; latsWithOffset[i] =
	 * data[i].latWithOffset; lons[i] = data[i].lon; lats[i] = data[i].lat;
	 * carNumbers[i] = data[i].carNumber; times[i] = data[i].gpsTime; speeds[i] =
	 * data[i].speed; directions[i] = data[i].direction; // var pt = new
	 * BMap.Point(lons[i], lats[i]); infos[i] = "<div
	 * style='font-size:13px;line-height:22px;'>" + "<div
	 * style='font-weight:bold;background-color:#c1e0ff;width:200px;height:30px'>车辆详细信息</div>" +
	 * "车牌号: " + carNumbers[i] + "<br/>" + "经度: " + lons[i] + "<br/>" + "纬度: " +
	 * lats[i] + "</br>" + "速度：" + speeds[i] + " 公里/时<br/>" + "行车方向： " +
	 * getAzimuth(directions[i]) + " (" + directions[i] + " deg)<br/>" +
	 * "GPS采集时间：" + times[i] + "<br/>" + "当前位置：" + "<span id='currentAddress'
	 * style='cursor:pointer' onclick=\"new BaiduMap().parseAddress(" +
	 * latsWithOffset[i] + "," + lonsWithOffset[i] + ",'showAddress')\">" +
	 * "点击显示位置..." + "</span>" + "</div>"; // addMarker(pt, info); } }
	 * this.addMarkersWithLabel(latsWithOffset, lonsWithOffset, carNumbers,
	 * infos, "grid"); // alert(getOverlaysLen()); }
	 */

	this.parseAllResultsOnMap = function(data, opt) {
		// alert("Calling the parseResultsOnMap()!");
		this.delOverlays();

		var lons = [];
		var lats = [];
		var carNumbers = [];
		var times = [];
		var speeds = [];
		var directions = [];
		var infos = [];
		var lonsWithOffset = [];
		var latsWithOffset = [];
		var onlineStatuses = [];

		for (var i = 0; i < data.gpsInfo.length; i++) {
			if (data.gpsInfo[i] != null) {
				lonsWithOffset[i] = data.gpsInfo[i].lonWithOffset;
				latsWithOffset[i] = data.gpsInfo[i].latWithOffset;
				lons[i] = data.gpsInfo[i].lon;
				lats[i] = data.gpsInfo[i].lat;
				carNumbers[i] = data.gpsInfo[i].carNumber;
				times[i] = data.gpsInfo[i].gpsTime;
				speeds[i] = data.gpsInfo[i].speed;
				directions[i] = data.gpsInfo[i].direction;
				for (var j = 0; j < data.statusInfo.length; j++) {
					if (data.statusInfo[j] != null
							&& data.statusInfo[j].carNumber == carNumbers[i]) {
						var isOnline = data.statusInfo[j].onlineStatus;
						// 区分车辆在线离线
						if (isOnline == 1) {
							onlineStatuses[i] = "online";
						} else {
							onlineStatuses[i] = "offline";
						}
					}
				}

				infos[i] = "<div style='font-size:13px;line-height:22px;'>"
						+ "<div style='font-weight:bold;background-color:#c1e0ff;width:200px;height:30px'>车辆详细信息</div>"
						+ "车牌号: "
						+ carNumbers[i]
						+ "<br/>"
						+ "经度: "
						+ lons[i]
						+ "<br/>"
						+ "纬度: "
						+ lats[i]
						+ "</br>"
						+ "速度："
						+ speeds[i]
						+ " 公里/时<br/>"
						+ "行车方向： "
						+ getAzimuth(directions[i])
						+ " ("
						+ directions[i]
						+ " deg)<br/>"
						+ "GPS采集时间："
						+ times[i]
						+ "<br/>"
						+ "是否在线："
						+ (isOnline == 1 ? "在线" : "离线")
						+ "<br/>"
						+ "当前位置："
						+ "<span  style='cursor:pointer' onclick=\"new BaiduMap().parseAddress("
						+ latsWithOffset[i]
						+ ","
						+ lonsWithOffset[i]
						+ ",'showAddress',this)\">"
						+ "点击显示位置..."
						+ "</span>"
						+ "</div>";
			}
		}
		this.addMarkersWithLabel(latsWithOffset, lonsWithOffset, carNumbers,
				infos, onlineStatuses);
		// alert(getOverlaysLen());
		if (opt != "refresh" && latsWithOffset.length > 0
				&& lonsWithOffset.length > 0) {
			this.resizeView(latsWithOffset, lonsWithOffset);
		}
	}

	this.parseResultOnMap = function(data) {
		var info = "<div style='font-size:13px;line-height:22px;'>"
				+ "<div style='font-weight:bold;background-color:#c1e0ff;width:200px;height:30px'>车辆详细信息</div>"
				+ "车牌号: "
				+ data.carNumber
				+ "<br/>"
				+ "经度: "
				+ data.lon
				+ "<br/>"
				+ "纬度: "
				+ data.lat
				+ "</br>"
				+ "速度："
				+ data.speed
				+ " 公里/时<br/>"
				+ "行车方向： "
				+ getAzimuth(data.direction)
				+ " ("
				+ data.direction
				+ " deg)<br/>"
				+ "GPS采集时间："
				+ data.gpsTime
				+ "<br/>"
				+ "当前位置："
				+ "<div style='cursor:pointer' onclick=\"new BaiduMap().parseAddress("
				+ data.latWithOffset
				+ ","
				+ data.lonWithOffset
				+ ",'showAddress',this)\">" + "点击显示位置..." + "</div>" + "</div>";

		var label = data.carNumber;
		this.addMarkerWithLabel(data.latWithOffset, data.lonWithOffset, label,
				info, "online");

		// // 用于恢复取消轨迹查询后的地图
		// trackPointsOriGV[0] = data.vehicleId;
		// trackPointsOriGV[1] = data.lat;
		// trackPointsOriGV[2] = data.lon;
		// trackPointsOriGV[3] = data.direction;
		// trackPointsOriGV[4] = data.gpsTime;

		this.center(data);
	}

	this.center = function(data) {
		var pt = new BMap.Point(data.lonWithOffset, data.latWithOffset);
		var zoom = map.getZoom();
		map.centerAndZoom(pt, zoom);
	}

	//
	// 调用百度地图API进行地址解析
	this.parseAddress = function(lat, lon, callbackFunname, obj) {
		var geoCodeApi = "http://api.map.baidu.com/geocoder/v2/?ak=" + this.ak;
		var geoCodeUrl = geoCodeApi + "&callback=" + callbackFunname
				+ "&location=" + lat + "," + lon + "&output=json&pois=0";
		jQuery.getScript(geoCodeUrl);
		addressCursorGV = obj;
	}

	// 根据城市名定位
	this.locationByCity = function(cityName) {
		// alert("Calling the locationByCity()!");
		if (cityName != "") {
			map.centerAndZoom(cityName, 11); // 用城市名设置地图中心点
		}
	}

	this.locationByBrowser = function() {
		var geolocation = new BMap.Geolocation();
		geolocation.getCurrentPosition(function(r) {
			if (this.getStatus() == BMAP_STATUS_SUCCESS) {
				map.panTo(r.point);
			}
		}, {
			enableHighAccuracy : true
		})
		// 关于状态码
		// BMAP_STATUS_SUCCESS 检索成功。对应数值“0”。
		// BMAP_STATUS_CITY_LIST 城市列表。对应数值“1”。
		// BMAP_STATUS_UNKNOWN_LOCATION 位置结果未知。对应数值“2”。
		// BMAP_STATUS_UNKNOWN_ROUTE 导航结果未知。对应数值“3”。
		// BMAP_STATUS_INVALID_KEY 非法密钥。对应数值“4”。
		// BMAP_STATUS_INVALID_REQUEST 非法请求。对应数值“5”。
		// BMAP_STATUS_PERMISSION_DENIED 没有权限。对应数值“6”。(自 1.1 新增)
		// BMAP_STATUS_SERVICE_UNAVAILABLE 服务不可用。对应数值“7”。(自 1.1 新增)
		// BMAP_STATUS_TIMEOUT 超时。对应数值“8”。(自 1.1 新增)
	}

	this.getPointFromMouseToBDMap = function(e) {
		var point;
		map.addEventListener("click", function(e) {
			point = new BMap.Point(e.point.lng, e.point.lat);
			// alert(e.point.lng+","+e.point.lat);
		});
		return point;
	}

	this.rule = function() {
		var myDis = new BMapLib.DistanceTool(map);
		myDis.open(); // 开启鼠标测距
	}

	this.addCircle = function(lat, lon, radius) {
		// Unit for parameters:
		// lat: degree
		// lon: degree
		// radius: meter

		// added by Dawn Che, 2017/05/29

		// equation for the circle: (x-a)*(x-a)+(y-b)*(y-b)=r*r
		// convert the unit from 'km' to 'deg'
		var rLat = radius / (111.2 * 1000);
		var rLon = radius / (111.2 * 1000);
		var latArr = [];
		var lonArr = [];
		var rad = 0;
		// var log = "lat\tlon\n";
		var latTmp, lonTmp;
		for (var deg = 0; deg <= 360;) {
			rad = this.getRadian(deg);
			latTmp = lat + rLat * Math.sin(rad);
			lonTmp = lon + rLon / Math.cos(this.getRadian(latTmp))
					* Math.cos(rad);
			latArr.push(latTmp);
			lonArr.push(lonTmp);
			deg += 10;
			// log += latTmp + "\t" + lonTmp + "\t" + "\n"
		}
		// console.log(log);
		this.addPolygon(latArr, lonArr, "#FF0000", 3);
	}

	this.addCircleBD = function(lat, lon, radius) {
		// Using the default function of BMap.Circle() exists the bugs.
		var pt = new BMap.Point(lon, lat);
		var circle = new BMap.Circle(pt, radius, {
			fillColor : "#ffffff",
			fillOpacity : 0.5,
			strokeColor : "red",
			strokeWeight : 3,
			strokeOpacity : 0.8,
			enableEditing : false
		});
		map.addOverlay(circle);
	}

	this.addOval = function(centrePoint, x, y) {
		var assemble = new Array();
		var angle;
		var dot;
		var tangent = x / y;
		for (i = 0; i < 36; i++) {
			angle = (2 * Math.PI / 36) * i;
			dot = new BMap.Point(centrePoint.lng + Math.sin(angle) * y
					* tangent, centrePoint.lat + Math.cos(angle) * y);
			assemble.push(dot);
		}
		var oval = new BMap.Polygon(assemble, {
			strokeColor : "red",
			strokeWeight : 3,
			strokeOpacity : 0.8,
			fillColor : "#ffffff",
			fillOpacity : 0.5,
		});
		map.addOverlay(oval);
	}

	this.getDistance = function(lat1, lon1, lat2, lon2) {
		// unit for the returned parameter: meter
		if ((Math.abs(lat1) > 90) || (Math.abs(lat2) > 90)) {
			alert("输入的纬度不是大地坐标！");
			return false;
		}
		if ((Math.abs(lon1) > 180) || (Math.abs(lon2) > 180)) {
			alert("输入的经度不是大地坐标！");
			return false;
		}
		// alert(lat1+", "+lon1+" | "+lat2+", "+lon2);
		var radLat1 = this.getRadian(lat1);
		var radLat2 = this.getRadian(lat2);
		var a = radLat1 - radLat2;
		var b = this.getRadian(lon1) - this.getRadian(lon2);
		var s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2)
				+ Math.cos(radLat1) * Math.cos(radLat2)
				* Math.pow(Math.sin(b / 2), 2)));
		s = s * 6378.137;// EARTH_RADIUS;

		s = Math.round(s * 10000) / 10000 * 1000;
		// unit: meter
		return s;
	}

	this.getRadian = function(deg) {
		return deg * Math.PI / 180.0;
	}

	this.getRadianInverse = function(rad) {
		return rad * 180 / Math.PI;
	}

	this.getDeflection = function(refLat, refLon, defLat, defLon) {
		// reference point: (refLat, refLon)
		// deflection direction point: (defLat, defLon)
		var def = 0;
		var y = defLat - refLat;
		var x = defLon - refLon;
		var r = Math.sqrt(x * x + y * y);

		if (y >= 0 && x >= 0) {
			def = Math.asin(x / r);
		}

		if (y <= 0 && x >= 0) {
			def = Math.PI - Math.asin(x / r);
		}

		if (y <= 0 && x <= 0) {
			def = Math.PI - Math.asin(x / r);
		}
		if (y >= 0 && x <= 0) {
			def = Math.PI * 2 + Math.asin(x / r);
		}

		return this.getRadianInverse(def);

	}

}

// 地址解析辅助性函数
function showAddress(data) {
	addressCursorGV.innerHTML = data.result.formatted_address;
}