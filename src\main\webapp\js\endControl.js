//主页右键菜单函数
//函数名：临时跟踪
function tempMonitor() {
	var vId = $("#menuParameterL1").val();
	var codes = "<div id='monitorDiv'>"
			+ "位置汇报间隔 "
			+ "<input type='text' id='reportInterval' style='width: 50px' value='30'/> 秒"
			+ "&nbsp;&nbsp;持续时间 "
			+ "<input type='text' id='reportLastTime' style='width: 50px' /> 秒"
			+ "</div>";
	var d = dialog({
		title : '临时跟踪_' + vId,
		content : codes,
		okValue : '跟踪',
		ok : function() {
			var reportIntervalValue = $("#reportInterval").val();
			var reportLastTimeValue = $("#reportLastTime").val();
			submitInterval(reportIntervalValue, reportLastTimeValue, vId);
			return true;
		},
		cancelValue : '取消',
		cancel : function() {
		}
	});
	d.showModal();
}

function submitInterval(reportIntervalValue, reportLastTimeValue, carNumber) {

	initCommandResult();

	clearInterval(submitIntervalInterval);

	if (reportIntervalValue != 30 | reportLastTimeValue != "") {
		var dateString = getFormatDate();
		// 命令正在执行
		var commandContent1 = "<tr onmouseover='styleCellOnMouseOver(this, cellHoverColor)' onmouseout='styleCellOnMouseOut(this,cellDefaultColor)'><td>"
				+ carNumber
				+ "</td><td>临时跟踪</td><td>"
				+ dateString
				+ "</td><td>发送成功</td></tr>";

		$("#cmdTable").append(commandContent1);

		$
				.ajax({
					url : "/808FrontProject/endControl/changeIntervalAction",
					data : {
						reportInterval : reportIntervalValue,
						reportLastTime : reportLastTimeValue,
						carNumber : carNumber
					},
					type : "post",
					dataType : "json",
					success : function(data) {
						submitIntervalInterval = setInterval(
								function() {
									if (commandResult == 0)
										queryCommandResult(data.simCardString,
												data.commandSerialNumber);
									else {
										// 命令执行成功
										var dateString2 = getFormatDate();
										var commandContent2 = "<tr onmouseover='styleCellOnMouseOver(this, cellHoverColor)' onmouseout='styleCellOnMouseOut(this,cellDefaultColor)'><td>"
												+ carNumber
												+ "</td><td>临时跟踪</td><td>"
												+ dateString2
												+ "</td><td>执行成功</td></tr>";
										$("#cmdTable").append(commandContent2);
										clearInterval(submitIntervalInterval);
									}
								}, 1000);

						setTimeout(function() {
							clearInterval(submitIntervalInterval);
						}, 15000);

					}

				});

	}

}

// 函数名：拍照监控
function photoCapture() {
	var vId = $("#menuParameterL1").val();

	var nowDate = new Date();
	var nowh = nowDate.getHours();
	var nowm = nowDate.getMinutes();
	var nows = nowDate.getHours();
	var isNow = false;
	var codes1 = "<div id='photoDiv1'><hr style='height:1px; border:#FFFFFF'/>&nbsp;"
			+ "请选择摄像头通道 "
			+ "<select style='width: 80px' id='tab1_channelId'>"
			+ "<option value='1'>摄像头1</option>"
			+ "<option value='2'>摄像头2</option>"
			+ "<option value='3'>摄像头3</option>"
			+ "<option value='4'>摄像头4</option>"
			+ "</select>&nbsp;&nbsp; "
			+ "拍摄张数"
			+ "<input type='text' id='tab1_shotNumber' style='width: 50px' value='1'/> 张&nbsp;&nbsp;"
			+ "拍照间隔<input type='text' id='tab1_shotInterval' style='width: 50px' value='0'/> 秒"
			+ "</div>";

	var codes2 = "<div id='photoDiv2'><hr style='height:1px; border:#FFFFFF'/>&nbsp;"
			+ "请选择摄像头通道 "
			+ "<select style='width: 80px' id='tab2_channelId'>"
			+ "<option value='1'>摄像头1</option>"
			+ "<option value='2'>摄像头2</option>"
			+ "<option value='3'>摄像头3</option>"
			+ "<option value='4'>摄像头4</option>"
			+ "</select>&nbsp;&nbsp; "
			+ "拍摄时间&nbsp;";

	codes2 += "<select id='tab2_hour'>";
	for (var h = 0; h <= 9; h++) {
		if (isNow && h == nowh)
			codes2 += "<option selected='selected' value='0" + h + "'>0" + h
					+ "</option>";
		else
			codes2 += "<option value='0" + h + "'>0" + h + "</option>";
	}

	for (var h = 10; h <= 23; h++) {
		if (isNow && h == nowh)
			codes2 += "<option selected='selected' value='" + h + "'>" + h
					+ "</option>";
		else
			codes2 += "<option value='" + h + "'>" + h + "</option>";
	}
	codes2 += "</select>时 ";
	codes2 += "<select id='tab2_minute'>";
	for (var m = 0; m <= 9; m++) {
		if (isNow && m == nowm)
			codes2 += "<option selected='selected' value='0" + m + "'>0" + m
					+ "</option>";
		else
			codes2 += "<option value='0" + m + "'>0" + m + "</option>";
	}
	for (var m = 10; m <= 59; m++) {
		if (isNow && m == nowm)
			codes2 += "<option selected='selected' value='" + m + "'>" + m
					+ "</option>";
		else
			codes2 += "<option value='" + m + "'>" + m + "</option>";
	}
	codes2 += "</select>分 ";
	// + "<input type='text' id='tab2_shotTime' style='width: 80px' value=''/>"
	codes2 += "</div>";

	var codes3 = "<div id='photoDiv3'><hr style='height:1px; border:#FFFFFF'/>&nbsp;"
			+ "请选择摄像头通道 "
			+ "<select style='width: 80px' id='tab3_channelId'>"
			+ "<option value='1'>摄像头1</option>"
			+ "<option value='2'>摄像头2</option>"
			+ "<option value='3'>摄像头3</option>"
			+ "<option value='4'>摄像头4</option>"
			+ "</select>&nbsp;&nbsp; "
			+ "拍摄间隔"
			+ "<input type='text' id='tab3_shotInterval' style='width: 50px' value='300'/> 秒&nbsp;&nbsp;"
			+ "ACC状态  <select style='width: 40px' id='tab3_accStatus'>"
			+ "<option value='1'>开</option>"
			// + "<option value='2'>关</option>"
			+ "<option value='2'>全部</option>" + "</select>" + "</div>";

	var codes = "<div id='photoDiv' style='width:550px; height:55px'>"
			+ "<ul id='photoDivName'>"
			+ "<li>即时拍照</li>"
			+ "<li>定时拍照</li>"
			+ "<li>间隔拍照</li>"
			+ "</ul>"
			+ "<div id='photoDivForm'>"
			+ codes1
			+ codes2
			+ codes3
			+ "</div>"
			+ "</div>"
			+ "<script type='text/javascript'>"
			+ "var mephoto = new MapeyeWin(\"photoDiv\", \"photoDivName\", \"photoDivForm\");"
			+ "mephoto.setObjWidth('550px');" + "mephoto.init();"
			+ "mephoto.setImgPath(\"/808FrontProject/js/\");"
			+ "</script><br/>";

	var d = dialog({
		title : '拍照监控_' + vId,
		content : codes,
		okValue : '拍照',
		ok : function() {
			var index = "tab1";
			var tab = document.getElementById("photoDivName");
			var tabs = tab.children;
			for (var i = 0; i < tabs.length; i++) {
				if (tabs[i].nodeType == 1
						&& tabs[i].className == "winNameFocus") {
					index = "tab" + (i + 1);
				}
			}
			if (index == "tab1") {
				var channelId = $("#tab1_channelId").val();
				var shotNumber = $("#tab1_shotNumber").val();
				var shotInterval = $("#tab1_shotInterval").val();
				immediatelyPhoto(vId, channelId, shotNumber, shotInterval);
			} else if (index == "tab2") {
				var startTime = $("#tab2_hour").val() + ":"
						+ $("#tab2_minute").val();
				fixedTimePhoto(vId, startTime);
			} else if (index == "tab3") {
				var shotInterval = $("#tab3_shotInterval").val();
				var accStatus = $("#tab3_accStatus").val();
				fixedIntervalPhoto(vId, accStatus, shotInterval);
			}

			return true;

			// return false;
		},
		cancelValue : '取消',
		cancel : function() {
		}
	});
	d.showModal();
	// d.height(80);
}

// 函数名：拍照查询
function photoQuery() {
	var vId = $("#menuParameterL1").val();

	var w = 480;
	var h = 320;

	var codes = "<div id='photoDiv1' width='480px' height='100px' style='font-size:13px'>";
	codes += "请输入起始时间  "
	codes += "<input type='text' name='sDate1' id='sDate1' "
	codes += "onFocus='initFormText(this,\"\",\"请输入起始时间...\")'"
	codes += "onBlur='initFormText(this,\"请输入起始时间...\",\"请输入起始时间...\")' "
	codes += "style='width: 100px; margin-top: 2px; font-size:13px' value='请输入起始时间...'"
	codes += "title=\"时间格式：yyyy-mm-dd hh:mm\" /> "
	codes += "<input id=\"sdate1Btn\" type=\"button\" style=\"height: 25px; width: 30px; font-size:13px\" value=\"...\" /> "
	codes += "&nbsp;请输入结束时间  "
	codes += "<input type='text' name='sDate2' id='sDate2' "
	codes += "onFocus='initFormText(this,\"\",\"请输入结束时间...\")'"
	codes += "onBlur='initFormText(this,\"请输入结束时间...\",\"请输入结束时间...\")' "
	codes += "style='width: 100px; margin-top: 1px; font-size:13px' value='请输入结束时间...' title=\"时间格式：yyyy-mm-dd hh:mm\" />"
	codes += "<input id=\"sdate2Btn\" type=\"button\" style=\"height: 25px; width: 30px; font-size:13px\" value=\"...\" />"
	codes += "<br/> "
	codes += "请选择多媒体类型 <select style='width: 80px' id='mType'>"
	codes += "<option value='1'>图片</option>"
	codes += "</select>"
	codes += "</div>";
	codes += "<div id='photoDiv2' width='480px' height='0px'></div>";
	codes += "<script type='text/javascript'>";
	codes += "var cal1 = Calendar.setup({";
	codes += "onSelect : function(cal1) {";
	codes += "cal1.hide()";
	codes += "},";
	codes += "showTime: true";
	codes += "});";
	codes += "var cal2 = Calendar.setup({";
	codes += "onSelect : function(cal2) {";
	codes += "cal2.hide()";
	codes += "},";
	codes += "showTime: true";
	codes += "});";
	codes += "cal1.manageFields(\"sdate1Btn\", \"sDate1\", \"%Y-%m-%d %H:%M\");";
	codes += "cal2.manageFields(\"sdate2Btn\", \"sDate2\", \"%Y-%m-%d %H:%M\");";
	codes += "</script>";

	var d = dialog({
		title : '拍照查询_' + vId,
		content : codes,
		okValue : '查询',
		ok : function() {
			var picUrlArr = [];
			var picNoteArr = [];
			var sDate1 = $("#sDate1").val();
			var sDate2 = $("#sDate2").val();
			var mType = $("#mType").val();
			// 发送参数到服务端
			if (mType == 1) {
				$.ajax({
					url : "/808FrontProject/common/queryHistoryPhotoAction",
					data : {
						startDate : sDate1,
						endDate : sDate2,
						carNumber : vId
					},
					dataType : "json",
					type : "post",
					success : function(data) {
						for (var i = 0; i < data.length; i++) {
							picUrlArr.push(data[i].webPath);
							picNoteArr.push(data[i].note);
						}

						if (data.length > 0) {
							document.getElementById("photoDiv2").setAttribute(
									"height", "320px");
							var myPhoto = new MapeyeImg("photoDiv2", picUrlArr,
									picNoteArr);
							myPhoto.setWidth(w);
							myPhoto.setHeight(h);
							myPhoto.init();
							myPhoto.show();
						}

					}
				});
			}

			return false;
		},
		cancelValue : '取消',
		cancel : function() {
		}
	});
	d.showModal();
}

// 函数名：拍照设置
function photoSetting() {
	var vId = $("#menuParameterL1").val();

	var codes1 = "<div style='overflow: auto'><table id='tab1_table' style='width:100%; border: 1px #FFFFFF;text-align:center;font-size:13px'>"
			+ "<tr style='background-color:#ACD6FF;font-weight:bolder'>"
			+ "<td>车牌号</td>"
			+ "<td>摄像头通道</td>"
			+ "<td>拍摄时间</td>"
			+ "<td>选择</td>" + "</tr>" + "</table></div>";

	var codes2 = "<div style='overflow: auto'><table id='tab2_table' style='width:100%; border: 1px #FFFFFF;text-align:center;font-size:13px'>"
			+ "<tr style='background-color:#ACD6FF;font-weight:bolder'>"
			+ "<td>车牌号</td>"
			+ "<td>摄像头通道</td>"
			+ "<td>拍摄间隔</td>"
			+ "<td>ACC状态</td>" + "<td>选择</td>" + "</tr>" + "</table></div>";

	var codes = "<div id='photoDiv' style='width:600px; height:200px'>"
			+ "<ul id='photoDivName'>"
			+ "<li>定时拍照</li>"
			+ "<li>间隔拍照</li>"
			+ "</ul>"
			+ "<div id='photoDivForm'>"
			+ codes1
			+ codes2
			+ "</div>"
			+ "</div>"
			+ "<script type='text/javascript'>"
			+ "var mephoto = new MapeyeWin(\"photoDiv\", \"photoDivName\", \"photoDivForm\");"
			+ "mephoto.setObjWidth('550px');" + "mephoto.init();"
			+ "mephoto.setImgPath(\"/808FrontProject/js/\");"
			+ "</script><br/>";

	var d = dialog({
		title : '拍照设置',
		content : codes,
		okValue : '删除',
		ok : function() {
			var index = "tab1";
			var tab = document.getElementById("photoDivName");
			var tabs = tab.children;
			for (var i = 0; i < tabs.length; i++) {
				if (tabs[i].nodeType == 1
						&& tabs[i].className == "winNameFocus") {
					index = "tab" + (i + 1);
				}
			}
			var tab1 = document.getElementById("tab1_table");
			var tab2 = document.getElementById("tab2_table");

			var cellHoverColor = "#c4ddf6";
			var cellDefaultColor = "#ECF5FF";

			if (index == "tab1") {
				var selectedItems = "";
				$('input:checkbox[name=tab1_delCarNum]:checked').each(
						function(i) {
							if (0 == i) {
								selectedItems = $(this).val();
							} else {
								selectedItems += ("," + $(this).val());
							}
							// var selectedItem = $(this).val();
							// var selectedItemArray=selectedItem.split("_");
							// var index=selectedItemArray[2];
							// tab1.deleteRow(parseInt(index)+1);

						});
				if (selectedItems != "") {
					$
							.ajax({
								url : "/808FrontProject/endControl/deleteFixedTimeShotSettingAction",
								data : {
									selectedItems : selectedItems
								},
								type : "post",
								success : function(data) {
									delRowsFromTable(tab1, false);
									// 为false时不删除标题栏
									$
											.ajax({
												url : "/808FrontProject/endControl/queryFixedTimePhotoSettingAction",
												type : "post",
												dataType : "json",
												success : function(data) {
													for (var i = 0; i < data.length; i++) {
														var newtr1 = tab1
																.insertRow(tab1.rows.length);
														newtr1.style = "background-color:"
																+ cellDefaultColor;
														newtr1
																.setAttribute(
																		"onmouseover",
																		"styleCellOnMouseOver(this,'"
																				+ cellHoverColor
																				+ "')");
														newtr1
																.setAttribute(
																		"onmouseout",
																		"styleCellOnMouseOut(this,'"
																				+ cellDefaultColor
																				+ "')");
														var newtd11 = newtr1
																.insertCell(0);
														newtd11.innerHTML = data[i].carNumber;
														var newtd12 = newtr1
																.insertCell(1);
														newtd12.innerHTML = "摄像头通道1";
														var newtd13 = newtr1
																.insertCell(2);
														newtd13.innerHTML = data[i].startTime;
														var newtd14 = newtr1
																.insertCell(3);
														newtd14.innerHTML = "<input type='checkbox' name='tab1_delCarNum' value='"
																+ data[i].carNumber
																+ "_"
																+ data[i].startTime
																+ "_"
																+ i
																+ "'>";
													}
												}
											});

								}
							});
				}
			}

			if (index == "tab2") {
				var selectedItems = "";
				$('input:checkbox[name=tab2_delCarNum]:checked').each(
						function(i) {
							if (0 == i) {
								selectedItems = $(this).val();
							} else {
								selectedItems += ("," + $(this).val());
							}
							// var selectedItem = $(this).val();
							// var selectedItemArray=selectedItem.split("_");
							// var index=selectedItemArray[1];
							// tab2.deleteRow(parseInt(index)+1);
						});
				if (selectedItems != "") {
					$
							.ajax({
								url : "/808FrontProject/endControl/deleteFixedIntervalShotSettingAction",
								data : {
									selectedItems : selectedItems
								},
								type : "post",
								success : function(data) {
									delRowsFromTable(tab2, false);

									$
											.ajax({
												url : "/808FrontProject/endControl/queryFixedIntervalPhotoSettingAction",
												type : "post",
												dataType : "json",
												success : function(data) {
													for (var i = 0; i < data.length; i++) {
														var newtr2 = tab2
																.insertRow(tab2.rows.length);
														newtr2.style = "background-color:"
																+ cellDefaultColor;
														newtr2
																.setAttribute(
																		"onmouseover",
																		"styleCellOnMouseOver(this,'"
																				+ cellHoverColor
																				+ "')");
														newtr2
																.setAttribute(
																		"onmouseout",
																		"styleCellOnMouseOut(this,'"
																				+ cellDefaultColor
																				+ "')");
														var newtd21 = newtr2
																.insertCell(0);
														newtd21.innerHTML = data[i].carNumber;
														var newtd22 = newtr2
																.insertCell(1);
														newtd22.innerHTML = "摄像头通道1";
														var newtd23 = newtr2
																.insertCell(2);
														newtd23.innerHTML = data[i].shotInterval;
														var newtd24 = newtr2
																.insertCell(3);
														if (data[i].accStatus == 1)
															newtd24.innerHTML = "ACC开";
														else
															newtd24.innerHTML = "ACC开和关"
														var newtd25 = newtr2
																.insertCell(4);
														newtd25.innerHTML = "<input type='checkbox' name='tab2_delCarNum' value='"
																+ data[i].carNumber
																+ "_"
																+ i
																+ "'>";
													}
												}
											});

								}
							});
				}
			}

			if (Cookies.get("notShow") != "true") {
				if (confirm("请在一分钟之内不要对所选车辆进行间隔拍照设置！\n不再提示.")) {
					Cookies.set("notShow", "true");
				}
			}

			return false;
		},
		button : [ {
			value : '查询',
			callback : function() {
				var index = "tab1";
				var tab = document.getElementById("photoDivName");
				var tabs = tab.children;
				for (var i = 0; i < tabs.length; i++) {
					if (tabs[i].nodeType == 1
							&& tabs[i].className == "winNameFocus") {
						index = "tab" + (i + 1);
					}
				}
				// alert(index);
				var tab1 = document.getElementById("tab1_table");
				var tab2 = document.getElementById("tab2_table");

				var cellHoverColor = "#c4ddf6";
				var cellDefaultColor = "#ECF5FF";

				if (index == "tab1") {
					delRowsFromTable(tab1, false);
					// 为false时不删除标题栏
					$
							.ajax({
								url : "/808FrontProject/endControl/queryFixedTimePhotoSettingAction",
								type : "post",
								dataType : "json",
								success : function(data) {
									for (var i = 0; i < data.length; i++) {
										var newtr1 = tab1
												.insertRow(tab1.rows.length);
										newtr1.style = "background-color:"
												+ cellDefaultColor;
										newtr1
												.setAttribute(
														"onmouseover",
														"styleCellOnMouseOver(this,'"
																+ cellHoverColor
																+ "')");
										newtr1.setAttribute("onmouseout",
												"styleCellOnMouseOut(this,'"
														+ cellDefaultColor
														+ "')");
										var newtd11 = newtr1.insertCell(0);
										newtd11.innerHTML = data[i].carNumber;
										var newtd12 = newtr1.insertCell(1);
										newtd12.innerHTML = "摄像头通道1";
										var newtd13 = newtr1.insertCell(2);
										newtd13.innerHTML = data[i].startTime;
										var newtd14 = newtr1.insertCell(3);
										newtd14.innerHTML = "<input type='checkbox' name='tab1_delCarNum' value='"
												+ data[i].carNumber
												+ "_"
												+ data[i].startTime
												+ "_"
												+ i
												+ "'>";
									}
								}
							});

				}
				if (index == "tab2") {
					delRowsFromTable(tab2, false);

					$
							.ajax({
								url : "/808FrontProject/endControl/queryFixedIntervalPhotoSettingAction",
								type : "post",
								dataType : "json",
								success : function(data) {
									for (var i = 0; i < data.length; i++) {
										var newtr2 = tab2
												.insertRow(tab2.rows.length);
										newtr2.style = "background-color:"
												+ cellDefaultColor;
										newtr2
												.setAttribute(
														"onmouseover",
														"styleCellOnMouseOver(this,'"
																+ cellHoverColor
																+ "')");
										newtr2.setAttribute("onmouseout",
												"styleCellOnMouseOut(this,'"
														+ cellDefaultColor
														+ "')");
										var newtd21 = newtr2.insertCell(0);
										newtd21.innerHTML = data[i].carNumber;
										var newtd22 = newtr2.insertCell(1);
										newtd22.innerHTML = "摄像头通道1";
										var newtd23 = newtr2.insertCell(2);
										newtd23.innerHTML = data[i].shotInterval;
										var newtd24 = newtr2.insertCell(3);
										if (data[i].accStatus == 1)
											newtd24.innerHTML = "ACC开";
										else
											newtd24.innerHTML = "ACC开和关"
										var newtd25 = newtr2.insertCell(4);
										newtd25.innerHTML = "<input type='checkbox' name='tab2_delCarNum' value='"
												+ data[i].carNumber
												+ "_"
												+ i
												+ "'>";
									}
								}
							});

				}

				return false;
			},
			autofocus : true
		} ]
	});
	d.showModal();
}

//函数名：视频监控
function videoMonitor(){
	var result = new Array();
	$("[name = group]:checkbox").each(function() {
		if ($(this).is(":checked")) {
			result.push($(this).attr("value"));
		}
	});
	if(result.length==1){
		openModal('/808FrontProject/page/videoJiankong.html?carID='+result);
	}else{
		var d = dialog({
			title : '视频监控打开出错！',
			content : "视频监控仅限于选择一辆车，目前选择的车辆数为："+result.length,
			okValue : '确定',
			ok : function() {
				return true;
			}
		});
		d.showModal();
	}
	
}

//函数名：视频回放
function videoHuifang(){
	var result = new Array();
	$("[name = group]:checkbox").each(function() {
		if ($(this).is(":checked")) {
			result.push($(this).attr("value"));
		}
	});
	if(result.length==1){
		openModal('/808FrontProject/page/videoHuifang.html?carID='+result);
	}else{
		var d = dialog({
			title : '视频回放打开出错！',
			content : "视频回放仅限于选择一辆车，目前选择的车辆数为："+result.length,
			okValue : '确定',
			ok : function() {
				return true;
			}
		});
		d.showModal();
	}
	
}

// 函数名：围栏绑定
function fenceSetting() {
	var vId = $("#menuParameterL1").val();

	var cellHoverColor = "#c4ddf6";
	var cellDefaultColor = "#ECF5FF";

	$.ajax({
		url:"/808FrontProject/endControl/queryFenceSettingAction.action",
		type:"post",
		dataType:"json",
		success:function(data){
			var codes = "<div style='overflow: auto; width:550px; height:300px;'><table id='tab_table' style='width:100%; border: 1px #FFFFFF;text-align:center;font-size:13px'>"
				+ "<tr style='background-color:#ACD6FF;font-weight:bolder'>"
				+ "<td>序号</td>"
				+ "<td>电子围栏名称</td>"
				+ "<td>几何类型</td>"
				+ "<td>创建时间</td>"
				+ "<td>创建者</td>"
				+ "<td>选择</td>"
				+ "</tr>";
			for(var i=0;i<data.length;i++){
				codes+="<tr style='background-color:#ECF5FF;'  onmouseover=styleCellOnMouseOver(this,'#c4ddf6') onmouseout=styleCellOnMouseOut(this,'#ECF5FF')>";
				codes+= "<td>"+(i+1)+"</td>"
					+ "<td>"+data[i].areaName+"</td>"; 
				if(data[i].geometryType==1)
					codes+= "<td>圆形</td>";
				else if(data[i].geometryType==2)
					codes+= "<td>矩形</td>";
				else if(data[i].geometryType==3)
					codes+= "<td>多边形</td>";
				else if(data[i].geometryType==4)
					codes+= "<td>路线</td>";
				codes+= "<td>"+data[i].createTime+"</td>"
					+ "<td>"+data[i].creator+"</td>"
					+ "<td><input type='radio' name='selectedFence' value='"+data[i].areaName+"_"+data[i].geometryType+"'/></td>";
				codes+= "</tr>";
			}
			codes+= "</table></div>";

			var d = dialog({
				title : '围栏绑定_' + vId,
				content : codes,
				okValue : '绑定',
				ok : function() {
	
					var selectedFenceParams=$("input[name='selectedFence']:checked").val();
					var selectedFenceParamsArray=selectedFenceParams.split("_");
					var areaName=selectedFenceParamsArray[0];
					var geometryType=selectedFenceParamsArray[1];
					$.ajax({
						url:"/808FrontProject/endControl/bindFenceSettingAction",
						data:{
							carNumber:vId,
							areaName:areaName,
							geometryType:geometryType
						},
						type:"post",
						dataType:"json"
					});
					return false;
				},
	
				button : [ {
					value : '删除绑定',
					callback : function() {
						var selectedFenceParams=$("input[name='selectedFence']:checked").val();
						var selectedFenceParamsArray=selectedFenceParams.split("_");
						var areaName=selectedFenceParamsArray[0];
						var geometryType=selectedFenceParamsArray[1];
						$.ajax({
							url:"/808FrontProject/endControl/unbindFenceSettingAction",
							data:{
								carNumber:vId,
								areaName:areaName,
								geometryType:geometryType
							},
							type:"post",
							dataType:"json"
						});
						return false;
					}
				} ],
			});
			d.showModal();
			}
	});
	
}


function immediatelyPhoto(carNumber, channelId, shotNumber, shotInterval) {

	// clearInterval(immediatelyPhotoInterval);

	var dateString = getFormatDate();
	// 命令正在执行
	var commandContent1 = "<tr onmouseover='styleCellOnMouseOver(this, cellHoverColor)' onmouseout='styleCellOnMouseOut(this,cellDefaultColor)'><td>"
			+ carNumber
			+ "</td><td>车辆拍照</td><td>"
			+ dateString
			+ "</td><td>发送 成功</td></tr>";

	$("#cmdTable").append(commandContent1);

	$
			.ajax({
				url : "/808FrontProject/endControl/immediatelyPhotoAction",
				data : {
					carNumber : carNumber,
					channelId : channelId,
					shotNumber : shotNumber,
					shotInterval : shotInterval
				},
				type : "post",
				dataType : "json",
				success : function(data) {

					var immediatelyPhotoInterval = window
							.setInterval(
									function() {
										if (commandResult == 0)
											queryCommandResult(
													data.simCardString,
													data.commandSerialNumber);
										else {

											// 命令执行成功
											var dateString2 = getFormatDate();
											var commandContent2 = "<tr onmouseover='styleCellOnMouseOver(this, cellHoverColor)' onmouseout='styleCellOnMouseOut(this,cellDefaultColor)'><td>"
													+ carNumber
													+ "</td><td>车辆拍照</td><td>"
													+ dateString2
													+ "</td><td>执行成功</td></tr>";
											$("#cmdTable").append(
													commandContent2);
											initCommandResult();
											clearInterval(immediatelyPhotoInterval);
											// setTimeout(
											// function() {
											// getMultiMediaEventInfo(data.simCardString);
											// }, 5000);
											//
											// var
											// getMultiMediaEventInfoInterval;
											// if (shotNumber != 1) {
											// getMultiMediaEventInfoInterval =
											// setInterval(
											// function() {
											// getMultiMediaEventInfo(data.simCardString);
											// }, shotInterval * 1000);
											//
											// }
											// setTimeout(
											// function() {
											// clearInterval(getMultiMediaEventInfoInterval)
											// }, shotInterval
											// * shotNumber * 1000
											// - 1000);
										}
									}, 3000);
					setTimeout(function() {
						clearInterval(immediatelyPhotoInterval);
					}, 75000);
				}
			});

}

function fixedIntervalPhoto(carNumber, accStatus, shotInterval) {
	$.ajax({
		url : "/808FrontProject/endControl/fixedIntervalPhotoAction",
		data : {
			carNumber : carNumber,
			accStatus : accStatus,
			shotInterval : shotInterval
		},
		type : "post",
		success : function(data) {
			if (data.result == 0) {
				alert("该车牌号的已设置定间距拍照，不能重复设置");
			}
		}
	});
}

function fixedTimePhoto(carNumber, startTimeString) {
	$.ajax({
		url : "/808FrontProject/endControl/fixedTimePhotoAction",
		data : {
			carNumber : carNumber,
			startTimeString : startTimeString
		},
		type : "post"
	});
}
/**
 * function getMultiMediaEventInfo(simCardString) { $ .ajax({ url :
 * "/808FrontProject/common/getMultiMediaEventInfoBySimCardNumberAction", data : {
 * simCardString : simCardString, }, type : "post", dataType : "json", success :
 * function(data) { var htmlContent = "
 * <tr onmouseover='styleCellOnMouseOver(this, cellHoverColor)' onmouseout='styleCellOnMouseOut(this,cellDefaultColor)'>" + "
 * <td title='点击显示图像' style='cursor:pointer' onclick='showMultiMedia(this)'>" +
 * data.carNumber + "</td>
 * <td>" + data.eventType + "</td>
 * <td id='mediaType'>" + data.mediaType + "</td>
 * <td>" + data.fileType + "</td>
 * <td id='createTime'>" + data.createTime + "</td>
 * </tr>"; $("#multiMediaTable").append(htmlContent); } }); }
 * 
 * function showMultiMedia(obj) { var carNumber = $(obj).html(); var mediaType =
 * $(obj).parent("tr").find("td#mediaType").html(); var createTimeString =
 * $(obj).parent("tr").find("td#createTime").html(); if (mediaType == "图像") {
 * $.ajax({ url : "/808FrontProject/common/queryCurrentPhotoAction", data : {
 * carNumber : carNumber, createTimeString : createTimeString }, type : "post",
 * dataType : "json", success : function(data) { //
 * alert(data.simCardNumber+":"+data.commandSerialNumber); var w = 480; var h =
 * 320; var codes = "<a target='_blank' href='data'><img width=" + w + "
 * hight=" + h + " src='" + data + "' /></a>"; var d = dialog({ title : "实时图像_" +
 * carNumber + "_" + createTimeString, content : codes, }); d.show(); } }); } }
 */
// 函数名：文本下发
function textDelivery() {
	var vId = $("#menuParameterL1").val();
	var codes = "<div id='textDiv'>" + "下发文本信息  "
			+ "<input type='text' id='textMessage' style='width: 200px' />"
			+ "</div>";
	var d = dialog({
		title : '发送调度_' + vId,
		content : codes,
		okValue : '发送',
		ok : function() {
			var textMessageContent = $("#textMessage").val();
			deliveryTextMessage(vId, textMessageContent);
			return true;
		},
		cancelValue : '取消',
		cancel : function() {
		}
	});
	d.showModal();
}

function deliveryTextMessage(carNumber, textMessageContent) {

	initCommandResult();

	clearInterval(textMessageDeliveryInterval);

	var dateString = getFormatDate();
	// 命令正在执行
	var commandContent1 = "<tr onmouseover='styleCellOnMouseOver(this, cellHoverColor)' onmouseout='styleCellOnMouseOut(this,cellDefaultColor)'><td>"
			+ carNumber
			+ "</td><td>发送调度</td><td>"
			+ dateString
			+ "</td><td>发送成功</td></tr>";

	$("#cmdTable").append(commandContent1);

	$
			.ajax({
				url : "/808FrontProject/endControl/textMessageDeliveryAction",
				data : {
					carNumber : carNumber,
					textMessage : textMessageContent
				},
				type : "post",
				dataType : "json",
				success : function(data) {
					// alert("sim卡号:"+data.simCardString+"|流水号:"+data.commandSerialNumber);
					textMessageDeliveryInterval = setInterval(
							function() {
								if (commandResult == 0)
									queryCommandResult(data.simCardString,
											data.commandSerialNumber);
								else {
									// 命令执行成功
									var dateString2 = getFormatDate();
									var commandContent2 = "<tr onmouseover='styleCellOnMouseOver(this, cellHoverColor)' onmouseout='styleCellOnMouseOut(this,cellDefaultColor)'><td>"
											+ carNumber
											+ "</td><td>发送调度</td><td>"
											+ dateString2
											+ "</td><td>执行成功</td></tr>";
									$("#cmdTable").append(commandContent2);
									clearInterval(textMessageDeliveryInterval);
								}
							}, 1000);

					setTimeout(function() {
						clearInterval(textMessageDeliveryInterval);
					}, 15000);

				}
			});
}

// 车辆点名
function positionEnquire() {

	var carNumber = $("#menuParameterL1").val();

	initCommandResult();

	clearInterval(positionEnquireInterval);

	var dateString = getFormatDate();
	// 命令正在执行
	var commandContent1 = "<tr onmouseover='styleCellOnMouseOver(this, cellHoverColor)' onmouseout='styleCellOnMouseOut(this,cellDefaultColor)'><td>"
			+ carNumber
			+ "</td><td>车辆点名</td><td>"
			+ dateString
			+ "</td><td>发送成功</td></tr>";

	$("#cmdTable").append(commandContent1);

	$
			.ajax({
				url : "/808FrontProject/endControl/positionEnquireAction",
				data : {
					carNumber : carNumber
				},
				type : "post",
				dataType : "json",
				success : function(data) {
					positionEnquireInterval = window
							.setInterval(
									function() {
										if (commandResult == 0)
											queryCommandResult(
													data.simCardString,
													data.commandSerialNumber);
										else {
											// 命令执行成功
											var dateString2 = getFormatDate();
											var commandContent2 = "<tr onmouseover='styleCellOnMouseOver(this, cellHoverColor)' onmouseout='styleCellOnMouseOut(this,cellDefaultColor)'><td>"
													+ carNumber
													+ "</td><td>车辆点名</td><td>"
													+ dateString2
													+ "</td><td>执行成功</td></tr>";
											$("#cmdTable").append(
													commandContent2);
											clearInterval(positionEnquireInterval);

										}

									}, 1000);
					setTimeout(function() {
						clearInterval(positionEnquireInterval);
					}, 15000);
				}
			});
}

// 终端控制
function terminalReset() {
	var vId = $("#menuParameterL1").val();

	initCommandResult();
	clearInterval(terminalResetCommandInterval);

	$.ajax({
		url : "/808FrontProject/endControl/terminalResetAction",
		data : {
			carNumber : vId
		},
		dataType : "json",
		type : "post",
		success : function(data) {
			terminalResetCommandInterval = setInterval(function() {
				if (commandResult == 0)
					queryCommandResult(data.simCardString,
							data.commandSerialNumber);
				else
					clearInterval(terminalResetCommandInterval);
			}, 1000);

			setTimeout(function() {
				clearInterval(terminalResetCommandInterval);
			}, 15000);
		}

	});
}

function nTempMonitor() {
	var result = new Array();
	$("[name = group]:checkbox").each(function() {
		if ($(this).is(":checked")) {
			result.push($(this).attr("value"));
		}
	});
	var codes = showResultInTable(result);
	if (result.length > 0) {
		codes += "<div id='monitorDiv' style='margin-top:10px'>"
				+ "位置汇报间隔 "
				+ "<input type='text' id='reportInterval' style='width: 50px' value='30'/> 秒"
				+ "&nbsp;&nbsp;持续时间 "
				+ "<input type='text' id='reportLastTime' style='width: 50px' /> 秒"
				+ "</div>";
		var d = dialog({
			title : '群发: 临时跟踪',
			content : codes,
			okValue : '跟踪',
			ok : function() {
				return true;
			},
			cancelValue : '取消',
			cancel : function() {
			}
		});
		d.showModal();
	} else {
		var d = dialog({
			title : '群发: 临时跟踪',
			content : codes,
			button : [ {
				value : '取消',
				callback : function() {
				},
				autofocus : true
			} ]
		});
		d.showModal();
	}
}

function nPhotoCapture() {
	var carNumberArray = new Array();
	$("[name = group]:checkbox").each(function() {
		if ($(this).is(":checked")) {
			carNumberArray.push($(this).attr("value"));
		}
	});

	var carNumbers = "";
	for (var i = 0; i < carNumberArray.length - 1; i++) {
		carNumbers += carNumberArray[i] + ","
	}
	carNumbers += carNumberArray[carNumberArray.length - 1];

	var codes = showResultInTable(carNumberArray);

	var nowDate = new Date();
	var nowh = nowDate.getHours();
	var nowm = nowDate.getMinutes();
	var nows = nowDate.getHours();
	var isNow = false;
	var codes1 = "<div id='photoDiv1'><hr style='height:1px; border:#FFFFFF'/>&nbsp;"
			+ "请选择摄像头通道&nbsp;"
			+ "<select style='width: 80px' id='tab1_channelId'>"
			+ "<option value='1'>摄像头1</option>"
			+ "<option value='2'>摄像头2</option>"
			+ "<option value='3'>摄像头3</option>"
			+ "<option value='4'>摄像头4</option>"
			+ "</select>&nbsp;&nbsp; "
			+ "拍摄张数&nbsp;"
			+ "<input type='text' id='tab1_shotNumber' style='width: 50px' value='1'/> 张&nbsp;&nbsp; "
			+ "拍照间隔<input type='text' id='tab1_shotInterval' style='width: 50px' value='0'/> 秒"
			+ "</div>";

	var codes2 = "<div id='photoDiv2'><hr style='height:1px; border:#FFFFFF'/>&nbsp;"
			+ "请选择摄像头通道&nbsp;"
			+ "<select style='width: 80px' id='tab2_channelId'>"
			+ "<option value='1'>摄像头1</option>"
			+ "<option value='2'>摄像头2</option>"
			+ "<option value='3'>摄像头3</option>"
			+ "<option value='4'>摄像头4</option>"
			+ "</select>&nbsp;&nbsp; "
			+ "拍摄时间&nbsp;";

	codes2 += "<select id='tab2_hour'>";
	for (var h = 0; h <= 9; h++) {
		if (isNow && h == nowh)
			codes2 += "<option selected='selected' value='0" + h + "'>0" + h
					+ "</option>";
		else
			codes2 += "<option value='0" + h + "'>0" + h + "</option>";
	}
	for (var h = 10; h <= 23; h++) {
		if (isNow && h == nowh)
			codes2 += "<option selected='selected' value='" + h + "'>" + h
					+ "</option>";
		else
			codes2 += "<option value='" + h + "'>" + h + "</option>";
	}
	codes2 += "</select>时 ";
	codes2 += "<select id='tab2_minute'>";
	for (var m = 0; m <= 9; m++) {
		if (isNow && m == nowm)
			codes2 += "<option selected='selected' value='0" + m + "'>0" + m
					+ "</option>";
		else
			codes2 += "<option value='0" + m + "'>0" + m + "</option>";
	}
	for (var m = 10; m <= 59; m++) {
		if (isNow && m == nowm)
			codes2 += "<option selected='selected' value='" + m + "'>" + m
					+ "</option>";
		else
			codes2 += "<option value='" + m + "'>" + m + "</option>";
	}
	codes2 += "</select>分 ";
	// + "<input type='text' id='tab2_shotTime' style='width: 80px' value=''/>"
	codes2 += "</div>";

	var codes3 = "<div id='photoDiv3'><hr style='height:1px; border:#FFFFFF'/>&nbsp;"
			+ "请选择摄像头通道&nbsp;"
			+ "<select style='width: 80px' id='tab3_channelId'>"
			+ "<option value='1'>摄像头1</option>"
			+ "<option value='2'>摄像头2</option>"
			+ "<option value='3'>摄像头3</option>"
			+ "<option value='4'>摄像头4</option>"
			+ "</select>&nbsp;&nbsp; "
			+ "拍摄间隔&nbsp;"
			+ "<input type='text' id='tab3_shotInterval' style='width: 50px' value='300'/> 秒&nbsp;&nbsp; "
			+ "ACC状态&nbsp;<select style='width: 40px' id='tab3_accStatus'>"
			+ "<option value='1'>开</option>"
			+ "<option value='2'>开和关</option>"
			+ "</select>" + "</div>";
	// + "<option value='2'>关</option>"
	// + "<option value='3'>全部</option>" + "</select>" + "</div>";

	if (carNumberArray.length > 0) {
		codes += "<div id='photoDiv' style='width:550px; height:55px; margin-top:10px'>"
				+ "<ul id='photoDivName'>"
				+ "<li>即时拍照</li>"
				+ "<li>定时拍照</li>"
				+ "<li>间隔拍照</li>"
				+ "</ul>"
				+ "<div id='photoDivForm'>"
				+ codes1
				+ codes2
				+ codes3
				+ "</div>"
				+ "</div>"
				+ "<script type='text/javascript'>"
				+ "var mephoto = new MapeyeWin(\"photoDiv\", \"photoDivName\", \"photoDivForm\");"
				+ "mephoto.setObjWidth('550px');"
				+ "mephoto.init();"
				+ "mephoto.setImgPath(\"/808FrontProject/js/\");"
				+ "</script>"
				+ "<br/>";

		var d = dialog({
			title : '群发: 拍照监控',
			content : codes,
			okValue : '拍照',
			ok : function() {
				var index = "tab1";
				var tab = document.getElementById("photoDivName");
				var tabs = tab.children;
				for (var i = 0; i < tabs.length; i++) {
					if (tabs[i].nodeType == 1
							&& tabs[i].className == "winNameFocus") {
						index = "tab" + (i + 1);
					}
				}
				if (index == "tab1") {
					var channelId = $("#tab1_channelId").val();
					var shotNumber = $("#tab1_shotNumber").val();
					var shotInterval = $("#tab1_shotInterval").val();
					nimmediatelyPhoto(vId, channelId, shotNumber, shotInterval);
				} else if (index == "tab2") {
					var startTime = $("#tab2_hour").val() + ":"
							+ $("#tab2_minute").val();
					nFixedTimePhoto(carNumbers, startTime);
				} else if (index == "tab3") {
					var shotInterval = $("#tab3_shotInterval").val();
					var accStatus = $("#tab3_accStatus").val();
					nFixedIntervalPhoto(carNumbers, accStatus, shotInterval);
				}

				return true;
			},
			cancelValue : '取消',
			cancel : function() {
			}
		});
		d.showModal();
	} else {
		var d = dialog({
			title : '群发: 拍照监控',
			content : codes,
			button : [ {
				value : '取消',
				callback : function() {
				},
				autofocus : true
			} ]
		});
		d.showModal();
	}
}

function nFixedTimePhoto(carNumbers, startTimeString) {
	$.ajax({
		url : "/808FrontProject/endControl/nFixedTimePhotoAction",
		data : {
			carNumbers : carNumbers,
			startTimeString : startTimeString
		},
		type : "post"
	});
}

function nFixedIntervalPhoto(carNumbers, accStatus, shotInterval) {
	$.ajax({
		url : "/808FrontProject/endControl/nFixedIntervalPhotoAction",
		data : {
			carNumbers : carNumbers,
			accStatus : accStatus,
			shotInterval : shotInterval
		},
		type : "post",
		success : function(data) {

			var warningContent = "";
			for (var i = 0; i < data.length; i++) {
				if (data[i].result == 0)
					warningContent += "车牌" + data[i].carNumber
							+ "已设置定间距拍照，不能重复设置" + "\n"
			}
			if (warningContent != "")
				alert(warningContent);
		}
	});
}

function showResultInTable(arr) {
	var len = arr.length;
	var codes = "";
	var tdNum = 5;
	if (len > 0) {
		codes = "<table style='border-collapse: collapse;text-align:center'>";
		var tds = 0;
		var trs = 0;
		if (len <= tdNum) {
			trs = 2;
			tds = len;
		}
		if (len > tdNum) {
			trs = Math.ceil(len / tdNum) + 1;
			tds = tdNum;
		}
		// alert(len+","+trs+","+tds)
		var i = 0;
		for (var m = 0; m < trs; m++) {
			if (m == 0) {
				codes += "<tr class='trStyle'>";
				for (var n = 0; n < tds; n++) {
					codes += "<td width='107px' style='border: 1px solid gray'>选择的车辆</td>";
				}
				codes += "</tr>";
			} else {
				codes += "<tr>";
				for (var n = 0; n < tds; n++) {
					if (i < len) {
						codes += "<td style='border: 1px solid gray' onmouseover='styleCellOnMouseOver(this, cellHoverColor)' onmouseout='styleCellOnMouseOut(this,cellDefaultColor)'>"
								+ arr[i] + "</td>";
					} else {
						codes += "<td style='border: 1px solid gray'>"
								+ "</td>";
					}
					i++
				}
				codes += "</tr>";
			}
			// alert(codes);
		}
		codes += "</table>";
	} else {
		codes = "请在左侧车辆列表中选择车辆！";
	}
	return codes;
}

function nImmediatelyPhoto(carNumbers, channelId) {

	var dateString = getFormatDate();
	// 命令正在执行
	var commandContent1 = "<tr onmouseover='styleCellOnMouseOver(this, cellHoverColor)' onmouseout='styleCellOnMouseOut(this,cellDefaultColor)'><td>"
			+ carNumber
			+ "</td><td>群发车辆拍照</td><td>"
			+ dateString
			+ "</td><td>发送 成功</td></tr>";

	$("#cmdTable").append(commandContent1);

	$
			.ajax({
				url : "/808FrontProject/endControl/nImmediatelyPhotoAction",
				data : {
					carNumbers : carNumbers,
					channelId : channelId,
					shotNumber : shotNumber,
					shotInterval : shotInterval
				},
				dataType : "json",
				type : "post",
				success : function(data) {
					var simCardStrings = "";
					var commandSerialNumbers = "";
					for (var i = 0; i < data.length - 1; i++) {
						simCardStrings += data[i].simCardString + ",";
						commandSerialNumbers += data[i].commandSerialNumber
								+ ",";
					}
					simCardStrings += data[data.length - 1].simCardString;
					commandSerialNumbers += data[data.length - 1].commandSerialNumber;

					var nImmediatelyPhotoInterval = setInterval(
							function() {
								if (commandResults == 0) {
									queryCommandResults(simCardStrings,
											commandSerialNumbers);
								} else {
									var dateString2 = getFormatDate();
									var commandContent2 = "<tr onmouseover='styleCellOnMouseOver(this, cellHoverColor)' onmouseout='styleCellOnMouseOut(this,cellDefaultColor)'><td>"
											+ carNumber
											+ "</td><td>群发车辆拍照</td><td>"
											+ dateString2
											+ "</td><td>执行成功</td></tr>";
									$("#cmdTable").append(commandContent2);
									initCommandResults();
									clearInterval(nImmediatelyPhotoInterval);

								}

							}, 3000);

				}
			});
}

function nPositionEnquire() {

	initCommandResults();
	clearInterval(nPositionEnquireInterval);

	var carNumberArray = new Array();
	$("[name = group]:checkbox").each(function() {
		if ($(this).is(":checked")) {
			carNumberArray.push($(this).attr("value"));
		}
	});

	if (carNumberArray.length > 0) {
		var carNumbers = "";

		for (var i = 0; i < carNumberArray.length - 1; i++) {
			carNumbers += carNumberArray[i] + ","
		}
		carNumbers += carNumberArray[carNumberArray.length - 1];

		$
				.ajax({
					url : "/808FrontProject/endControl/nPositionEnquireAction",
					data : {
						carNumbers : carNumbers
					},
					dataType : "json",
					type : "post",
					success : function(data) {

						var simCardStrings = "";
						var commandSerialNumbers = "";
						for (var i = 0; i < data.length - 1; i++) {
							simCardStrings += data[i].simCardString + ",";
							commandSerialNumbers += data[i].commandSerialNumber
									+ ",";
						}
						simCardStrings += data[data.length - 1].simCardString;
						commandSerialNumbers += data[data.length - 1].commandSerialNumber;

						nPositionEnquireInterval = setInterval(function() {
							if (commandResults == 0)
								queryCommandResults(simCardStrings,
										commandSerialNumbers);
							else
								clearInterval(nPositionEnquireInterval);
						}, 1000);
						setTimeout(function() {
							clearInterval(nPositionEnquireInterval);
						}, 15000);
					}
				});
	}

}
