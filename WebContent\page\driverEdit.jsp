<%@page import="cn.edu.ntu.entity.common.DriverInfo"%>
<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@taglib prefix="s" uri="/struts-tags"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>司乘人员管理</title>

<link rel="stylesheet" type="text/css"
	href="/808FrontProject/css/user.css">
<link rel="stylesheet" type="text/css"
	href="/808FrontProject/css/viewer.css">

<script type="text/javascript" src="/808FrontProject/js/lib.jquery.js"></script>
<script type="text/javascript" src="/808FrontProject/js/ajaxfileupload.js"></script>
<script type="text/javascript" src="/808FrontProject/js/viewer.js"></script>

<!-- date component [s] -->
<script type="text/javascript"
	src="/808FrontProject/js/lib.jsdate/jscal2.js"></script>
<script type="text/javascript"
	src="/808FrontProject/js/lib.jsdate/en.js"></script>
<link rel="stylesheet" type="text/css"
	href="/808FrontProject/js/lib.jsdate/jscal2.css">
<link rel="stylesheet" type="text/css"
	href="/808FrontProject/js/lib.jsdate/border-radius.css">
<link rel="stylesheet" type="text/css"
	href="/808FrontProject/js/lib.jsdate/steel.css">
<!-- date component [e] -->

<script type="text/javascript">

	$(window).load(
			function() {
				//date componenent
				var cal = Calendar.setup({
					onSelect : function(cal) {
						cal.hide()
					},
					showTime : true
				});
				cal.manageFields("bornDateBtn", "bornDate", "%Y-%m-%d");
				cal.manageFields("licenseExpireDateBtn",
						"licenseExpireDate", "%Y-%m-%d");
				cal.manageFields("receiveLicenseDateBtn", "receiveLicenseDate",
						"%Y-%m-%d");
				cal.manageFields("firstCertificateDateBtn",
						"firstCertificateDate", "%Y-%m-%d");
				cal.manageFields("certificateDeliveryDateBtn",
						"certificateDeliveryDate", "%Y-%m-%d");
				cal.manageFields("certificateExpireDateBtn",
						"certificateExpireDate", "%Y-%m-%d");
				
				if($("#driverModifyFlag").val()==1){
					$("#driverId").focus(function(){
						this.blur();
					});
				}else{
					$("#driverId").blur(function(){
						validateDriverId(this);
					});
				}
				
				jQuery.browser={
						function(){
							jQuery.browser.msie=false;
							jQuery.browser.version=0;
							if(navigator.userAgent.match(/MSIE ([0-9]+)./)){
								jQuery.browser.msie=true;
								jQuery.browser.version=RegExp.$1;
							}
						}
						
				};
				
				$("#viewPhotoTd").viewer();

			});
	
	function validateDriverId(field){
		if($.trim(field.value)==""){
			$("#validationResultImage").attr("src","/808FrontProject/image/false.png");
			$("#storeButton").attr("disabled","disabled");
		}else{
			$.ajax({
				url:"/808FrontProject/common/validateDriverIdAction",
			    data:{
			    	driverId:$.trim(field.value)
			    },
			    type:"post",
			    dataType:"json",
			    success:function(data){
			    	if(data=="司机编号已存在"){
						$("#validationResultImage").attr("src","/808FrontProject/image/false.png");
						$("#file").attr("disabled","disabled");
						$("#storeButton").attr("disabled","disabled");
					}else{
						$("#validationResultImage").attr("src","/808FrontProject/image/true.png");
						$("#file").removeAttr("disabled");
						$("#storeButton").removeAttr("disabled");
						$.ajax({
							url:"/808FrontProject/driver/addStoreAction.action",
							data:{"driverId":$.trim(field.value)},
							type:"post"
						});
					}
			    }
			
			});
		}
		
	}
	
	function getBornDate(){
		var idno=$("input[name='driverInfo.identificationCardNumber']").val();
		if($.trim(idno)!=""){
			var reg = /^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$|^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/;
			if (!reg.test($.trim(idno))) {
				alert("身份证号码格式不对");
				dICard.focus();
				return false;
			}
			var birthdayno,birthdaynotemp;
		    if(idno.length==18){
		        birthdayno=idno.substring(6,14);
		    }else if(idno.length==15){
		        birthdaynotemp=idno.substring(6,12);
		        birthdayno="19"+birthdaynotemp;
		    }
		    
		    var birthday=birthdayno.substring(0,4)+"-"+birthdayno.substring(4,6)+"-"+birthdayno.substring(6,8);
		    $("input[name='driverInfo.bornDate']").val(birthday);
		}
	    
	}

	function uploadCertificatePhoto() {
		var file = $("#file");
		if (file.val() == "") {
			file.focus();
			return false;
		}
		
		var filepath = $("#file").val();
        var extStart = filepath.lastIndexOf(".");
        var ext = filepath.substring(extStart, filepath.length).toUpperCase();
        if (ext != ".BMP" && ext != ".PNG" && ext != ".GIF" && ext != ".JPG" && ext != ".JPEG") {
            alert("图片限于bmp,png,gif,jpeg,jpg格式!");
            file.focus();
            return false;
        }
        
		var filesize;
	    if ($.browser.msie) {
			var img = new Image();
			img.src = file.value;
			filesize=img.fileSize;
		}else{
			var files =file.prop('files');
			filesize = files[0].size;
		}
	    
	    if(filesize>2*1024*1024){
	    	alert("图片大小应小于2MB!");
	    	file.focus();
	    	return false;
	    }

		var driverId = $.trim($("input[name='driverInfo.driverId']").val());

		$
				.ajaxFileUpload({
					url : "/808FrontProject/common/uploadDriverCertificatePhotoAction.action",
					secureuri : false,
					fileElementId : "file",
					dataType : "json",
					data : {
						"driverId" : driverId
					},
					success : function(data) {

						var src = "/808FrontProject/common/getCertificatePhotoAction?driverId="	+ driverId;
						$("#certificatePhoto").attr("src", src);

					},
					error:function (XMLHttpRequest, textStatus, errorThrown) { 
						$("#certificatePhoto").attr("src", "/808FrontProject/image/failedToUpload.png");
						$("#certificatePhoto").height(50);
						$("#certificatePhoto").width(50);
					} 
				});

		return false;
	}

	function store() {

		var dId = $("input[name='driverInfo.driverId']");
		var dPassword = $("input[name='driverInfo.driverPassword']");
		if (dId.val() == "") {
			alert("请输入司乘人员编号！");
			dId.focus();
			return false;
		}

		var dName = $("input[name='driverInfo.driverName']");
		if (dName.val() == "") {
			alert("请输入司乘人员姓名！");
			dName.focus();
			return false;
		}
		
		var dCYZGZBH = $("input[name='driverInfo.qualificationCertificateId']");

		if (dCYZGZBH.val() == "") {
			alert("请输入从业资格证编号！");
			dCYZGZBH.focus();
			return false;
		}

		$("#modifyDriverInfoForm").attr("action",
				"/808FrontProject/driver/editStoreAction.action").submit();
		
	}
	
	function cancle(){
		
		if($("#driverModifyFlag").val()==0){
		
		var driverId = $.trim($("input[name='driverInfo.driverId']").val());
		$.ajax({
			url:"/808FrontProject/driver/deleteAction",
			data:{
				"driverId":driverId
			},
			type:"post",
			success:function(data){
				history.go(-1);
			}
		});
		
		}
		
	}
	
</script>
</head>

<body>
	<span class="title1">司机信息管理</span>
	<s:form id="modifyDriverInfoForm" theme="simple">
		<s:if test='null == driverInfo'>
			<s:hidden id="driverModifyFlag" value="0">
			</s:hidden>
		</s:if>
		<s:else>
			<s:hidden id="driverModifyFlag" value="1">
			</s:hidden>
		</s:else>
		<table class="tableCommon">
			<tr>
				<td colspan="6" class="title2 thColor">基本信息</td>
			</tr>
			<tr>
			<!-- 
				<td width="150px">司乘人员分类</td>
				<td><s:select name="driverInfo.driverType"
						list="#{1:'司机',2:'乘务员'}" listKey="value" listValue="value"
						headerKey="0" headerValue="请选择"></s:select> *</td>
			 -->
				<td width="150px">编号</td>
				<td><s:textfield name="driverInfo.driverId" id="driverId"></s:textfield> *<img id="validationResultImage" 
						src="/808FrontProject/image/blank.png"></td>
			<!-- 	<td width="150px">密码</td>
				<td><s:textfield name="driverInfo.driverPassword"></s:textfield>
					*</td>
			 -->
			    <td>姓名</td>
				<td><s:textfield name="driverInfo.driverName"></s:textfield> *</td>
				<td>性别</td>
				<td><s:select name="driverInfo.driverSex" list="#{1:'男',2:'女'}"
						listKey="value" listValue="value" headerKey="" headerValue="请选择"></s:select>
					</td>

			</tr>
			<!-- 
			<tr>
			 	<td>GUID</td>
				<td><s:textfield name="driverInfo.guid" id="guid"></s:textfield>&nbsp;<input
					type="button" class="buttonSmallStyle" value="生成"
					onclick="generateGuid()" /> *</td>
				
			</tr>
			 -->
			<tr>
				<td>身份证号</td>
				<td><s:textfield name="driverInfo.identificationCardNumber" onblur="getBornDate()"></s:textfield>
					</td>
				<td>出生日期</td>
				<td><s:textfield name="driverInfo.bornDate" id='bornDate'
						style='width: 100px; margin-top: 2px'></s:textfield>
					<button id="bornDateBtn" type="button">...</button> </td>
				<td>移动电话</td>
				<td><s:textfield name="driverInfo.contactPhoneNumber"></s:textfield></td>
			<!-- 
				<td>联系电话</td>
				<td><s:textfield name="driverInfo.contactNumber"></s:textfield>
					*</td>
			 -->
			</tr>
			<!-- 
			<tr>
				
			
				<td>籍贯</td>
				<td><s:textfield name="driverInfo.nativePlace"></s:textfield> *</td>
				<td>家庭住址</td>
				<td><s:textfield name="driverInfo.driverAddress"></s:textfield>
					*</td>
			
			</tr>
			 -->
			<tr>
			    
				<td>准驾机型</td>
				<td><s:select name="driverInfo.driverLicenseClass"
						list="#{1:'A1',2:'A2',3:'B1',4:'B2',5:'C1',6:'C2'}"
						listKey="value" listValue="value" headerKey="0" headerValue="请选择"></s:select>
					</td>
				<td>驾驶证过期日期</td>
				<td><s:textfield name="driverInfo.licenseExpireDate"
						id="licenseExpireDate" style='width: 100px; margin-top: 2px'></s:textfield>
					<button id="licenseExpireDateBtn" type="button">...</button> </td>
				<td>驾驶证领取日期</td>
				<td><s:textfield name="driverInfo.receiveLicenseDate"
						id="receiveLicenseDate" style='width: 100px; margin-top: 2px'></s:textfield>
					<button id="receiveLicenseDateBtn" type="button">...</button> </td>
			</tr>
			<tr>
				<td colspan="6" id="vehicleTag" class="title2 thColor">从业资格证信息</td>
			</tr>

			<tr>
				<td>初次发证日期</td>
				<td><s:textfield name="driverInfo.firstCertificateDate"
						id="firstCertificateDate" style='width: 100px; margin-top: 2px'></s:textfield>
					<button id="firstCertificateDateBtn" type="button">...</button> </td>
				<td>类别</td>
				<td><s:select name="driverInfo.certificateType"
						list="#{1:'旅客运输',2:'危货运输',3:'出租客运',4:'普通货运',5:'大客车旅客运输'}"
						listKey="value" listValue="value" headerKey="0" headerValue="请选择"></s:select>
					</td>
				<td>从业资格证编号</td>
				<td><s:textfield name="driverInfo.qualificationCertificateId"></s:textfield>
					*</td>
			</tr>
			<tr>
				<td>发证机构</td>
				<td><s:textfield name="driverInfo.certificateOrganization"></s:textfield>
					</td>
				<td>最后发证时间</td>
				<td><s:textfield name="driverInfo.certificateDeliveryDate"
						id="certificateDeliveryDate" style='width: 100px; margin-top: 2px'></s:textfield>
					<button id="certificateDeliveryDateBtn" type="button">...</button>
					</td>
				<td>过期时间</td>
				<td><s:textfield name="driverInfo.certificateExpireDate"
						id="certificateExpireDate" style='width: 100px; margin-top: 2px'></s:textfield>
					<button id="certificateExpireDateBtn" type="button">...</button> </td>
			</tr>
			<tr>
				<td>监管机构</td>
				<td><s:textfield name="driverInfo.regulatoryAgencyName"></s:textfield>
					</td>
				<td>监督电话</td>
				<td><s:textfield name="driverInfo.regulatoryTelephoneNumber"></s:textfield> </td>
				<td>上传资格证照片</td>
				<td>
				    <input type="file"	style="width: 200px; background-color: #CAE1FF" id="file" name="file" onchange="return uploadCertificatePhoto();"/> 
				    <!-- 
				    <input type="button" class="buttonSmallStyle" name="" value="上传" onclick="return ajaxFileUpload();" />
				     -->
				</td>
			</tr>
			<tr>
				<td id="viewPhotoTd" colspan="6">
				<s:if test='null == driverInfo'>
			       <img id="certificatePhoto" height="200px" width="200px"	src="../image/notUploaded.png" />
				
		        </s:if>
		        <s:else>
			       <img id="certificatePhoto" height="200px" width="200px" src="/808FrontProject/common/getCertificatePhotoAction?driverId=<s:property value="driverInfo.driverId"/>"/>
		        </s:else>
				</td>
			</tr>
			<tr>
				<td colspan="6" style="padding-left: 0px; text-align: center;"><input
					class="buttonStyle" type="button" value="保存" id="storeButton" onclick="store();" />&nbsp;<input
					class="buttonStyle" type="button" value="返回"
					onclick="cancle();" /></td>
			</tr>
		</table>
	</s:form>

</body>
</html>