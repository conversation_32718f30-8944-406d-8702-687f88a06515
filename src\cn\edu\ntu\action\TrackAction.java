package cn.edu.ntu.action;

/**
 * <AUTHOR>
 * @date 2016.4.8
 */

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.opensymphony.xwork2.ActionSupport;
import com.sun.org.apache.regexp.internal.recompile;

import cn.edu.ntu.entity.common.SearchTrackResult;
import cn.edu.ntu.entity.common.VehicleGpsInfo;
import cn.edu.ntu.entity.common.VehicleGpsInfoForHistory;
import cn.edu.ntu.service.common.implement.TrackInfoServiceImpl;
import cn.edu.ntu.service.common.implement.VehicleGpsInfoServiceImpl;
import cn.edu.ntu.service.common.interfaces.TrackInfoService;
import net.sf.json.JSONArray;

@Controller
@Scope("prototype")
public class TrackAction extends ActionSupport {

	private VehicleGpsInfo vehicleGpsInfo;
	private String carNumber;
	private String dateStart;
	private String dateEnd;
	private boolean findDriver;
	private SearchTrackResult result;
	
	private TrackInfoService trackInfoService;
	
	public VehicleGpsInfo getVehicleGpsInfo() {
		return vehicleGpsInfo;
	}

	public void setVehicleGpsInfo(VehicleGpsInfo vehicleGpsInfo) {
		this.vehicleGpsInfo = vehicleGpsInfo;
	}

	public String getCarNumber() {
		return carNumber;
	}

	public void setCarNumber(String carNumber) {
		this.carNumber = carNumber;
	}

	public String getDateStart() {
		return dateStart;
	}

	public void setDateStart(String dateStart) {
		this.dateStart = dateStart;
	}

	public String getDateEnd() {
		return dateEnd;
	}

	public void setDateEnd(String dateEnd) {
		this.dateEnd = dateEnd;
	}
	
    public boolean isFindDriver() {
		return findDriver;
	}

	public void setFindDriver(boolean findDriver) {
		this.findDriver = findDriver;
	}

	public SearchTrackResult getResult() {
		return result;
	}

	public void setResult(SearchTrackResult result) {
		this.result = result;
	}

	@Autowired
	public void setTrackInfoService(TrackInfoService trackInfoService) {
		this.trackInfoService = trackInfoService;
	}

	@Override
	public String execute() throws Exception {
		if(findDriver)
			result = trackInfoService.searchTrackWithDriverName(carNumber, dateStart, dateEnd);
		else
		    result = trackInfoService.searchTrack(carNumber, dateStart, dateEnd);
		return SUCCESS;
	}
	
}
