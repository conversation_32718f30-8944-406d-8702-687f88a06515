<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE struts PUBLIC
	"-//Apache Software Foundation//DTD Struts Configuration 2.3//EN"
	"http://struts.apache.org/dtds/struts-2.3.dtd">

<struts>

	<constant name="struts.devMode" value="true" />
	
	<constant name="struts.objectFactory" value="org.apache.struts2.spring.StrutsSpringObjectFactory" />
	
	<package name="vehicle" namespace="/vehicle" extends="struts-default">
	    <action name="listAction" class="vehicleAction" method="list">
	        <result>/page/vehicleManage.jsp</result>
	    </action>
	    
	    <action name="addAction" class="vehicleAction" method="add">
	        <result>/page/vehicleEdit.jsp</result>
	    </action>
	    
	    <action name="addStoreAction" class="vehicleAction" method="addStore">
	        <result type="redirect">listAction.action</result>
	    </action>
	    
	    <action name="editAction" class="vehicleAction" method="edit">
	        <result>/page/vehicleEdit.jsp</result>
	    </action>
	    
	    <action name="editStoreAction" class="vehicleAction" method="editStore">
	        <result type="redirect">listAction.action</result>
	    </action>
	    
	    <action name="deleteAction" class="vehicleAction" method="delete">
	        <result type="redirect">listAction.action</result>
	    </action>
	    
	    <action name="queryAction" class="vehicleAction" method="query">
	         <result>/page/vehicleManage.jsp</result>
	    </action>
	    
	</package>

</struts>