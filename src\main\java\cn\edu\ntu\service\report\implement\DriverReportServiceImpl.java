package cn.edu.ntu.service.report.implement;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.struts2.components.Else;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.mysql.jdbc.Driver;

import cn.edu.ntu.dao.common.interfaces.DriverInfoDao;
import cn.edu.ntu.dao.common.interfaces.VehicleGpsInfoDao;
import cn.edu.ntu.dao.common.interfaces.VehicleInfoDao;
import cn.edu.ntu.dao.report.interfaces.DriverReportDao;
import cn.edu.ntu.entity.common.DriverInfo;
import cn.edu.ntu.entity.common.VehicleGpsInfo;
import cn.edu.ntu.entity.common.VehicleGpsInfoForHistory;
import cn.edu.ntu.entity.common.VehicleInfo;
import cn.edu.ntu.entity.report.DriverReport;
import cn.edu.ntu.message.req.PositionReport;
import cn.edu.ntu.service.common.interfaces.PositionReportRedisService;
import cn.edu.ntu.service.common.interfaces.TrackInfoService;
import cn.edu.ntu.service.report.interfaces.DriverReportService;

/**
 * Driver Report Service Implementation
 *
 * <AUTHOR>
 * @date 2017-03-30 02:27:39
 */
@Service
public class DriverReportServiceImpl implements DriverReportService {
	
	@Autowired
	private DriverReportDao driverReportDao;
	@Autowired 
	private VehicleInfoDao vehicleInfoDao;
    @Autowired	
	private VehicleGpsInfoDao vehicleGpsInfoDao;
    
    @Autowired
    private TrackInfoService trackInfoService;

	@Override
	public List<DriverReport> getDriverReportByDriverName(String driverName, String startTimeString, String endTimeString) throws Exception{
		List<DriverReport> driverReportList=driverReportDao.getDriverReportByDriverName(driverName,startTimeString,endTimeString);
		
        SimpleDateFormat simpleDateFormat=new SimpleDateFormat("yyyy-MM-dd HH:mm");
		
		Date startTime=simpleDateFormat.parse(startTimeString);
		Date endTime=simpleDateFormat.parse(endTimeString);
		
		Date currentTime=new Date();
		if(endTime.getTime()>currentTime.getTime())
		    endTime=currentTime;
		// If query end time is later than current time, use current time as query end time
		
		DriverReport queryDriverReport=new DriverReport();
		queryDriverReport.setStartTime(startTime);
		queryDriverReport.setEndTime(endTime);
		
        List<DriverReport> newDriverReportList=new ArrayList<DriverReport>();
		
		for(DriverReport driverReport:driverReportList){
	        Date newStartTime=driverReport.getStartTime();
	        Date newEndTime=driverReport.getEndTime();
        	DriverReport newDriverReport=getOverlap(queryDriverReport, driverReport);
        	if(newDriverReport!=null)
        		newDriverReportList.add(newDriverReport);
        }
		
		for(DriverReport driverReport:newDriverReportList){
			int driveTimeInMinutes=(int) ((driverReport.getEndTime().getTime()-driverReport.getStartTime().getTime())/1000/60)+1;
			String driveTimeString;
			if (driveTimeInMinutes < 60)
				driveTimeString = driveTimeInMinutes + "��";
			else if (driveTimeInMinutes < 60 * 24)
				driveTimeString = (int) (driveTimeInMinutes / 60) + "ʱ" + driveTimeInMinutes % 60 + "��";
			else
				driveTimeString = (int) (driveTimeInMinutes / 60 / 24) + "��"
						+ (int) ((driveTimeInMinutes % (60 * 24)) / 60) + "ʱ" 
						+ driveTimeInMinutes % (60 * 24) % 60+ "��";
			driverReport.setDriveTimeString(driveTimeString);
			
			int startDistance=findDistance(driverReport.getCarNumber(),driverReport.getStartTime());
			int endDistance=findDistance(driverReport.getCarNumber(),driverReport.getEndTime());
			
			if(startDistance!=0&&endDistance!=0)
			    driverReport.setDriveDistance(endDistance-startDistance);
		}
		
		return newDriverReportList;
//		for(DriverReport driverReport:driverReportListWhenCardOut){
//			driverReport.setDriveDistance(driverReport.getEndDistance()-driverReport.getStartDistance());
//			int driveTimeInMinutes=(int) ((driverReport.getEndTime().getTime()-driverReport.getStartTime().getTime())/1000/60)+1;
//			String driveTimeString;
//			if(driveTimeInMinutes<60)
//				driveTimeString=driveTimeInMinutes+"��";
//			else if(driveTimeInMinutes<60*24)
//				driveTimeString=(int)(driveTimeInMinutes/60)+"ʱ"+driveTimeInMinutes%60+"��";
//			else
//				driveTimeString=(int)(driveTimeInMinutes/60/24)+"��"+(int)((driveTimeInMinutes%(60*24))/60)+"ʱ"+driveTimeInMinutes%(60*24)%60+"��";
//			driverReport.setDriveTimeString(driveTimeString);
//		}
//		
//		List<DriverReport> driverReportListWhenCardIn=driverReportDao.getDriverReportByDriverName(driverName, startTime);
//		
//		List<VehicleInfo> vehicleInfoList=vehicleInfoDao.listVehiclesByDriverReportList(driverReportListWhenCardIn);
//		Map<String,VehicleInfo> carNumber2VehicleInfoMap=new HashMap<String,VehicleInfo>();
//		for(VehicleInfo vehicleInfo:vehicleInfoList){
//			carNumber2VehicleInfoMap.put(vehicleInfo.getCarNumber(),vehicleInfo);
//		}
//		
//		for(DriverReport driverReport:driverReportListWhenCardIn){
//			for(VehicleInfo vehicleInfo:vehicleInfoList){
//				if(driverReport.getCarNumber().equals(vehicleInfo.getCarNumber())){
//					VehicleGpsInfo gpsInfo=vehicleGpsInfoDao.find(driverReport.getCarNumber(),carNumber2VehicleInfoMap);
//					driverReport.setDriveDistance(gpsInfo.getTotalDistance()-driverReport.getStartDistance());
//					int driveTimeInMinutes=(int) ((System.currentTimeMillis()-driverReport.getStartTime().getTime())/1000/60)+1;
//					String driveTimeString;
//					if(driveTimeInMinutes<60)
//						driveTimeString=driveTimeInMinutes+"��";
//					else if(driveTimeInMinutes<60*24)
//						driveTimeString=(int)(driveTimeInMinutes/60)+"ʱ"+driveTimeInMinutes%60+"��";
//					else
//						driveTimeString=(int)(driveTimeInMinutes/60/24)+"��"+(int)((driveTimeInMinutes%(60*24))/60)+"ʱ"+driveTimeInMinutes%(60*24)%60+"��";
//					driverReport.setDriveTimeString(driveTimeString);
//				}
//			}
//		}
//		
//		List<DriverReport> driverReportList=new ArrayList<DriverReport>();
//		driverReportList.addAll(driverReportListWhenCardOut);
//		driverReportList.addAll(driverReportListWhenCardIn);
//		
//		return driverReportList;
	}

	@Override
	public List<DriverReport> getDriverReportByCarNumber(String carNumber, String startTimeString, String endTimeString)
			throws Exception {
		
		List<DriverReport> driverReportList=driverReportDao.getDriverReportByCarNumber(carNumber, startTimeString, endTimeString);
		
		SimpleDateFormat simpleDateFormat=new SimpleDateFormat("yyyy-MM-dd HH:mm");
		
		Date startTime=simpleDateFormat.parse(startTimeString);
		Date endTime=simpleDateFormat.parse(endTimeString);
		
		Date currentTime=new Date();
		if(endTime.getTime()>currentTime.getTime())
		    endTime=currentTime;
		//����ѯ����ʱ����ڵ�ǰʱ�䣬���õ�ǰʱ������ѯ����ʱ��
		
		DriverReport queryDriverReport=new DriverReport();
		queryDriverReport.setStartTime(startTime);
		queryDriverReport.setEndTime(endTime);
		
        List<DriverReport> newDriverReportList=new ArrayList<DriverReport>();
		
		for(DriverReport driverReport:driverReportList){
	        Date newStartTime=driverReport.getStartTime();
	        Date newEndTime=driverReport.getEndTime();
        	DriverReport newDriverReport=getOverlap(queryDriverReport, driverReport);
        	if(newDriverReport!=null)
        		newDriverReportList.add(newDriverReport);
        }
		
		for(DriverReport driverReport:newDriverReportList){
			int driveTimeInMinutes=(int) ((driverReport.getEndTime().getTime()-driverReport.getStartTime().getTime())/1000/60)+1;
			String driveTimeString;
			if (driveTimeInMinutes < 60)
				driveTimeString = driveTimeInMinutes + "��";
			else if (driveTimeInMinutes < 60 * 24)
				driveTimeString = (int) (driveTimeInMinutes / 60) + "ʱ" + driveTimeInMinutes % 60 + "��";
			else
				driveTimeString = (int) (driveTimeInMinutes / 60 / 24) + "��"
						+ (int) ((driveTimeInMinutes % (60 * 24)) / 60) + "ʱ" 
						+ driveTimeInMinutes % (60 * 24) % 60+ "��";
			driverReport.setDriveTimeString(driveTimeString);
			
			int startDistance=findDistance(driverReport.getCarNumber(),driverReport.getStartTime());
			int endDistance=findDistance(driverReport.getCarNumber(),driverReport.getEndTime());
			
			if(startDistance!=0&&endDistance!=0)
			    driverReport.setDriveDistance(endDistance-startDistance);
		}
		
		return newDriverReportList;
		
		
//        List<DriverReport> driverReportListWhenCardOut=driverReportDao.getDriverReportByCarNumberWhenCardOut(carNumber,startTime,endTime);
//		
//		for(DriverReport driverReport:driverReportListWhenCardOut){
//			driverReport.setDriveDistance(driverReport.getEndDistance()-driverReport.getStartDistance());
//			int driveTimeInMinutes=(int) ((driverReport.getEndTime().getTime()-driverReport.getStartTime().getTime())/1000/60)+1;
//			String driveTimeString;
//			if(driveTimeInMinutes<60)
//				driveTimeString=driveTimeInMinutes+"��";
//			else if(driveTimeInMinutes<60*24)
//				driveTimeString=(int)(driveTimeInMinutes/60)+"ʱ"+driveTimeInMinutes%60+"��";
//			else
//				driveTimeString=(int)(driveTimeInMinutes/60/24)+"��"+(int)((driveTimeInMinutes%(60*24))/60)+"ʱ"+driveTimeInMinutes%(60*24)%60+"��";
//			driverReport.setDriveTimeString(driveTimeString);
//		}
//		
//		List<DriverReport> driverReportListWhenCardIn=driverReportDao.getDriverReportByCarNumberWhenCardIn(carNumber, startTime,endTime);
//		
//		List<VehicleInfo> vehicleInfoList=vehicleInfoDao.listVehiclesByDriverReportList(driverReportListWhenCardIn);
//		Map<String,VehicleInfo> carNumber2VehicleInfoMap=new HashMap<String,VehicleInfo>();
//		for(VehicleInfo vehicleInfo:vehicleInfoList){
//			carNumber2VehicleInfoMap.put(vehicleInfo.getCarNumber(),vehicleInfo);
//		}
//		
//		for(DriverReport driverReport:driverReportListWhenCardIn){
//			for(VehicleInfo vehicleInfo:vehicleInfoList){
//				if(driverReport.getCarNumber().equals(vehicleInfo.getCarNumber())){
//					VehicleGpsInfo gpsInfo=vehicleGpsInfoDao.find(driverReport.getCarNumber(),carNumber2VehicleInfoMap);
//					driverReport.setDriveDistance(gpsInfo.getTotalDistance()-driverReport.getStartDistance());
//					int driveTimeInMinutes=(int) ((System.currentTimeMillis()-driverReport.getStartTime().getTime())/1000/60)+1;
//					String driveTimeString;
//					if(driveTimeInMinutes<60)
//						driveTimeString=driveTimeInMinutes+"��";
//					else if(driveTimeInMinutes<60*24)
//						driveTimeString=(int)(driveTimeInMinutes/60)+"ʱ"+driveTimeInMinutes%60+"��";
//					else
//						driveTimeString=(int)(driveTimeInMinutes/60/24)+"��"+(int)((driveTimeInMinutes%(60*24))/60)+"ʱ"+driveTimeInMinutes%(60*24)%60+"��";
//					driverReport.setDriveTimeString(driveTimeString);
//				}
//			}
//		}
//		
//		List<DriverReport> driverReportList=new ArrayList<DriverReport>();
//		driverReportList.addAll(driverReportListWhenCardOut);
//		driverReportList.addAll(driverReportListWhenCardIn);
//		
//		return driverReportList;
		
	}
	
	private int findDistance(String carNumber,Date time) {
		
		int periodInFiveMinites=5*60*1000;
		Date startTime=new Date(time.getTime()-periodInFiveMinites);
		Date endTime=new Date(time.getTime()+periodInFiveMinites);
		
		SimpleDateFormat simpleDateFormat=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		String startTimeString=simpleDateFormat.format(startTime);
		String endTimeString=simpleDateFormat.format(endTime);
		
		List<VehicleGpsInfoForHistory> list=trackInfoService.searchTrack(carNumber, startTimeString, endTimeString).getGpsInfoList();
		
		int totalDistance=list.get(list.size()/2).getTotalDistance();
		
		return totalDistance;
	}

	private DriverReport getOverlap(DriverReport driverReport,DriverReport anotherDriverReport){
		Date startTime=driverReport.getStartTime();
		Date endTime=driverReport.getEndTime();
		Date anotherStartTime=anotherDriverReport.getStartTime();
		Date anotherEndTime=anotherDriverReport.getEndTime();
		
		Date newStartTime = null;
		Date newEndTime = null;
		
		if(startTime.getTime()>anotherStartTime.getTime()&&endTime.getTime()<anotherEndTime.getTime()){
			newStartTime=startTime;
			newEndTime=endTime;
		}else if(startTime.getTime()<anotherStartTime.getTime()&&endTime.getTime()>anotherEndTime.getTime()){
			newStartTime=anotherStartTime;
			newEndTime=anotherEndTime;
		}else if(startTime.getTime()<anotherStartTime.getTime()&&endTime.getTime()<anotherEndTime.getTime()&&endTime.getTime()>anotherStartTime.getTime()){
			newStartTime=anotherStartTime;
			newEndTime=endTime;
		}else if(startTime.getTime()>anotherStartTime.getTime()&&endTime.getTime()>anotherEndTime.getTime()&&startTime.getTime()<anotherEndTime.getTime()){
			newStartTime=startTime;
			newEndTime=anotherEndTime;
		}
		
		if (newStartTime != null && newEndTime != null) {
			DriverReport newDriverReport = new DriverReport();
			newDriverReport.setStartTime(newStartTime);
			newDriverReport.setEndTime(newEndTime);
			
			if(driverReport.getCarNumber()!=null&&driverReport.getDriverName()!=null){
				newDriverReport.setCarNumber(driverReport.getCarNumber());
				newDriverReport.setDriverName(driverReport.getDriverName());
			}
			
			if(anotherDriverReport.getCarNumber()!=null&&anotherDriverReport.getDriverName()!=null){
				newDriverReport.setCarNumber(anotherDriverReport.getCarNumber());
				newDriverReport.setDriverName(anotherDriverReport.getDriverName());
			}

			return newDriverReport;
		} else {
			return null;
		}
	}

}
