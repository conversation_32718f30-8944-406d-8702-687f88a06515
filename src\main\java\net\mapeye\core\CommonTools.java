package net.mapeye.core;

import java.util.Calendar;

/**
 * 通用工具类
 * 提供日期处理、格式化等常用功能
 * 
 * <AUTHOR>
 * @date 2025-07-03
 */
public class CommonTools {

    /**
     * 获取指定月份的天数
     * @param month 月份 (1-12)
     * @return 该月份的天数
     */
    public static int getDays(int month) {
        // 使用当前年份来计算，如果需要考虑闰年，可以传入年份参数
        Calendar calendar = Calendar.getInstance();
        int year = calendar.get(Calendar.YEAR);
        return getDays(year, month);
    }
    
    /**
     * 获取指定年月的天数
     * @param year 年份
     * @param month 月份 (1-12)
     * @return 该月份的天数
     */
    public static int getDays(int year, int month) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.MONTH, month - 1); // Calendar月份从0开始
        return calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
    }

    /**
     * 获取标准化的月份字符串 (两位数格式)
     * @param month 月份 (1-12)
     * @return 格式化的月份字符串 (01-12)
     */
    public static String getMonthNormal(int month) {
        if (month < 1 || month > 12) {
            throw new IllegalArgumentException("月份必须在1-12之间");
        }
        return String.format("%02d", month);
    }

    /**
     * 获取标准化的日期字符串 (两位数格式)
     * @param day 日期 (1-31)
     * @return 格式化的日期字符串 (01-31)
     */
    public static String getDayNormalOfMonth(int day) {
        if (day < 1 || day > 31) {
            throw new IllegalArgumentException("日期必须在1-31之间");
        }
        return String.format("%02d", day);
    }

    /**
     * 获取标准化的日期字符串
     * @param year 年份
     * @param month 月份 (1-12)
     * @param day 日期 (1-31)
     * @param separator 分隔符
     * @return 格式化的日期字符串
     */
    public static String getDateNormal(int year, int month, int day, String separator) {
        // 处理日期溢出的情况
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.MONTH, month - 1); // Calendar月份从0开始
        calendar.set(Calendar.DAY_OF_MONTH, day);
        
        int actualYear = calendar.get(Calendar.YEAR);
        int actualMonth = calendar.get(Calendar.MONTH) + 1; // 转换回1-12
        int actualDay = calendar.get(Calendar.DAY_OF_MONTH);
        
        return String.format("%04d%s%02d%s%02d", 
            actualYear, separator, actualMonth, separator, actualDay);
    }

    /**
     * 获取标准化的日期字符串 (默认使用"-"作为分隔符)
     * @param year 年份
     * @param month 月份 (1-12)
     * @param day 日期 (1-31)
     * @return 格式化的日期字符串
     */
    public static String getDateNormal(int year, int month, int day) {
        return getDateNormal(year, month, day, "-");
    }

    /**
     * 获取标准化的年份字符串 (四位数格式)
     * @param year 年份
     * @return 格式化的年份字符串
     */
    public static String getYearNormal(int year) {
        return String.format("%04d", year);
    }

    /**
     * 获取标准化的小时字符串 (两位数格式)
     * @param hour 小时 (0-23)
     * @return 格式化的小时字符串 (00-23)
     */
    public static String getHourNormal(int hour) {
        if (hour < 0 || hour > 23) {
            throw new IllegalArgumentException("小时必须在0-23之间");
        }
        return String.format("%02d", hour);
    }

    /**
     * 获取标准化的分钟字符串 (两位数格式)
     * @param minute 分钟 (0-59)
     * @return 格式化的分钟字符串 (00-59)
     */
    public static String getMinuteNormal(int minute) {
        if (minute < 0 || minute > 59) {
            throw new IllegalArgumentException("分钟必须在0-59之间");
        }
        return String.format("%02d", minute);
    }

    /**
     * 获取标准化的秒字符串 (两位数格式)
     * @param second 秒 (0-59)
     * @return 格式化的秒字符串 (00-59)
     */
    public static String getSecondNormal(int second) {
        if (second < 0 || second > 59) {
            throw new IllegalArgumentException("秒必须在0-59之间");
        }
        return String.format("%02d", second);
    }

    /**
     * 获取标准化的时间字符串
     * @param hour 小时 (0-23)
     * @param minute 分钟 (0-59)
     * @param second 秒 (0-59)
     * @param separator 分隔符
     * @return 格式化的时间字符串
     */
    public static String getTimeNormal(int hour, int minute, int second, String separator) {
        return String.format("%02d%s%02d%s%02d", hour, separator, minute, separator, second);
    }

    /**
     * 获取标准化的时间字符串 (默认使用":"作为分隔符)
     * @param hour 小时 (0-23)
     * @param minute 分钟 (0-59)
     * @param second 秒 (0-59)
     * @return 格式化的时间字符串
     */
    public static String getTimeNormal(int hour, int minute, int second) {
        return getTimeNormal(hour, minute, second, ":");
    }

    /**
     * 获取标准化的日期时间字符串
     * @param year 年份
     * @param month 月份 (1-12)
     * @param day 日期 (1-31)
     * @param hour 小时 (0-23)
     * @param minute 分钟 (0-59)
     * @param second 秒 (0-59)
     * @return 格式化的日期时间字符串
     */
    public static String getDateTimeNormal(int year, int month, int day, int hour, int minute, int second) {
        return getDateNormal(year, month, day) + " " + getTimeNormal(hour, minute, second);
    }

    /**
     * 判断是否为闰年
     * @param year 年份
     * @return 是否为闰年
     */
    public static boolean isLeapYear(int year) {
        return (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0);
    }

    /**
     * 验证日期是否有效
     * @param year 年份
     * @param month 月份 (1-12)
     * @param day 日期 (1-31)
     * @return 日期是否有效
     */
    public static boolean isValidDate(int year, int month, int day) {
        if (month < 1 || month > 12) {
            return false;
        }
        if (day < 1) {
            return false;
        }
        int maxDays = getDays(year, month);
        return day <= maxDays;
    }

    /**
     * 验证时间是否有效
     * @param hour 小时 (0-23)
     * @param minute 分钟 (0-59)
     * @param second 秒 (0-59)
     * @return 时间是否有效
     */
    public static boolean isValidTime(int hour, int minute, int second) {
        return hour >= 0 && hour <= 23 && 
               minute >= 0 && minute <= 59 && 
               second >= 0 && second <= 59;
    }
}
