package cn.edu.ntu.entity.common;

import java.util.Date;

import org.apache.struts2.json.annotations.JSON;

public class RoundAreaSetting extends AreaSetting {

	private int areaId;
	private int areaType;
	private String areaAlias;
	private Date startTime;
	private Date endTime;
	private int maxSpeed;
	private int lastTimeOverSpeed;
	private int radius;
	private float centerLat;
	private float centerLng;
	
	public int getAreaId() {
		return areaId;
	}
	public void setAreaId(int areaId) {
		this.areaId = areaId;
	}
	public int getAreaType() {
		return areaType;
	}
	public void setAreaType(int areaType) {
		this.areaType = areaType;
	}
	public String getAreaAlias() {
		return areaAlias;
	}
	public void setAreaAlias(String areaAlias) {
		this.areaAlias = areaAlias;
	}
	
	public Date getStartTime() {
		return startTime;
	}
	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}
	public Date getEndTime() {
		return endTime;
	}
	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}
	public int getMaxSpeed() {
		return maxSpeed;
	}
	public void setMaxSpeed(int maxSpeed) {
		this.maxSpeed = maxSpeed;
	}
	public int getLastTimeOverSpeed() {
		return lastTimeOverSpeed;
	}
	public void setLastTimeOverSpeed(int lastTimeOverSpeed) {
		this.lastTimeOverSpeed = lastTimeOverSpeed;
	}
	public int getRadius() {
		return radius;
	}
	public void setRadius(int radius) {
		this.radius = radius;
	}
	public float getCenterLat() {
		return centerLat;
	}
	public void setCenterLat(float centerLat) {
		this.centerLat = centerLat;
	}
	public float getCenterLng() {
		return centerLng;
	}
	public void setCenterLng(float centerLng) {
		this.centerLng = centerLng;
	}
	
	
}
