package cn.edu.ntu.service.common.implement;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.edu.ntu.dao.common.implement.MemberInfoDaoImpl;
import cn.edu.ntu.dao.common.interfaces.MemberInfoDao;
import cn.edu.ntu.entity.common.MemberInfo;
import cn.edu.ntu.service.common.interfaces.MemberInfoService;

/**
 *
 * 
 * <AUTHOR>
 * @date 2016��6��17�� ����2:08:40 
 */
@Service
public class MemberInfoServiceImpl implements MemberInfoService{
	
	private MemberInfoDao memberInfoDao;
	
	@Autowired
	public void setMemberInfoDao(MemberInfoDao memberInfoDao) {
		this.memberInfoDao = memberInfoDao;
	}

	@Override
	public  MemberInfo getMemberInfoByCarNumber(String carNumber) {
		MemberInfo memberInfo=memberInfoDao.getMemberInfoByCarNumber(carNumber);
		return memberInfo;
	}
	
	@Override
	public  MemberInfo getCarNumberByMemberName(String memberName) {
		MemberInfo memberInfo=memberInfoDao.getMemberInfoByMemberName(memberName);
		return memberInfo;
	}

	@Override
	public void addStoreMember(MemberInfo memberInfo) {

		memberInfoDao.addStoreMember(memberInfo);
	}

	@Override
	public  void editStoreMember(MemberInfo memberInfo) {
		memberInfoDao.editStoreMember(memberInfo);
	}

	@Override
	public  void deleteMember(String carNumber) {
		memberInfoDao.deleteMember(carNumber);
	}

	@Override
	public void modifyPassword(String userName, String userPassword) {

		memberInfoDao.modifyPassword(userName,userPassword);
	}

}
