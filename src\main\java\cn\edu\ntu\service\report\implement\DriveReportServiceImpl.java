package cn.edu.ntu.service.report.implement;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import cn.edu.ntu.dao.common.implement.VehicleGpsInfoDaoImpl;
import cn.edu.ntu.dao.common.implement.VehicleInfoDaoImpl;
import cn.edu.ntu.dao.common.implement.VehicleTrackDaoImpl;
import cn.edu.ntu.dao.common.interfaces.OffsetInfoDao;
import cn.edu.ntu.dao.common.interfaces.VehicleInfoDao;
import cn.edu.ntu.dao.common.interfaces.VehicleTrackDao;
import cn.edu.ntu.dao.report.implement.DriveReportDaoImpl;
import cn.edu.ntu.dao.report.interfaces.DriveReportDao;
import cn.edu.ntu.entity.common.OffsetInfo;
import cn.edu.ntu.entity.common.TableInfo;
import cn.edu.ntu.entity.common.VehicleGpsInfoForHistory;
import cn.edu.ntu.entity.common.VehicleInfo;
import cn.edu.ntu.entity.report.DriveReport;
import cn.edu.ntu.service.common.implement.VehicleInfoServiceImpl;
import cn.edu.ntu.service.common.interfaces.VehicleInfoService;
import cn.edu.ntu.service.report.interfaces.DriveReportService;
import cn.edu.ntu.utils.db.DateParseUtil;
import sun.net.www.content.text.plain;

/**
 *
 * 
 * <AUTHOR>
 * @date 2016��7��28�� ����10:14:16 
 */
@Service
public class DriveReportServiceImpl extends DefaultReportService implements DriveReportService{
	
	@Autowired
	private VehicleTrackDao vehicleTrackDao;
	@Autowired
	private DriveReportDao driveReportDao;
	@Autowired
	private VehicleInfoDao vehicleInfoDao;
	@Autowired
	@Qualifier("baiduOffsetInfoDaoImpl")
	private OffsetInfoDao offsetInfoDao;
	
	@Override
	public List<DriveReport> getDriveReportByCarNumber(String carNumber, String startTime, String endTime) {
//		DateParseUtil dpus=new DateParseUtil(startTime,false);
//		String startDate=dpus.getAfterDate();
//		DateParseUtil dpue=new DateParseUtil(endTime,false);
//		String endDate=dpue.getDate();
		
//		List<DriveReport> driveReportListPart1=parseDriveReport(carNumber, startTime, startDate);
//		
//		List<DriveReport> driveReportListPart2=driveReportDao.getDriveReport(carNumber,startDate,endDate);
//		
//		List<DriveReport> driveReportListPart3=parseDriveReport(carNumber, endDate, endTime);
//		
//		List<DriveReport> driveReportList=joinDriveReportList(driveReportListPart1,driveReportListPart2,driveReportListPart3);
		
		List<DriveReport> driveReportList=driveReportDao.getDriveReport(carNumber,startTime,endTime);
		
		return driveReportList;
	}
	
	@Override
	public  List<DriveReport> getDriveReportByGroupName(String groupName, String startTime, String endTime) {
		
		List<DriveReport> driveReportList=new ArrayList<DriveReport>();
		
		List<VehicleInfo> vehicleInfoList=vehicleInfoDao.listVehiclesByGroupName(groupName);
		
		for(VehicleInfo vehicleInfo:vehicleInfoList){
			List<DriveReport> list=getDriveReportByCarNumber(vehicleInfo.getCarNumber(), startTime, endTime);
			driveReportList.addAll(list);
		}
		
		return driveReportList;
	}
	
	@Override
	public  void storeDriveReportList(List<DriveReport> driveReportList) {

//		
//		for(DriveReport driveReport:driveReportList)
//		    driveReportDao.storeDriveReport(driveReport);
		
		driveReportDao.storeDriveReport(driveReportList);
	}
	
	@Override
	public void endDriveReportOperation() {
	    driveReportDao.endDriveReportOperation();	
	}


	private List<DriveReport> parseDriveReport(String carNumber, String startTime, String endTime) {
		
		TableInfo[] tableInfos=getTableInfosFromDate(startTime, endTime);
		
		VehicleInfo vehicleInfo=vehicleInfoDao.getVehicleInfoByCarNumber(carNumber);
		
		List<VehicleGpsInfoForHistory> list = vehicleTrackDao.searchTrack(vehicleInfo.getSimCardNumber(),tableInfos,startTime,endTime);
		
		List<VehicleGpsInfoForHistory> listWithOffset=calOffset(list);
		
		List<DriveReport> driveReportList=parseDriveReport(listWithOffset, vehicleInfo);
		
		return driveReportList;
		
//		List<List<VehicleGpsInfoForHistory>> listForDriveReport=getListForDriveReport(list);
//		
//		for(List<VehicleGpsInfoForHistory> subListForDriveReport:listForDriveReport){
//			DriveReport driveReport=new DriveReport();
//			
//			Date startDate=subListForDriveReport.get(0).getGpsTime();
//			driveReport.setBeginTime(startDate);
//			
//			int length=subListForDriveReport.size();
//			Date endDate=subListForDriveReport.get(length-1).getGpsTime();
//			driveReport.setEndTime(endDate);
//			
//			int duringTime=(int) ((endDate.getTime()-startDate.getTime())/1000/60);
//			driveReport.setDuringTime(duringTime);
//			
//			float maxSpeed=getMaxSpeed(subListForDriveReport);
//			driveReport.setMaxSpeed(maxSpeed);
//			
//			float totalDistance=getSoftwareTotalDistance(subListForDriveReport);
//			driveReport.setDriveDistance(totalDistance);
//			
//		    driveReport.setCarNumber(carNumber);
//		    
//		    driveReport.setCarOwner(vehicleInfo.getCarOwner());
//			
//			driveReport.setGroupName(vehicleInfo.getVehicleGroupNameListString());
//			
//			if (totalDistance > 0.2)
//				driveReportList.add(driveReport);
//			
//		}
		
	}
	
	private List<VehicleGpsInfoForHistory> calOffset(List<VehicleGpsInfoForHistory> vehicleGpsInfoWithoutOffsetList) {
        List<VehicleGpsInfoForHistory> vehicleGpsInfoWithOffsetList=new ArrayList<VehicleGpsInfoForHistory>();
		
		List<? extends OffsetInfo> offsetInfoList=offsetInfoDao.findOffsets(vehicleGpsInfoWithoutOffsetList);
		
		for(VehicleGpsInfoForHistory gpsInfo:vehicleGpsInfoWithoutOffsetList){
			for(OffsetInfo offsetInfo:offsetInfoList){
				float lon=(float) (Math.round(gpsInfo.getLon()*100)/100.0);
				float lat=(float) (Math.round(gpsInfo.getLat()*100)/100.0);
				if(floatEqual(lon, offsetInfo.getLng())&&floatEqual(lat, offsetInfo.getLat())){
					float lngWithOffset = gpsInfo.getLon() + offsetInfo.getOffsetLng();
					float latWithOffset = gpsInfo.getLat() + offsetInfo.getOffsetLat();
					gpsInfo.setLonWithOffset(lngWithOffset);
					gpsInfo.setLatWithOffset(latWithOffset);
					vehicleGpsInfoWithOffsetList.add(gpsInfo);
				}
			}
		}
		
		return vehicleGpsInfoWithOffsetList;
	}
	
	private boolean floatEqual(float firstNumber,float secondNumber){
		int firstInt=(int)(firstNumber*100);
		int secondInt=(int)(secondNumber*100);
		return firstInt==secondInt;
	}

	
	/*
	  private  List<List<VehicleGpsInfoForHistory>> getListForDriveReport(List<VehicleGpsInfoForHistory> list) {
			
			List<VehicleGpsInfoForHistory> vehicleGpsInfoList;
			List<List<VehicleGpsInfoForHistory>> listForDriveReport=new ArrayList<List<VehicleGpsInfoForHistory>>();
			
			int accOnIndex=0,accOffIndex=0;

			while(accOnIndex<list.size()&&accOffIndex<list.size()&&accOnIndex!=-1&&accOffIndex!=-1){
				accOnIndex = findAccOnChange(list, accOffIndex);
				accOffIndex = findAccOffChange(list, accOnIndex);

				if(accOnIndex!=-1&&accOffIndex!=-1){
					vehicleGpsInfoList = list.subList(accOnIndex, accOffIndex);
					listForDriveReport.add(vehicleGpsInfoList);
				}
			}
			
			return listForDriveReport;
		}
	

	private List<DriveReport> joinDriveReportList(List<DriveReport> driveReportListPart1,
			List<DriveReport> driveReportListPart2, List<DriveReport> driveReportListPart3) {
		
		
		if (driveReportListPart1.size()!=0&&driveReportListPart2.size() != 0&&driveReportListPart3.size()!=0) {
			
			List<DriveReport> driveReportList=new ArrayList<DriveReport>();
			List<DriveReport> tempList;
			
			if(driveReportListPart1.size()>1){
				 tempList= driveReportListPart1.subList(0, driveReportListPart1.size() - 1);
				driveReportList.addAll(tempList);
			}
			

			DriveReport lastDriveReportFromListPart1 = driveReportListPart1.get(driveReportListPart1.size() - 1);
			DriveReport firstDriveReportFromListPart2 = driveReportListPart2.get(0);
			if (firstDriveReportFromListPart2.getBeginTime().getTime()
					- lastDriveReportFromListPart1.getEndTime().getTime() <= 30 * 1000) {
				DriveReport driveReport = joinDriveReport(lastDriveReportFromListPart1, firstDriveReportFromListPart2);
				driveReportList.remove(driveReportList.size()-1);
				driveReportList.add(driveReport);
			} else {
				driveReportList.add(firstDriveReportFromListPart2);
			}

			if(driveReportListPart2.size()>1){
				tempList = driveReportListPart2.subList(1, driveReportListPart2.size() - 1);
				driveReportList.addAll(tempList);
			}
			

			DriveReport lastDriveReportFromListPart2 = driveReportList.get(driveReportList.size() - 1);
			DriveReport firstDriveReportFromListPart3 = driveReportListPart3.get(0);
			if (firstDriveReportFromListPart3.getBeginTime().getTime()
					- lastDriveReportFromListPart2.getEndTime().getTime() <= 30 * 1000) {
				DriveReport driveReport = joinDriveReport(lastDriveReportFromListPart2, firstDriveReportFromListPart3);
				driveReportList.remove(driveReportList.size()-1);
				driveReportList.add(driveReport);
			} else {
				driveReportList.add(firstDriveReportFromListPart3);
			}
			
			if(driveReportListPart3.size()>1){
				tempList = driveReportListPart3.subList(1, driveReportListPart3.size() - 1);
				driveReportList.addAll(tempList);
			}
		
			return driveReportList;
		} else if(driveReportListPart1.size()!=0&&driveReportListPart2.size()==0&&driveReportListPart3.size()!=0){
			return joinDriveReportList(driveReportListPart1, driveReportListPart3);
		} else if(driveReportListPart1.size()==0&&driveReportListPart2.size()!=0&&driveReportListPart3.size()!=0){
			return joinDriveReportList(driveReportListPart2, driveReportListPart3);
		} else if(driveReportListPart1.size()!=0&&driveReportListPart2.size()!=0&&driveReportListPart3.size()==0){
			return joinDriveReportList(driveReportListPart1, driveReportListPart2);
		} else if(driveReportListPart1.size()!=0&&driveReportListPart2.size()==0&&driveReportListPart3.size()==0){
			return driveReportListPart1;
		} else if (driveReportListPart1.size()==0&&driveReportListPart2.size()!=0&&driveReportListPart3.size()==0) {
			return driveReportListPart2;
		} else if (driveReportListPart1.size()==0&&driveReportListPart2.size()==0&&driveReportListPart3.size()!=0) {
			return driveReportListPart3;
		}
		
		return new ArrayList<DriveReport>();
	}

	private  List<DriveReport> joinDriveReportList(List<DriveReport> driveReportListPart1, List<DriveReport> driveReportListPart3){
		List<DriveReport> driveReportList=new ArrayList<DriveReport>();
		List<DriveReport> tempList;
		
		if (driveReportListPart1.size() > 1) {
			 tempList= driveReportListPart1.subList(0, driveReportListPart1.size() - 2);
			driveReportList.addAll(tempList);
		}
		
		DriveReport lastDriveReportFromListPart1=driveReportListPart1.get(driveReportListPart1.size()-1);
		DriveReport firstDriveReportFromListPart3=driveReportListPart3.get(0);
		if(firstDriveReportFromListPart3.getBeginTime().getTime()-lastDriveReportFromListPart1.getEndTime().getTime()<=30*1000){
			DriveReport driveReport=joinDriveReport(lastDriveReportFromListPart1, firstDriveReportFromListPart3);
			driveReportList.add(driveReport);
		}else{
			driveReportList.add(lastDriveReportFromListPart1);
			driveReportList.add(firstDriveReportFromListPart3);
		}
		
		if(driveReportListPart3.size()>1){
			tempList=driveReportListPart3.subList(1, driveReportListPart3.size()-1);
			driveReportList.addAll(tempList);
		}
		
		
		return driveReportList;
	}


	private  DriveReport joinDriveReport(DriveReport driveReport1,DriveReport driveReport2) {
		DriveReport driveReport=new DriveReport();
		driveReport.setCarNumber(driveReport1.getCarNumber());
		driveReport.setGroupName(driveReport1.getGroupName());
		driveReport.setCarOwner(driveReport1.getCarOwner());
		driveReport.setBeginTime(driveReport1.getBeginTime());
		driveReport.setEndTime(driveReport2.getEndTime());
		driveReport.setDuringTime(driveReport1.getDuringTime()+driveReport2.getDuringTime());
		driveReport.setDriveDistance(driveReport1.getDriveDistance()+driveReport2.getDriveDistance());
		driveReport.setMaxSpeed(Math.max(driveReport1.getMaxSpeed(), driveReport2.getMaxSpeed()));
		return driveReport;
	}
*/
}	
