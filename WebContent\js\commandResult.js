function queryCommandResult(simCardString, commandSerialNumber) {
	$.ajax({
		url : "/808FrontProject/common/queryCommandResultAction",
		data : {
			simCardString : simCardString,
			commandSerialNumber : commandSerialNumber
		},
		dataType : "json",
		type : "post",
		success : function(data) {
			commandResult = data.result;
		}
	});
}

function initCommandResult() {
	commandResult = 0;
}

function queryCommandResults(simCardStrings, commandSerialNumbers) {
	$.ajax({
		url : "/808FrontProject/common/queryCommandResultsAction",
		data : {
			simCardStrings : simCardStrings,
			commandSerialNumbers : commandSerialNumbers
		},
		dataType : "json",
		type : "post",
		success : function(data) {
			commandResults = data.result;
		}
	});
}

function initCommandResults() {
	commandResults = 0;
}
