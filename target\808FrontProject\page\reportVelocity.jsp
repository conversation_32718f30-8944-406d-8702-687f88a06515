<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@taglib prefix="s" uri="/struts-tags"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>速度报表</title>
<link rel="stylesheet" type="text/css" href="/808FrontProject/css/user.css">
<script type="text/javascript" src="/808FrontProject/js/lib.jquery.js"></script>
<script type="text/javascript" src="/808FrontProject/js/tools.js"></script>
	
<!-- map component [S] -->
<script type="text/javascript" src="/808FrontProject/js/baidumap.js"></script>
<script type="text/javascript"
	src="http://api.map.baidu.com/api?v=2.0&ak=6ukSP6o4ff8u9SSnlVEKmiZC"></script>
<script type="text/javascript"
	src="http://api.map.baidu.com/library/DrawingManager/1.4/src/DrawingManager.js"></script>
<script type="text/javascript"
	src="http://api.map.baidu.com/library/MarkerManager/1.2/src/MarkerManager.js"></script>
<!-- map component [E] -->


<!-- date component [S] -->
<script type="text/javascript"
	src="/808FrontProject/js/lib.jsdate/jscal2.js"></script>
<script type="text/javascript"
	src="/808FrontProject/js/lib.jsdate/en.js"></script>
<link rel="stylesheet" type="text/css"
	href="/808FrontProject/js/lib.jsdate/jscal2.css">
<link rel="stylesheet" type="text/css"
	href="/808FrontProject/js/lib.jsdate/border-radius.css">
<link rel="stylesheet" type="text/css"
	href="/808FrontProject/js/lib.jsdate/steel.css">
<!-- date component [E] -->

<!-- chart component [s] -->
<link rel="stylesheet" type="text/css"
	href="/808FrontProject/js/lib.mechart/chart.css">
<script src="/808FrontProject/js/lib.mechart/d3.min.js" charset="utf-8"></script>
<script src="/808FrontProject/js/lib.mechart/chart.js" charset="utf-8"></script>
<!-- chart component [e] -->


<script type="text/javascript">
	var x = [];
	var y = [];
	for (var i = 0; i < 3; i++) {
		y[i] = [];
	}
	var yLegend = [];

	$(window)
			.load(
					function() {

						//date componenent
						var cal = Calendar.setup({
							onSelect : function(cal) {
								cal.hide()
							},
							showTime : true
						});
						cal.manageFields("date1Btn", "searchDate1",
								"%Y-%m-%d %H:%M");
						cal.manageFields("date2Btn", "searchDate2",
								"%Y-%m-%d %H:%M");
						
						// Attaining the current date and initializing [S]
						var curDate = getFormatDate().substring(0, 10);
						document.getElementById("searchDate1").value = curDate
								+ " 00:00";
						document.getElementById("searchDate2").value = curDate
								+ " 23:59";
						// Attaining the current date and initializing [E]
						
						//select vehicleGroup
						$.ajax({
							url : "/808FrontProject/common/selectAllGroupAction",
							type : "post",
							dataType : "json",
							success : function(data) {
								var htmlContent = "<option>选择车队</option>";
								for (var i = 0; i < data.length; i++)
									htmlContent += "<option>" + data[i].name
											+ "</option>";
								$("#selectGroup").html(htmlContent);
							}
						});

						//select vehicle
						$("#selectGroup")
								.change(
										function() {
											var groupName = $("#selectGroup")
													.val();
											if (groupName != "选择车队") {
												$
														.ajax({
															url : "/808FrontProject/common/selectVehicleAction",
															data : {
																"groupName" : groupName
															},
															type : "post",
															dataType : "json",
															success : function(
																	data) {
																var htmlContent = "<option>全部车辆</option>";
																for (var i = 0; i < data.length; i++) {
																	htmlContent += "<option>"
																			+ data[i].carNumber
																			+ "</option>";
																}

																$(
																		"#selectVehicle")
																		.html(
																				htmlContent);
															}
														});
											}
										});

					});

	function getVelocityReport() {

		var groupName = $("#selectGroup").val();
		var carNumber = $("#selectVehicle").val();
		var startTime = $("#searchDate1").val();
		var endTime = $("#searchDate2").val();

		if (groupName == "选择车队") {
			alert("请选择车队");
			return;
		}

		if (carNumber != "全部车辆") {

			$
					.ajax({
						url : "/808FrontProject/report/getVelocityReportByCarNumber.action",
						data : {
							"carNumber" : carNumber,
							"startTime" : startTime,
							"endTime" : endTime,
						},
						type : "post",
						dataType : "json",
						success : function(data) {
							clearChartData();
							var htmlContent = '<tr class="thSize thColor"><td>编号</td>	<td>车牌号码</td><td>起始时间</td>'
									+ '<td>结束时间</td><td>时长</td><td>最大速度</td><td>最大速度位置</td><td>最小速度</td><td>平均速度</td></tr>';
							for (var i = 0; i < data.length; i++) {
								if(data[i].lngAtMaxSpeed==0){
								htmlContent += "<tr><td>" + (i + 1) + "</td>";
								htmlContent += "<td>" + data[i].carNumber
										+ "</td>";
								htmlContent += "<td>" + data[i].beginTime
										+ "</td>";
								htmlContent += "<td>" + data[i].endTime
										+ "</td>";
								htmlContent += "<td>" + data[i].duringTime
										+ "分</td>";
								htmlContent += "<td>" + data[i].maxSpeed
										+ "</td>";
								htmlContent += "<td>位置</td>";
								htmlContent += "<td>" + data[i].minSpeed
										+ "</td>";
								htmlContent += "<td>" + data[i].averageSpeed
										+ "</td></tr>";
								}else{
									htmlContent += "<tr><td>" + (i + 1) + "</td>";
									htmlContent += "<td>" + data[i].carNumber
											+ "</td>";
									htmlContent += "<td>" + data[i].beginTime
											+ "</td>";
									htmlContent += "<td>" + data[i].endTime
											+ "</td>";
									htmlContent += "<td>" + data[i].duringTime
											+ "分</td>";
									htmlContent += "<td>" + data[i].maxSpeed
											+ "</td>";
									htmlContent += "<td><div style='cursor:pointer' onclick=\"new BaiduMap().parseAddress("
										+ data[i].latAtMaxSpeed
										+ ","
										+ data[i].lngAtMaxSpeed
										+ ",'showAddress',this)\">"
										+ "点击显示位置...</div></td>";
									htmlContent += "<td>" + data[i].minSpeed
											+ "</td>";
									htmlContent += "<td>" + data[i].averageSpeed
											+ "</td></tr>";
								}
								y[0][i] = data[i].maxSpeed;
								y[1][i] = data[i].minSpeed;
								y[2][i] = data[i].averageSpeed;
								x[i] = (i + 1) + " ";

								yLegend[0] = "最大速度";
								yLegend[1] = "最小速度";
								yLegend[2] = "平均速度";

							}
							$("#resultSpan").html(htmlContent);
							renderChart();
						}
					});
		} else {
			$
					.ajax({
						url : "/808FrontProject/report/getVelocityReportByGroupName.action",
						data : {
							"groupName" : groupName,
							"startTime" : startTime,
							"endTime" : endTime,
						},
						type : "post",
						dataType : "json",
						success : function(data) {
							clearChartData();
							var htmlContent = '<tr class="thSize thColor"><td>编号</td>	<td>车牌号码</td><td>起始时间</td>'
									+ '<td>结束时间</td><td>时长</td><td>最大速度</td><td>最大速度位置</td><td>最小速度</td><td>平均速度</td></tr>';
							for (var i = 0; i < data.length; i++) {
								if(data[i].lngAtMaxSpeed==0){
									htmlContent += "<tr><td>" + (i + 1) + "</td>";
									htmlContent += "<td>" + data[i].carNumber
											+ "</td>";
									htmlContent += "<td>" + data[i].beginTime
											+ "</td>";
									htmlContent += "<td>" + data[i].endTime
											+ "</td>";
									htmlContent += "<td>" + data[i].duringTime
											+ "分</td>";
									htmlContent += "<td>" + data[i].maxSpeed
											+ "</td>";
									htmlContent += "<td>位置</td>";
									htmlContent += "<td>" + data[i].minSpeed
											+ "</td>";
									htmlContent += "<td>" + data[i].averageSpeed
											+ "</td></tr>";
									}else{
										htmlContent += "<tr><td>" + (i + 1) + "</td>";
										htmlContent += "<td>" + data[i].carNumber
												+ "</td>";
										htmlContent += "<td>" + data[i].beginTime
												+ "</td>";
										htmlContent += "<td>" + data[i].endTime
												+ "</td>";
										htmlContent += "<td>" + data[i].duringTime
												+ "分</td>";
										htmlContent += "<td>" + data[i].maxSpeed
												+ "</td>";
										htmlContent += "<td><div style='cursor:pointer' onclick=\"new BaiduMap().parseAddress("
											+ data[i].latAtMaxSpeed
											+ ","
											+ data[i].lngAtMaxSpeed
											+ ",'showAddress',this)\">"
											+ "点击显示位置...</div></td>";
										htmlContent += "<td>" + data[i].minSpeed
												+ "</td>";
										htmlContent += "<td>" + data[i].averageSpeed
												+ "</td></tr>";
									}

								y[0][i] = data[i].maxSpeed;
								y[1][i] = data[i].minSpeed;
								y[2][i] = data[i].averageSpeed;
								x[i] = (i + 1) + " ";
								yLegend[0] = "最大速度";
								yLegend[1] = "最小速度";
								yLegend[2] = "平均速度";

							}
							$("#resultSpan").html(htmlContent);
							renderChart();
						}
					});
		}
	}

	//绘制图表 [s] --------------------------------------------
	$(window).resize(function() {
		renderChart();
	});

	function renderChart() {
		var canvasId = "canvasId";
		var jqObj = $("#" + canvasId);
		var w = getWinWidth();
		var h = 200;
		var offset = 50;
		if (y[0].length > 0) {
			jqObj.height(h);
			jqObj.width(w - offset);
			var chart = new MapeyeChart(canvasId);
			chart.multiLineDyn(y, x, yLegend, "行驶速度 (km/h)", "序号");
		}
	}
	function clearChartData() {
		//alert("Calling the clearData()!");
		x = [];
		y = [];
		for (var i = 0; i < 3; i++) {
			y[i] = [];
		}
		yLegend = [];
		var obj=document.getElementById("canvasId");
		obj.innerHTML="暂无数据";
		obj.style.height="30px";
	}
	//绘制图表 [e] --------------------------------------------
</script>
</head>
<body>
	<span class="title1">速度报表</span>
	<table class="tableCommon">
		<tr>
			<td colspan=3>选择车队 <select id="selectGroup"></select>
				&nbsp;&nbsp;选择车辆 <select id="selectVehicle"></select></td>
		</tr>


		<tr>
			<td>起始时间 <input type='text' name='searchDate1' id='searchDate1'
				title="时间格式：yyyy-mm-dd hh:mm" /> <input id="date1Btn" type="button"
				style="height: 25px; width: 30px;" value="..." />
			</td>

			<td>结束时间 <input type='text' name='searchDate2' id='searchDate2'
				title="时间格式：yyyy-mm-dd hh:mm" /> <input id="date2Btn" type="button"
				style="height: 25px; width: 30px;" value="..." />
			</td>


			<td style="padding-left: 0px; text-align: center; width: 10%"><input
				type="button" class="buttonStyle" value="查询"
				onclick="getVelocityReport();" /></td>
		</tr>

	</table>

	<table class="tableCommon">
		<tr>
			<td><div id="canvasId">暂无记录</div></td>
		</tr>
	</table>

	<table class="tableCommon" id="resultSpan">
		<tr class="thSize thColor">
			<td>编号</td>
			<td>车牌号码</td>
			<td>起始时间</td>
			<td>结束时间</td>
			<td>时长</td>
			<td>最大速度</td>
			<td>最大速度发生位置</td>
			<td>最小速度</td>
			<td>平均速度</td>
		</tr>
		<tr>
			<td colspan=9>暂无记录</td>
		</tr>
	</table>
</body>
</html>