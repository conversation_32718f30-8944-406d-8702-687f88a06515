/* Written by <PERSON>, <PERSON><PERSON> in 2016-07-26
 * Version: 1.0
 * Function name: line chart
*/
.border {
	border: 1px solid #000000;
}

.axis path, .axis line {
	fill: none;
	stroke: black;
	shape-rendering: crispEdges;
}

.axis text {
	font-family: sans-serif;
	font-size: 12px;
	font-weight: bold;
}

.chart {
	font-family: sans-serif;
	font-size: 12px;
	text-anchor: middle;
}

.dataLabel {
	font-family: sans-serif;
	fill: black;
	font-size: 12px;
	text-anchor: middle;
	font-weight: bold;
}

.axisXLegend .axisYLegend {
	font-family: sans-serif;
	fill: black;
	font-size: 12px;
}