package cn.edu.ntu.service.common.interfaces;

import java.util.List;

import cn.edu.ntu.entity.common.DynatreeNode;
import cn.edu.ntu.entity.common.VehicleGpsInfo;
import cn.edu.ntu.entity.common.VehicleTotalInfo;

/**
 * Vehicle GPS Info Service Interface
 *
 * <AUTHOR>
 * @date 2016-12-06 05:19:52
 */
public interface VehicleGpsInfoService {
	
	public  VehicleTotalInfo showAllVehiclesOnMap(String carNumberString,String currentUserName) throws Exception ;
	public  VehicleGpsInfo showSingleVehicleOnMap(String carNumber) throws Exception ;
}
