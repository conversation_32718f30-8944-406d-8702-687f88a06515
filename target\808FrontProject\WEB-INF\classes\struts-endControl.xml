<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE struts PUBLIC
	"-//Apache Software Foundation//DTD Struts Configuration 2.3//EN"
	"http://struts.apache.org/dtds/struts-2.3.dtd">

<struts>

	<constant name="struts.devMode" value="true" />
	
	<constant name="struts.objectFactory" value="org.apache.struts2.spring.StrutsSpringObjectFactory" />
	
	<package name="endControl" namespace="/endControl" extends="json-default"> 
        
        <action name="immediatelyPhotoAction" class="endControlAction" method="immediatelyPhoto">
            <result type="json">
               <param name="root">commandInfo</param>
            </result>
        </action>
        
        <action name="nImmediatelyPhotoAction" class="endControlAction" method="nImmediatelyPhoto">
            <result type="json">
               <param name="root">commandInfos</param>
            </result>
        </action>
        
        <action name="fixedIntervalPhotoAction" class="endControlAction" method="fixedIntervalPhoto">
            <result type="json">
               <param name="root">storeResult</param>
            </result>
        </action>
        
        <action name="nFixedIntervalPhotoAction" class="endControlAction" method="nFixedIntervalPhoto">
            <result type="json">
               <param name="root">storeResultList</param>
            </result>
        </action>
        
        <action name="fixedTimePhotoAction" class="endControlAction" method="fixedTimePhoto">
        </action>
        
         <action name="nFixedTimePhotoAction" class="endControlAction" method="nFixedTimePhoto">
        </action>
        
        <action name="queryFixedTimePhotoSettingAction" class="endControlAction" method="queryFixedTimePhotoSetting">
            <result type="json">
                <param name="root">ShotSettingList</param>
            </result>
        </action>
        
        <action name="queryFixedIntervalPhotoSettingAction" class="endControlAction" method="queryFixedIntervalPhotoSetting">
            <result type="json">
                <param name="root">ShotSettingList</param>
            </result>
        </action>
        
        <action name="deleteFixedTimeShotSettingAction" class="endControlAction" method="deleteFixedTimeShotSetting">
        </action>
        
        <action name="deleteFixedIntervalShotSettingAction" class="endControlAction" method="deleteFixedIntervalShotSetting">
        </action>
        
        <action name="changeIntervalAction" class="endControlAction" method="changeInterval">
            <result type="json">
               <param name="root">commandInfo</param>
            </result>
        </action>
        
        <action name="positionEnquireAction" class="endControlAction" method="positionEnquire">
            <result type="json">
               <param name="root">commandInfo</param>
            </result>
        </action>
        
         <action name="nPositionEnquireAction" class="endControlAction" method="nPositionEnquire">
            <result type="json">
               <param name="root">commandInfos</param>
            </result>
        </action>

        <action name="textMessageDeliveryAction" class="endControlAction" method="textMessageDelivery">
            <result type="json">
                <param name="root">commandInfo</param>
            </result>
        </action>
        
        <action name="terminalResetAction" class="endControlAction" method="terminalReset">
            <result type="json">
                <param name="root">commandInfo</param>
            </result>
        </action>
        
        <action name="queryFenceSettingAction" class="endControlAction" method="queryFenceSetting">
            <result type="json">
                <param name="root">areaSettingList</param>
            </result>
        </action>
        
         <action name="bindFenceSettingAction" class="endControlAction" method="bindFenceSetting">
        </action>
        
        <action name="unbindFenceSettingAction" class="endControlAction" method="unbindFenceSetting">
        </action>
       
	</package>
	

	
</struts>