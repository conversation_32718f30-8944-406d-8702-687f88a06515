//
var imgPath = "/808FrontProject/image/";
// 动画播放相关变量
var latArr = [];
var lonArr = [];
var angleArr = [];
var speedArr = [];
var labelArr = [];
var cmdst;
var lineColor;
var lineSize;
var len = 0, n = 0;
var lyr1, lyr2;
var btn;
var cntCallShowAnimation = 0;

var carNumberGV = "";
var trackResGV = "";
var trackLayerOriGV = 0;
// 跟踪信息面板中的全局变量，用于保存临时数据
var trackTab1DataGV = "";// 信息栏面板布局页1变量
// var trackTab2DataGV = "";
// var trackTab3DataGV = "";
// var trackTab4DataGV = "";
// var trackTab5DataGV = "";
var addressCursorGV;// 在信息栏列表中，用于确定地址解析中的游标

var carNumbers = [];
var speeds = [];
var lons = []; // wgs cds
var lats = []; // wgs cds
var lonsWithOffset = []; // baidu cds
var latsWithOffset = [];// baidu cds
var angles = [];// angle
var gpsTimes = [];
var labels = [];
var infos = [];
var locations = [];
var states = [];
var ops = [];
var drivers=[];

var xlsUrl = "";

var lonInit;
var latInit;
var labelInit;
var stateStringInit;
var runStateInit;
var infoInit;

var isFind;// whether find the drivers

function searchTrackInit() {
	// alert("view single by number");
	if (p != null) {
		carNumberGV = p;
	}

	$
			.ajax({
				url : "/808FrontProject/showVehicle/showSingleVehicleAction",
				data : {
					"carNumber" : carNumberGV,
				},
				type : "post",
				dataType : "json",
				success : function(data) {
					lonInit = data.lonWithOffset;
					latInit = data.latWithOffset;
					labelInit = carNumberGV;
					stateStringInit = "";
					if ((data.state & 0x00000001) == 1)
						stateStringInit += "ACC开;";
					else
						stateStringInit += "ACC关;";
					runStateInit = (data.state & 0x00000010) >> 4;
					if (runStateInit == 1)
						stateStringInit += "停运状态";
					else
						stateStringInit += "运营状态";
					infoInit = "<div style='font-size:13px;line-height:22px;'>"
							+ "<div style='font-weight:bold;background-color:#c1e0ff;width:200px;height:30px'>车辆详细信息</div>"
							+ "车牌号: "
							+ carNumberGV
							+ "<br/>"
							+ "经度: "
							+ data.lon
							+ "<br/>"
							+ "纬度: "
							+ data.lat
							+ "</br>"
							+ "速度："
							+ data.speed
							+ " 公里/时<br/>"
							+ "行车方向： "
							+ getAzimuth(data.direction)
							+ " ("
							+ data.direction
							+ " deg)<br/>"
							+ "GPS采集时间："
							+ data.gpsTime
							+ "<br/>"
							+ "当前状态："
							+ stateStringInit
							+ "<br/>"
							+ "当前位置："
							+ "<span id='currentAddress' style='cursor:pointer' onclick=\"new BaiduMap().parseAddress("
							+ data.latWithOffset
							+ ","
							+ data.lonWithOffset
							+ ",'showAddress',this)\">"
							+ "点击显示位置..."
							+ "</span>"
							+ "</div>";
					// alert(info);
					mapTrack.addMarkerWithLabel(latInit, lonInit, labelInit,
							infoInit, "offline");
					mapTrack.moveView(latInit, lonInit);

				}
			});
}

function searchTrack() {
	
	isFind=document.getElementById("isFind").checked;
	// if (p != null) {
	// carNumberGV = p;
	// }
	var sDate1 = $("#searchDate1").val();
	var sDate2 = $("#searchDate2").val();
	// alert(sNumber + ", " + sDate1 + ", " + sDate2);

	// validating the input vehicle ID
	if (carNumberGV.length == 0) {
		alert("请在左侧车辆列表中选择车牌号！");
		return false;
	}
	// validating the order for input dates
	if (sDate1.length != 16) {
		alert("您输入的起始时间‘" + sDate1 + "’无法识别，请按照格式‘yyyy-mm-dd hh:mm’重新输入！");
		return false;
	} else {
		var date = sDate1.substring(0, 10);
		var resDate = dateValidation(date);
		if (!resDate) {
			alert("您输入的起始日期‘" + date + "’无法识别，请按照格式‘yyyy-mm-dd’重新输入！");
			return false;
		}
		var time = sDate1.substring(11, 16);
		var resTime = timeValidation(time);
		if (!resTime) {
			alert("您输入的起始时刻‘" + time + "’无法识别，请按照格式‘hh:mm’重新输入！");
			return false;
		}
	}

	if (sDate2.length != 16) {
		alert("您输入的结束时间‘" + sDate2 + "’无法识别，请按照格式‘yyyy-mm-dd hh:mm’重新输入！");
		return false;
	} else {
		var date = sDate2.substring(0, 10);
		var resDate = dateValidation(date);
		if (!resDate) {
			alert("您输入的结束日期‘" + date + "’无法识别，请按照格式‘yyyy-mm-dd’重新输入！");
			return false;
		}
		var time = sDate2.substring(11, 16);
		var resTime = timeValidation(time);
		if (!resTime) {
			alert("您输入的结束时刻‘" + time + "’无法识别，请按照格式‘hh:mm’重新输入！");
			return false;
		}
	}

	var dateNow = getFormatDate();
	// alert(dateNow);
	var dateComp = getDateInterval(sDate1, sDate2); // unit: day
	var dateComp1 = getDateInterval(sDate1, dateNow);
	var dateComp2 = getDateInterval(dateNow, sDate2);
	var resComp = dateComp[0] * 24 * 60 + dateComp[1] * 60 + dateComp[2];
	var resComp1 = dateComp1[0] * 24 * 60 + dateComp1[1] * 60 + dateComp1[2]; // unit:
	// min
	var resComp2 = dateComp2[0] * 24 * 60 + dateComp2[1] * 60 + dateComp2[2]; // unit:
	// min

	if (resComp1 < 0 || resComp2 > 0) {
		alert("您要查询的历史轨迹数据尚未生成！");
		return false;
	}

	// var resComp = dateCompare(sDate1, sDate2);
	if (resComp <= 0) {
		alert("您输入的结束时间不能早于开始时间！");
		return false;
	}

	if (dateComp[0] > 7) {
		alert("您要查询的历史轨迹时间间隔不能超过7天！");
		return false;
	}

	// clear the previous track records
	stopAnimation();

	// 使用模态对话框显示当前执行状态------------------------------------------
	var d = dialog({
		content : '查询中...',
	});
	d.show();
	freezeAnmiation();// 冻结操作按钮
	showTrackLayouts(""); // 重写轨迹查询结果栏
	// 清空地图
	mapTrack.delMarkerAll();
	mapTrack.delOverlays();
	mapTrack.addMarkerWithLabel(latInit, lonInit, labelInit, infoInit,
			"offline");
	mapTrack.moveView(latInit, lonInit);
	// ------------------------------------------------------------------
	$.ajax({
		url : "/808FrontProject/showVehicle/viewTrackAction",
		data : {
			"carNumber" : carNumberGV,// encodeUTF8(carNumber)
			"dateStart" : sDate1,
			"dateEnd" : sDate2,
		    "findDriver": isFind
		},
		type : "post",
		dataType : "json",
		success : function(data) {
			d.remove();
			xlsUrl = data.webFilePath;
			// alert(data.gpsInfoList.length);
			handleTrack(data.gpsInfoList);

		},
		error : function(data) {
			d.remove();
			showTrackLayouts("暂无记录!"); // 重写轨迹查询结果栏
			// 清空地图
			mapTrack.delMarkerAll();
			mapTrack.delOverlays();
			mapTrack.addMarkerWithLabel(latInit, lonInit, labelInit, infoInit,
					"offline");
			mapTrack.moveView(latInit, lonInit);
		}
	});
}

function handleTrack(data) {
	cntCallShowAnimation = 0;

	// clear the arrays below
	carNumbers = [];
	speeds = [];
	lons = []; // wgs cds
	lats = []; // wgs cds
	lonsWithOffset = []; // baidu cds
	latsWithOffset = [];// baidu cds
	angles = [];// angle
	gpsTimes = [];
	labels = [];
	infos = [];
	locations = [];
	states = [];
	opts = [];
	

	var tmp = "lon\tlat\tangle\n";
	var len = data.length;

	trackTab1DataGV = "";// clear the global variable
	trackResGV = "";// clear the global variable

	var isFilter = document.getElementById("isFilter").checked;
	// alert(isFilter);

	var cntFilter = 0;
	var j = 0;
	for (var i = 0; i < len; i++) {
		if (isFilter) {
			if (data[i].speed == 0) {
				if (cntFilter == 0) {
					carNumbers[j] = data[i].carNumber;
					speeds[j] = data[i].speed;
					lons[j] = data[i].lon;
					lats[j] = data[i].lat;
					lonsWithOffset[j] = data[i].lonWithOffset;
					latsWithOffset[j] = data[i].latWithOffset;
					angles[j] = data[i].direction;
					gpsTimes[j] = data[i].gpsTime;
					states[j] = data[i].state;
					locations[j] = data[i].location;
					if(data[i].driverName==null)
						drivers[j]="";
					else
					    drivers[j]=data[i].driverName;
					
					// 查询司机信息列
					labels[j] = j + 1;
					j++;
				}
				cntFilter++;
			} else {
				cntFilter = 0;// 过滤计数器清零
				carNumbers[j] = data[i].carNumber;
				speeds[j] = data[i].speed;
				lons[j] = data[i].lon;
				lats[j] = data[i].lat;
				lonsWithOffset[j] = data[i].lonWithOffset;
				latsWithOffset[j] = data[i].latWithOffset;
				angles[j] = data[i].direction;
				gpsTimes[j] = data[i].gpsTime;
				states[j] = data[i].state;
				locations[j] = data[i].location;
				
				
				if(data[i].driverName==null)
					drivers[j]="";
				else
					drivers[j]=data[i].driverName;
				// 查询司机信息列
				labels[j] = j + 1;
				j++;
			}
		} else {
			carNumbers[i] = data[i].carNumber;
			speeds[i] = data[i].speed;
			lons[i] = data[i].lon;
			lats[i] = data[i].lat;
			lonsWithOffset[i] = data[i].lonWithOffset;
			latsWithOffset[i] = data[i].latWithOffset;
			angles[i] = data[i].direction;
			gpsTimes[i] = data[i].gpsTime;
			states[i] = data[i].state;
			locations[i] = data[i].location;
			
			
			if(data[i].driverName==null)
				drivers[i]="";
			else
				drivers[i]=data[i].driverName;
			// 查询司机信息列
			labels[i] = i + 1;
		}

	}
	for (var j = 0; j < lats.length; j++) {
		var stateString = "";
		if ((states[j] & 0x00000001) == 1)
			stateString += "ACC开;";
		else
			stateString += "ACC关;";
		var runState = (states[j] & 0x00000010) >> 4;
		if (runState == 1)
			stateString += " 停运状态";
		else
			stateString += " 运营状态";
		infos[j] = "<div style='font-size:13px;line-height:22px;'>"
				+ "<div style='font-weight:bold;background-color:#c1e0ff;width:200px;height:30px'>车辆详细信息</div>"
				+ "车牌号: "
				+ carNumbers[j]
				+ "<br/>"
				+ "经度: "
				+ lons[j]
				+ "<br/>"
				+ "纬度: "
				+ lats[j]
				+ "</br>"
				+ "速度："
				+ speeds[j]
				+ " 公里/时<br/>"
				+ "行车方向： "
				+ getAzimuth(angles[j])
				+ " ("
				+ angles[j]
				+ " deg)<br/>"
				+ "GPS采集时间："
				+ gpsTimes[j]
				+ "<br/>"
				+ "当前状态："
				+ stateString
				+ "<br/>"
				+ "当前位置："
				+ "<span id='currentAddress' style='cursor:pointer' onclick=\"new BaiduMap().parseAddress("
				+ latsWithOffset[j]
				+ ","
				+ lonsWithOffset[j]
				+ ",'showAddress',this)\">" + "点击显示位置..." + "</span>" + "</div>";

		tmp += lons[j] + "\t" + lats[j] + "\t" + angles[j] + "\n";

		// global variables for the track-info panel
		trackTab1DataGV += "<tr align='center' onmouseover='styleCellOnMouseOver(this, cellHoverColor)' onmouseout='styleCellOnMouseOut(this,cellDefaultColor)'>"
				+ "<td>"
				+ (j + 1)
				+ "</td>"
				+ "<td>"
				+ getEyeIcon(j, lats.length)
				+ "</td>"
				+ "<td>"
				+ carNumbers[j]
				+ "</td>"
				+ "<td>"
				+ gpsTimes[j]
				+ "</td>"
				+ "<td>"
				+ speeds[j]
				+ "</td>"
				+ "<td>"
				+ getAzimuth(angles[j])
				+ "</td>"
				+ "<td>"
				+ "<div style='cursor:pointer' onclick=\"new BaiduMap().parseAddress("
				+ latsWithOffset[j]
				+ ","
				+ lonsWithOffset[j]
				+ ",'showAddress',this)\">"
				+ "点击显示位置..."
				+ "</div>"
				+ "</td>" + "<td>" + stateString + "</td>";
		if (isFind) {
			trackTab1DataGV += "<td>" + drivers[j]+ "</td>";
		}
		trackTab1DataGV += "</tr>";
		
		if (j < (lats.length - 1)) {
			trackResGV += latsWithOffset[j] + "," + lonsWithOffset[j] + ","
					+ angles[j] + "," + speeds[j] + ";";
		} else {
			trackResGV += latsWithOffset[j] + "," + lonsWithOffset[j] + ","
					+ angles[j] + "," + speeds[j];
		}

		opts[j] = "offline";
	}

	if (len > 0) {
		trackTab1();
		mapTrack.delMarkerAll();
		mapTrack.delOverlays();

		mapTrack.resizeView(latsWithOffset, lonsWithOffset);
		if (labels.length > 0) {
			labels[0] = "起点";
			labels[labels.length - 1] = "终点";
		}
		mapTrack.addMarkersWithLabelAdvanced(latsWithOffset, lonsWithOffset,
				labels, infos, opts);
		mapTrack.addPolyLine(latsWithOffset, lonsWithOffset, "red", 2);

		mapTrack.showMarker(0);
		mapTrack.showMarker(lats.length - 1);
		if (lats.length <= 1) {
			freezeAnmiation();
		} else {
			unfreezeAnmiation();
		}
	} else {
		alert("未查询到历史数据！请更改查询条件再行查询！");
		freezeAnmiation();
	}

}

function getEyeIcon(index, arrLen) {
	var c;
	if (index == 0) {
		c = "<img id='img_" + index + "' src='" + imgPath
				+ "/xtracking-open.gif' onclick='controlMarkInMap(" + index
				+ ")' onmouseover=\"styleCursorOnMouseOver(this)\"/>"
				+ "<input type='hidden' id='eye_" + index + "' value='1'/>";
	} else if (index == arrLen - 1) {
		c = "<img id='img_" + index + "' src='" + imgPath
				+ "/xtracking-open.gif' onclick='controlMarkInMap(" + index
				+ ")' onmouseover=\"styleCursorOnMouseOver(this)\"/>"
				+ "<input type='hidden' id='eye_" + index + "' value='1'/>";
	} else {
		c = "<img id='img_" + index + "' src='" + imgPath
				+ "/xtracking-close.gif' onclick='controlMarkInMap(" + index
				+ ")' onmouseover=\"styleCursorOnMouseOver(this)\"/>"
				+ "<input type='hidden' id='eye_" + index + "' value='0'/>";
	}
	return c;
}

function changeEyeAction(eyeImgId, eyeHiddenId) {
	var eyeId = $("#" + eyeHiddenId);
	var eyeImg = $("#" + eyeImgId);

	if (eyeId.attr("value") == 1) {
		eyeImg.attr("src", imgPath + "/xtracking-open.gif");
		eyeImg.attr("title", "点击隐藏标注！");
		eyeId.attr("value", 0);
	} else {
		eyeImg.attr("src", imgPath + "/xtracking-close.gif");
		eyeImg.attr("title", "点击显示标注！");
		eyeId.attr("value", 1);
	}
}

function setEyeAction(eyeImgId, eyeHiddenId, val) {
	var eyeId = $("#" + eyeHiddenId);
	var eyeImg = $("#" + eyeImgId);
	eyeId.attr("value", val);
	if (val == 1) {
		eyeImg.attr("src", imgPath + "/xtracking-open.gif");
		eyeImg.attr("title", "点击隐藏标注！");
	} else {
		eyeImg.attr("src", imgPath + "/xtracking-close.gif");
		eyeImg.attr("title", "点击显示标注！");
	}
}

function controlMarkInMap(index) {
	var n = latsWithOffset.length;
	var eyeVal = $("#eye_" + index).attr("value");
	// alert(eyeVal);
	if (eyeVal == 0) {
		// 显示标注
		mapTrack.showMarker(index);
		setEyeAction("img_" + index, "eye_" + index, 1);
	} else {
		// 隐藏标注
		mapTrack.hideMarker(index);
		setEyeAction("img_" + index, "eye_" + index, 0);
	}
}

function controlMarkAllInMap() {
	var n = latsWithOffset.length;
	var eyeVal = $("#eye_th").attr("value");
	if (eyeVal == 1) {
		mapTrack.showMarkerAll();
		changeEyeAction("img_th", "eye_th");
		for (var i = 0; i < n; i++) {
			setEyeAction("img_" + i, "eye_" + i, 1)
		}
	} else {
		mapTrack.hideMarkerAll();
		changeEyeAction("img_th", "eye_th");
		for (var i = 0; i < n; i++) {
			setEyeAction("img_" + i, "eye_" + i, 0)
		}
	}
}

function freezeAnmiation() {
	// alert("Calling the freezeAnmiation()!");
	// document.getElementById("animationBtn").disabled = true;
	$("#animationBtn1").attr("disabled", true);
	$("#animationBtn2").attr("disabled", true);
	$("#saveBtn").attr("disabled", true);
}

function unfreezeAnmiation() {
	// document.getElementById("animationBtn").disabled = false;
	$("#animationBtn1").removeAttr("disabled");
	$("#animationBtn2").removeAttr("disabled");
	$("#saveBtn").removeAttr("disabled");
}

// 显示播放动画函数 [s] ----------------------------------------------
function showAnimation() {
	// alert("Calling the showAnimation()!");
	// if (p != null) {
	// carNumberGV = p;
	// }

	if (cntCallShowAnimation == 0) {
		lyr1 = mapTrack.getOverlaysLen();
		trackLayerOriGV = lyr1;
	}
	cntCallShowAnimation++;

	var val = trackResGV;
	// alert("Size of original layers:" + lyr1);
	var arr = val.split(";");

	for (var i = 0; i < arr.length; i++) {
		latArr[i] = arr[i].split(",")[0];
		lonArr[i] = arr[i].split(",")[1];
		angleArr[i] = arr[i].split(",")[2];
		speedArr[i] = arr[i].split(",")[3];
		labelArr[i] = "序号：" + (i + 1) + "，方位：" + getAzimuth(angleArr[i])
				+ "，时速：" + speedArr[i] + " 公里/时";
	}

	cmdst = "";
	lineColor = "green";
	lineSize = 4;
	btn = $("#animationBtn1");
	len = latArr.length;
	if (btn.val() == "播放") {
		cmdst = "run";
		addPolyLineAnimationExt();
	} else if (btn.val().indexOf("暂停") == 0) {
		cmdst = "pause";
		btn.val("继续");
	} else if (btn.val() == "继续") {
		cmdst = "goon";
		addPolyLineAnimationExt();
	} else {
		cmdst = "stop";
		btn.val("播放");
	}

}

function addPolyLineAnimationExt() {
	// alert("Calling the addPolyLineAnimationExt()!");
	// Desc: add the animated polyline to the map
	if (cmdst == "stop") {
		return false;
	}
	if (cmdst == "pause") {
		return n;
	}
	if (cmdst == "goon") {
		lyr2 = mapTrack.getOverlaysLen();
		mapTrack.delLastOverlays(lyr2 - lyr1); // clean the last overlays.
	}

	if (n < len) {
		var lats = subArray(latArr, 0, n);
		var lons = subArray(lonArr, 0, n);
		var angles = subArray(angleArr, 0, n);
		var labels = subArray(labelArr, 0, n);
		var latLast = lats[lats.length - 1];
		var lonLast = lons[lons.length - 1];
		var angleLast = angles[angles.length - 1];
		var labelLast = labels[labels.length - 1];
		lyr2 = mapTrack.getOverlaysLen();

		btn.val("暂停 (" + lats.length + "/" + len + ")");

		mapTrack.delLastOverlays(lyr2 - lyr1); // clean the last overlays.
		mapTrack.addPolyLine(lats, lons, lineColor, lineSize);
		// addMarkerWithLabel(latLast, lonLast, "", "", lineColor);
		mapTrack.addArrowMarkerWithLabel(latLast, lonLast, angleLast,
				labelLast, "");
		mapTrack.moveView(latLast, lonLast);
		++n;
	} else {
		n = 0;
		lyr2 = mapTrack.getOverlaysLen();
		mapTrack.delLastOverlays(lyr2 - lyr1); // clean the last overlays.
	}

	setTimeout(addPolyLineAnimationExt, 300);
}

function stopAnimation() {
	cmdst = "stop";
	var btn1 = $("#animationBtn1");
	if (btn1.val().indexOf("暂停") > -1 || btn1.val().indexOf("继续") > -1) {
		$("#animationBtn1").val("播放");
		n = 0;
		lyr1 = trackLayerOriGV;
		lyr2 = mapTrack.getOverlaysLen();
		mapTrack.delLastOverlays(lyr2 - lyr1); // clean the last overlays.
	}
}

// 显示播放动画函数 [e] ----------------------------------------------
/**
 * function clearTrackProcess() { // alert("Calling the clearTrackProcess()!"); //
 * close the animation stopAnimation(); // clean the content of info-panel
 * $("#infoOpenDiv").html(""); // infoPanelClose(); }
 */

// contents of info-panel for vehicles tracking
function showTrackLayouts(parm) {
	// alert("Calling the showTrackLayouts()!");
	trackTab1DataGV = "<tr><td colspan=9 "
			+ "style='text-align: left; padding-left: 30px; line-height: 30px;'>"
			+ parm + "</td></tr>";
	trackTab1();
}

function trackTab1() {
	// alert("Calling the basicInfo()!");
	var jqObj = $("#infoOpenDiv");
	var layouts = "<table width='100%' class='tabStyle'>"
			+ "<tr class='trStyle' >"
			+ "<td>&nbsp;序号</td>"
			+ "<td>"
			+ "<img id='img_th' src='"
			+ imgPath
			+ "/xtracking-close.gif' title='点击显示标注' onclick='controlMarkAllInMap()' onmouseover=\"styleCursorOnMouseOver(this)\" />"
			+ "<input type='hidden' id='eye_th' value='1'/>" + "</td>"
			+ "<td>车牌号</td>" + "<td>GPS时间</td>" + "<td>速度（公里/时）</td>"
			+ "<td>行车方向</td>" + "<td>位置</td>" + "<td>当前状态</td>";
	if (isFind) {
		layouts += "<td>司机信息</td>";
	}
	layouts += "</tr>" + trackTab1DataGV + "</table>";
	jqObj.html(layouts);
}

/**
// 调用百度地图API进行地址解析
function parseAddressTrack(lat, lon, callbackFunname, obj) {
	// alert("Calling the parseAddress()!");
	var ak = "6ukSP6o4ff8u9SSnlVEKmiZC";
	var geocoderApi = "http://api.map.baidu.com/geocoder/v2/?ak=" + ak;
	var geocoderUrl = geocoderApi + "&callback=" + callbackFunname
			+ "&location=" + lat + "," + lon + "&output=json&pois=0";
	jQuery.getScript(geocoderUrl);
	addressCursorGV = obj;
}

// 地址解析辅助性函数
function showAddressInInfoTable(data) {
	addressCursorGV.innerHTML = data.result.formatted_address;
}
*/

function saveData() {
	window.open(xlsUrl, '轨迹数据', '');
}
