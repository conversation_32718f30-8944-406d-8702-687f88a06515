<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@taglib prefix="s" uri="/struts-tags"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>车辆信息管理</title>

<link rel="stylesheet" type="text/css"
	href="/808FrontProject/css/user.css">

<!-- date component [s] -->
<script type="text/javascript"
	src="/808FrontProject/js/lib.jsdate/jscal2.js"></script>
<script type="text/javascript"
	src="/808FrontProject/js/lib.jsdate/en.js"></script>
<link rel="stylesheet" type="text/css"
	href="/808FrontProject/js/lib.jsdate/jscal2.css">
<link rel="stylesheet" type="text/css"
	href="/808FrontProject/js/lib.jsdate/border-radius.css">
<link rel="stylesheet" type="text/css"
	href="/808FrontProject/js/lib.jsdate/steel.css">
<!-- date component [e] -->

<script type="text/javascript" src="/808FrontProject/js/lib.jquery.js"></script>
<script type="text/javascript" src="/808FrontProject/js/tools.js"></script>

<!-- treeview component -->
<link rel="stylesheet"
	href="/808FrontProject/js/lib.jtree/jquery.treeview.css" />
<link rel="stylesheet" href="/808FrontProject/js/lib.jtree/screen.css" />
<script src="/808FrontProject/js/lib.jtree/jquery.cookie.js"></script>
<script src="/808FrontProject/js/lib.jtree/jquery.treeview.js"></script>
<script type="text/javascript">
	function addVehicle() {
		$("#modifyVehicleInfoForm").attr("action",
				"/808FrontProject/vehicle/addAction.action").submit();
	}

	function editVehicle() {
		var carNumber = $('input:radio[name="selectedCarNumber"]:checked')
				.val();
		if (undefined == carNumber || carNumber == "") {
			alert("请在单选按钮中选择要编辑的车牌号！");
			return false;
		}

		var path = "808FrontProject/vehicle/editAction.action?carNumber="
				+ carNumber;
		$("#modifyVehicleInfoForm").attr("action", path).submit();
	}

	function deleteVehicle() {
		var carNumber = $('input:radio[name="selectedCarNumber"]:checked')
				.val();
		if (undefined == carNumber || carNumber == "") {
			alert("请在单选按钮中选择要删除的车牌号！");
			return false;
		}
		if (window.confirm("确定要删除当前车辆信息吗？")) {
			var path = "808FrontProject/vehicle/deleteAction.action?carNumber="
					+ carNumber;
			$("#modifyVehicleInfoForm").attr("action", path).submit();
		} else {
			return false;
		}
	}

	$(window).load(
			function() {
				//date componenent
				var cal = Calendar.setup({
					onSelect : function(cal) {
						cal.hide()
					},
					showTime : true
				});
				cal.manageFields("date1Btn", "searchDate1", "%Y-%m-%d");
				cal.manageFields("date2Btn", "searchDate2", "%Y-%m-%d");

				//tree menu of CarType
				$("#black, #gray").treeview({
					//control : "#treecontrol",
					persist : "cookie",
					cookieId : "treeview-black"
				});

				var carTypeMenuElement = document.getElementById("carTypeMenu")
						.getElementsByTagName("a");
				for (var i = 0; i < carTypeMenuElement.length; i++) {
					carTypeMenuElement[i].onclick = function() {
						$("#carType").val(this.innerHTML);
					}
				}

				//tree menu of businessScope
				var hylxe = document.getElementById("hylxMenu")
						.getElementsByTagName("a");
				for (var i = 0; i < hylxe.length; i++) {
					hylxe[i].onclick = function() {
						$("#hylx").val(this.innerHTML);
					}
				}

			});

	//行业类型下拉树形菜单 [S] ---------------------
	function resizeHylx() {
		var obj = document.getElementById("hylx");
		var offsetX = 0;
		var offsetY = $(obj).height() + 5;
		var x = pageX(obj) + offsetX;
		var y = pageY(obj) + offsetY;
		$("#hylxMenu").css("left", x + "px");
		$("#hylxMenu").css("top", y + "px");
	}
	function showHylx() {
		resizeHylx();
		$("#hylxMenu").css("width", "205px");
		$("#hylxMenu").css("height", "300px");
	}
	function closeHylx() {
		$("#hylxMenu").css("width", "0px");
		$("#hylxMenu").css("height", "0px");
	}
	function closeHylxDiv() {
		t = setTimeout('closeHylx()', 100);
	}

	function resizeCarType() {
		var obj = document.getElementById("carType");
		var offsetX = 0;
		var offsetY = $(obj).height() + 5;
		var x = pageX(obj) + offsetX;
		var y = pageY(obj) + offsetY;
		$("#carTypeMenu").css("left", x + "px");
		$("#carTypeMenu").css("top", y + "px");
	}
	function showCarType() {
		resizeCarType();
		$("#carTypeMenu").css("width", "205px");
		$("#carTypeMenu").css("height", "300px");
	}
	function closeCarType() {
		$("#carTypeMenu").css("width", "0px");
		$("#carTypeMenu").css("height", "0px");
	}

	var t;

	//在这里调用setTimeout方法让它在一段很短的时间后执行  
	function closeDiv() {
		t = setTimeout('closeCarType()', 100);
	}
	//将setTimeout事件remove掉  
	function clearTime() {
		clearTimeout(t);
	}
</script>
</head>

<body>
	<span class="title1">车辆信息管理</span>
	<s:form action="queryAction" theme="simple" namespace="/vehicle">
		<table class="tableCommon">
			<tr>
				<td width="150px">车牌号码</td>
				<td><s:textfield name="queryVehicleInfo.carNumber" value=""></s:textfield></td>
				<td width="150px">车牌颜色</td>
				<td><s:textfield name="queryVehicleInfo.carColor" value=""></s:textfield></td>
				<td width="150px">终端ID</td>
				<td><s:textfield name="queryVehicleInfo.endId" value=""></s:textfield></td>
			</tr>
			<tr>
				<td>SIM卡号</td>
				<td><s:textfield name="queryVehicleInfo.simCardNumber" value=""></s:textfield></td>
				<td>车辆类型</td>
				<td><s:textfield name="queryVehicleInfo.carType" id="carType"
						value="" onclick="showCarType()" onmouseout="closeDiv();"
						onmouseover="clearTime();" />
					<div id="carTypeMenu" onmouseout="closeDiv();"
						onmouseover="clearTime();"
						style="position: absolute; z-index: 9000; display: block; overflow-y: auto; background-color: #ffffff; height: 0px; font-size: 14px; line-height: 20px;">
						<ul id="black" class="treeview-black">
							<li></li>
							<li><span><a>出租车</a></span></li>
							<li><span><a>危险品运输车</a></span></li>
							<li><span><a>农用车</a></span></li>
							<li><span><a>拖拉机</a></span>
							<li><span><a>客车</a></span>
								<ul>
									<li><a>大型客车</a></li>
									<li><a>中型客车</a></li>
									<li><a>小型客车</a></li>
									<li><a>轿车</a></li>
									<li><a>大型卧铺客车</a></li>
									<li><a>小型卧铺客车</a></li>
								</ul></li>
							<li><span><a>普通货车</a></span>
								<ul>
									<li><a>大型普通货车</a></li>
									<li><a>中型普通货车</a></li>
									<li><a>小型普通货车</a></li>
								</ul></li>
							<li><span><a>专用运输车</a></span>
								<ul>
									<li><a>集装箱车</a></li>
									<li><a>大件运输车</a></li>
									<li><a>保温冷藏车</a></li>
									<li><a>商品车专用运输车</a></li>
									<li><a>罐车</a></li>
									<li><a>牵引车</a></li>
									<li><a>挂车</a></li>
									<li><a>平板车</a></li>
									<li><a>其他专用车</a></li>

								</ul></li>
							<li><span><a>其他车辆</a></span></li>
						</ul>
					</div></td>
				<td>行业类型</td>
				<td><s:textfield name="queryVehicleInfo.businessScope"
						id="hylx" value="" onclick="showHylx()"
						onmouseout="closeHylxDiv();" onmouseover="clearTime();"></s:textfield>
					<div id="hylxMenu" onmouseout="closeHylxDiv();"
						onmouseover="clearTime();"
						style="position: absolute; z-index: 9000; display: block; overflow-y: auto; background-color: #ffffff; height: 0px; font-size: 14px; line-height: 20px;">
						<ul id="black" class="treeview-black">
							<li></li>
							<li><span><a>道路旅客运输</a></span>
								<ul>
									<li><a>班车客运</a></li>
									<li><a>包车客运</a></li>
									<li><a>定线旅游</a></li>
									<li><a>非定线旅游</a></li>
								</ul></li>
							<li><span><a>道路货物运输</a></span>
								<ul>
									<li><a>道路普通货物运输</a></li>
									<li><a>货物专用运输</a></li>
									<li><a>大型物件运输</a></li>
								</ul></li>
							<li><span><a>道路危险货物运输</a></span>
								<ul>
									<li><a>营运性危险货物运输</a></li>
									<li><a>非经营性危险货物运输</a></li>
								</ul></li>
							<li><span><a>机动车维修</a></span>
								<ul>
									<li><a>汽车维修</a></li>
									<li><a>危险货物运输车辆维修</a></li>
									<li><a>摩托车维修</a></li>
									<li><a>其他机动车维修</a></li>
								</ul></li>
							<li><span><a>机动车驾驶员培训</a></span>
								<ul>
									<li><a>普通机动车驾驶员培训</a></li>
									<li><a>道路运输驾驶员从业资格培训</a></li>
									<li><a>机动车驾驶员培训教练场</a></li>
								</ul></li>
							<li><span><a>公交运输</a></span>

								<ul>
									<li><a>公交运输</a></li>
								</ul></li>
							<li><span><a>出租运输</a></span>
								<ul>
									<li><a>客运出租运输</a></li>
									<li><a>货运出租运输</a></li>
								</ul></li>
							<li><span><a>汽车租凭</a></span>
								<ul>
									<li><a>客运汽车租赁</a></li>
									<li><a>货运汽车租凭</a></li>
								</ul></li>
						</ul>
					</div></td>
			</tr>
			<tr>
				<td>入网时间</td>
				<td><s:textfield name="queryVehicleInfo.networkEntryStartTime"
						id='searchDate1' onFocus='initFormText(this,"","请输入起始时间...")'
						onBlur='initFormText(this,"请输入起始时间...","请输入起始时间...")'
						style='width: 100px; margin-top: 2px' value='请输入起始时间...'
						title="时间格式：yyyy-mm-dd " />
					<button id="date1Btn" type="button">...</button> <s:textfield
						name="queryVehicleInfo.networkEntryStopTime" id='searchDate2'
						onFocus='initFormText(this,"","请输入结束时间...")'
						onBlur='initFormText(this,"请输入结束时间...","请输入结束时间...")'
						style='width: 100px; margin-top: 1px' value='请输入结束时间...'
						title="时间格式：yyyy-mm-dd hh:mm" />
					<button id="date2Btn" type="button">...</button></td>
				<td>车辆组</td>
				<td><input type="text" name="" value="" /></td>
				<td colspan="2" style="padding-left: 0px; text-align: center;"><input
					class="buttonStyle" type="submit" name="" value="查询" /></td>
			</tr>
		</table>
	</s:form>
	<s:form id="modifyVehicleInfoForm">
		<table class="tableCommon">
			<tr class="thSize thColor">
			    <td width="5%">序号</td>
				<td width="10%">车牌号码</td>
				<td width="10%">车牌颜色</td>
				<td width="10%">终端ID</td>
				<td width="10%">SIM卡号</td>
				<td width="10%">车辆类型</td>
				<td width="10%">行业类型</td>
				<td width="10%">入网时间</td>
				<td width="15%">关联司乘人员信息</td>
				<td width="10%">选择</td>
			</tr>
			<s:iterator value="vehicleInfoList" status="st">
				<tr <s:if test="#st.even">bgcolor="#ECF5FF"</s:if>>
				    <td style="padding-left:0px;text-align:center"><s:property value="#st.index+1" /></td>
					<td><s:property value="carNumber" /></td>
					<td><s:property value="carColor" /></td>
					<td><s:property value="endId" /></td>
					<td><s:property value="simCardNumber" /></td>
					<td><s:property value="carType" /></td>
					<td><s:property value="businessScope" /></td>
					<td><s:property value="networkEntryTime" /></td>
					<td></td>
					<td><input class="radioStyle" type="radio"
						name="selectedCarNumber" value='<s:property value="carNumber" />' /></td>
				</tr>
				
			</s:iterator>
			<tr>
				<td colspan="10" style="padding-left: 0px; text-align: center;"><input
					class="buttonStyle" type="button" value="添加" onclick="addVehicle()" />&nbsp;
					<input class="buttonStyle" type="button" value="编辑"
					onclick="editVehicle()" />&nbsp; <input class="buttonStyle"
					type="button" value="删除" onclick="deleteVehicle()" /> &nbsp;<input
					class="buttonStyle" type="button" value="关联司乘人员" /></td>
			</tr>
		</table>
	</s:form>


</body>
</html>