package cn.edu.ntu.dao.common.implement;

import java.sql.SQLException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.support.ClassPathXmlApplicationContext;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;
import org.springframework.stereotype.Service;

import cn.edu.ntu.dao.common.interfaces.VehicleGpsInfoDao;
import cn.edu.ntu.dao.mapper.implement.UploadedDriverInfoMapper;
import cn.edu.ntu.dao.mapper.implement.VehicleGpsInfoMapper;
import cn.edu.ntu.dao.mapper.implement.VehicleGpsInfoMapperForHistory;
import cn.edu.ntu.dao.mapper.implement.VehicleInfoMapper;
import cn.edu.ntu.entity.common.DriverInfo;
import cn.edu.ntu.entity.common.TableInfo;
import cn.edu.ntu.entity.common.UploadedDriverInfo;
import cn.edu.ntu.entity.common.VehicleGpsInfo;
import cn.edu.ntu.entity.common.VehicleGpsInfoForHistory;
import cn.edu.ntu.entity.common.VehicleInfo;
import cn.edu.ntu.message.req.PositionReport;
import cn.edu.ntu.message.req.PositionReport.AdditionnalInfo;
import cn.edu.ntu.service.common.implement.PositionReportRedisServiceImpl;
import cn.edu.ntu.service.common.interfaces.PositionReportRedisService;
import cn.edu.ntu.utils.db.JDBCTemplateForGpsHistory;
import cn.edu.ntu.utils.others.Integer1;

/**
 * Vehicle GPS Info DAO Implementation
 *
 * <AUTHOR>
 * @date 2016-01-26 03:03:47
 */
@Repository
public class VehicleGpsInfoDaoImpl implements VehicleGpsInfoDao{

	@Autowired
	private JdbcTemplate jdbcTemplate;
	
	@Autowired
    private PositionReportRedisService service;

	@Override
	public List<VehicleGpsInfo> search(String searchedCarNumber, String groupName) throws Exception{
		String sql = "select * from lastgps where carnumber like '%" + searchedCarNumber
				+ "%' and carnumber in (select carnumber from vehicleinfo where vehiclegroupname like '%" + groupName
				+ "%')";

		return jdbcTemplate.query(sql, new VehicleGpsInfoMapper());
	}

	@Override
	public VehicleGpsInfo find(String carNumber) throws Exception{
		
		String sql="select * from vehicleinfo where carnumber='"+carNumber+"'";
		
		VehicleInfo vehicleInfo=jdbcTemplate.queryForObject(sql,new VehicleInfoMapper());
		
		PositionReport report=service.load(vehicleInfo.getSimCardNumber());
		
		VehicleGpsInfo info=new VehicleGpsInfo();
		info.setCarNumber(carNumber);
		info.setSimCardNumber(vehicleInfo.getSimCardNumber());
		info.setVehicleGroupName(vehicleInfo.getVehicleGroupName());
		byte[] time = report.getTime();
		String timeString = "20" + Integer1.toHexString(time[0]) + "-" + Integer1.toHexString(time[1]) + "-"
				+ Integer1.toHexString(time[2]) + " " + Integer1.toHexString(time[3]) + ":"
				+ Integer1.toHexString(time[4]) + ":" + Integer1.toHexString(time[5]);
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	    Date gpsTime=simpleDateFormat.parse(timeString);
	    info.setGpsTime(gpsTime);
	    info.setRevTime(report.getRevTime());
	    int longtitude =report.getLongitude();
		float lon = ((float) longtitude) / 1000000;
		int latitude =report.getLatitude();
		float lat = ((float) latitude) / 1000000;
		info.setLon(lon);
		info.setLat(lat);
		info.setAlt(report.getAltitude());
		info.setDirection(report.getDirection());
		info.setState(report.getState());
		info.setWarningSign(report.getWarningSign());
        float speed=((float)report.getSpeed()) / 10;
        info.setSpeed(speed);
        
        return info;
		
	}

	@Override
	public VehicleGpsInfo find(String carNumber, Map<String, VehicleInfo> map) throws Exception {
		VehicleInfo vehicleInfo=map.get(carNumber);
		
        PositionReport report=service.load(vehicleInfo.getSimCardNumber());
		
        if(report!=null){
        	VehicleGpsInfo info=new VehicleGpsInfo();
    		info.setCarNumber(carNumber);
    		info.setSimCardNumber(vehicleInfo.getSimCardNumber());
    		info.setVehicleGroupName(vehicleInfo.getVehicleGroupNameListString());
    		byte[] time = report.getTime();
    		String timeString = "20" + Integer1.toHexString(time[0]) + "-" + Integer1.toHexString(time[1]) + "-"
    				+ Integer1.toHexString(time[2]) + " " + Integer1.toHexString(time[3]) + ":"
    				+ Integer1.toHexString(time[4]) + ":" + Integer1.toHexString(time[5]);
    		SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    	    Date gpsTime=simpleDateFormat.parse(timeString);
    	    info.setGpsTime(gpsTime);
    	    info.setRevTime(report.getRevTime());
    	    int longtitude =report.getLongitude();
    		float lon = ((float) longtitude) / 1000000;
    		int latitude =report.getLatitude();
    		float lat = ((float) latitude) / 1000000;
    		int totalDistance=-1;
    		List<AdditionnalInfo> additionnalInfoList=report.getAdditionnalInfoList();
    		if(additionnalInfoList!=null){
    			for(AdditionnalInfo additionnalInfo:additionnalInfoList){
    				if(additionnalInfo.getAdditionnalInfoId()==0x01){
    					totalDistance=(int) additionnalInfo.getAddtionnalInfoEntity()/10;
    					break;
    				}
    			}
    		}
    		info.setLon(lon);
    		info.setLat(lat);
    		info.setAlt(report.getAltitude());
    		info.setDirection(report.getDirection());
    		//info.setState(report.getState());
    		//modified
    		info.setState(1);
    		info.setWarningSign(report.getWarningSign());
            info.setSpeed(((float)report.getSpeed()) / 10);
            info.setTotalDistance(totalDistance);
            
            return info;
        }
		
        return null;
	}

	@Override
	public List<UploadedDriverInfo> findDriverInfo(List<VehicleGpsInfo> vehicleGpsInfoWithOffsetList) {
		if(!vehicleGpsInfoWithOffsetList.isEmpty()){
			String sql="select a.drivername,a.startdis,a.enddis,a.starttime,a.endtime,a.startlng,a.endlng,a.startlat,a.endlat,c.carnumber from uploadeddriverinfo as a ,vehicleinfo as c where a.endnumber=c.simcardnumber and ((a.id=(select max(id) from uploadeddriverinfo as b,vehicleinfo as d where b.endnumber=d.simcardnumber and d.carnumber='"+vehicleGpsInfoWithOffsetList.get(0).getCarNumber()+"') and a.endtime is null)";
			for(int i=1;i<vehicleGpsInfoWithOffsetList.size();i++)
				sql+=" or (a.id=(select max(id) from uploadeddriverinfo as b ,vehicleinfo as d where b.endnumber=d.simcardnumber and d.carnumber='"+vehicleGpsInfoWithOffsetList.get(i).getCarNumber()+"') and a.endtime is null)";
			sql+=")";
			List<UploadedDriverInfo> driverInfoList=driverInfoList = jdbcTemplate.query(sql, new UploadedDriverInfoMapper());
			return driverInfoList;
		}
		
		return null;
	}

	@Override
	public List<UploadedDriverInfo> findDriverInfoForHistory(List<VehicleGpsInfo> vehicleGpsInfoWithOffsetList) {
		
		SimpleDateFormat simpleDateFormat=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		if(!vehicleGpsInfoWithOffsetList.isEmpty()){
			String sql="select a.drivername,a.startdis,a.enddis,a.starttime,a.endtime,a.startlng,a.endlng,a.startlat,a.endlat,c.carnumber from uploadeddriverinfo as a ,vehicleinfo as c where (a.endnumber=c.simcardnumber and c.carnumber='"+vehicleGpsInfoWithOffsetList.get(0).getCarNumber()+"' and a.starttime<"+simpleDateFormat.format(vehicleGpsInfoWithOffsetList.get(0).getGpsTime())+" and a.endtime>"+simpleDateFormat.format(vehicleGpsInfoWithOffsetList.get(0).getGpsTime())+")";
			for(int i=1;i<vehicleGpsInfoWithOffsetList.size();i++)
				sql+=" or (a.endnumber=c.simcardnumber and c.carnumber='"+vehicleGpsInfoWithOffsetList.get(i).getCarNumber()+" and a.starttime<"+simpleDateFormat.format(vehicleGpsInfoWithOffsetList.get(i).getGpsTime())+" a.endtime>"+simpleDateFormat.format(vehicleGpsInfoWithOffsetList.get(i).getGpsTime())+")";
            System.out.println(sql);
			List<UploadedDriverInfo> driverInfoList=driverInfoList = jdbcTemplate.query(sql, new UploadedDriverInfoMapper());
			return driverInfoList;
		}
		
		return null;
	}


}
