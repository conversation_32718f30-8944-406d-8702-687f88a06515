# 808FrontProject - Maven改造说明

## 项目概述
本项目已成功从传统的Dynamic Web Project改造为Maven架构的项目。这是一个基于SSH（Struts2 + Spring + Hibernate）架构的车辆监控系统。

## Maven改造完成的内容

### 1. 项目结构改造
已创建标准的Maven目录结构：
```
808FrontProject/
├── pom.xml                    # Maven项目配置文件
├── src/
│   ├── main/
│   │   ├── java/             # Java源代码目录
│   │   ├── resources/        # 配置文件和资源目录
│   │   └── webapp/           # Web应用资源目录
│   └── test/
│       ├── java/             # 测试代码目录
│       └── resources/        # 测试资源目录
└── README.md                 # 本说明文件
```

### 2. pom.xml配置
已创建完整的pom.xml文件，包含：

#### 项目基本信息
- **GroupId**: cn.edu.ntu
- **ArtifactId**: 808FrontProject
- **Version**: 1.0.0
- **Packaging**: war
- **Java版本**: 1.8

#### 主要依赖框架
- **Spring Framework**: 4.2.8.RELEASE
- **Struts2**: 2.3.24.1
- **Hibernate**: 4.3.11.Final
- **MySQL**: 5.1.7
- **Redis**: Jedis 2.7.2, Spring Data Redis 1.7.4
- **Quartz**: 2.2.3
- **Jackson**: 2.2.1 (核心) + 1.8.8 (ASL)
- **Apache Commons**: 多个工具库
- **日志**: Log4j 1.2.16 + SLF4J 1.7.7

#### Maven插件配置
- Maven编译插件 (Java 8)
- Maven WAR插件
- Maven资源插件
- Tomcat7插件 (可选)

## 需要手动恢复的内容

由于在改造过程中原始文件被移动，您需要从备份或版本控制系统中恢复以下内容：

### 1. Java源代码
需要恢复到 `src/main/java/` 目录：
```
src/main/java/cn/edu/ntu/
├── action/          # Struts2 Action类
├── dao/            # 数据访问层
├── entity/         # 实体类
├── message/        # 消息相关类
├── quartz/         # 定时任务
├── report/         # 报表相关
├── service/        # 业务逻辑层
└── utils/          # 工具类
```

### 2. 配置文件
需要恢复到 `src/main/resources/` 目录：
```
src/main/resources/
├── config.properties
├── datasource.properties
├── hibernate.properties
├── log4j.properties
├── quartz.properties
├── redis.properties
├── spring-beans.xml
├── spring-quartz.xml
├── struts.xml
└── struts-*.xml (各个模块的struts配置)
```

### 3. Web资源
需要恢复到 `src/main/webapp/` 目录：
```
src/main/webapp/
├── WEB-INF/
│   └── web.xml     # Web应用配置文件
├── css/            # 样式文件
├── js/             # JavaScript文件
├── image/          # 图片资源
├── page/           # JSP页面
├── sound/          # 音频文件
└── file/           # 其他文件
```

**注意**: 不需要恢复 `WEB-INF/lib/` 目录，Maven会自动管理依赖。

## 使用Maven构建项目

### 1. 编译项目
```bash
mvn compile
```

### 2. 运行测试
```bash
mvn test
```

### 3. 打包项目
```bash
mvn package
```
这将在 `target/` 目录下生成 `808FrontProject.war` 文件。

### 4. 清理项目
```bash
mvn clean
```

### 5. 使用Tomcat插件运行
```bash
mvn tomcat7:run
```
项目将在 http://localhost:8080/808FrontProject 运行。

## IDE集成

### Eclipse/STS
1. 导入项目：File → Import → Existing Maven Projects
2. 选择项目根目录
3. Eclipse会自动识别为Maven项目

### IntelliJ IDEA
1. 打开项目：File → Open
2. 选择项目根目录
3. IDEA会自动识别为Maven项目

## 数据库配置
确保在 `src/main/resources/datasource.properties` 中配置正确的数据库连接信息：
```properties
driverClassName=com.mysql.jdbc.Driver
url=*****************************************
username=your_username
password=your_password
```

## Redis配置
确保在 `src/main/resources/redis.properties` 中配置正确的Redis连接信息：
```properties
redis.host=localhost
redis.port=6379
redis.timeout=2000
```

## 部署说明
1. 使用 `mvn package` 生成WAR文件
2. 将生成的WAR文件部署到Tomcat服务器
3. 确保数据库和Redis服务正常运行

## 注意事项
1. 项目使用Java 8编译，确保运行环境支持
2. 某些依赖可能存在版本冲突，如有问题请调整pom.xml中的版本
3. 原项目使用了混合版本的Jackson和Log4j，已在pom.xml中保持兼容
4. 如需升级框架版本，请注意兼容性测试

## 技术栈总结
- **前端**: JSP + JavaScript + CSS
- **后端**: Struts2 + Spring + Hibernate
- **数据库**: MySQL
- **缓存**: Redis
- **调度**: Quartz
- **构建工具**: Maven
- **应用服务器**: Tomcat
