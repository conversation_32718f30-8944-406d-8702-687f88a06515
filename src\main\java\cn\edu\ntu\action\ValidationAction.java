package cn.edu.ntu.action;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import com.opensymphony.xwork2.ActionSupport;

import cn.edu.ntu.service.common.interfaces.DriverInfoService;
import cn.edu.ntu.service.common.interfaces.UserInfoService;
import cn.edu.ntu.service.common.interfaces.VehicleGroupInfoService;
import cn.edu.ntu.service.common.interfaces.VehicleInfoService;

/**
 *
 * 
 * <AUTHOR>
 * @date 2016��12��22�� ����10:29:13 
 */
@Controller
public class ValidationAction extends ActionSupport {
	
	private String userName;
	
	private String carNumber;
	
	private String simCardNumber;

	private String vehicleGroupName;
	
	private String driverId;
	
	private String result;
	
	@Autowired
	private UserInfoService userInfoService;
	@Autowired
	private VehicleInfoService vehicleInfoService;
	@Autowired
	private VehicleGroupInfoService vehicleGroupInfoService;
	@Autowired
	private DriverInfoService driverInfoService;
	
	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getCarNumber() {
		return carNumber;
	}

	public void setCarNumber(String carNumber) {
		this.carNumber = carNumber;
	}

	public String getSimCardNumber() {
		return simCardNumber;
	}

	public void setSimCardNumber(String simCardNumber) {
		this.simCardNumber = simCardNumber;
	}

	public String getVehicleGroupName() {
		return vehicleGroupName;
	}

	public void setVehicleGroupName(String vehicleGroupName) {
		this.vehicleGroupName = vehicleGroupName;
	}

	public String getDriverId() {
		return driverId;
	}

	public void setDriverId(String driverId) {
		this.driverId = driverId;
	}

	public String getResult() {
		return result;
	}

	public void setResult(String result) {
		this.result = result;
	}
	
	public String validateUserName(){
		result=userInfoService.validateUserName(userName);
		return SUCCESS;
	}

	public String validateCarNumber(){
	    result=vehicleInfoService.validateCarNumber(carNumber);
	    return SUCCESS;
	}
	
	public String validateSimCardNumber(){
		result=vehicleInfoService.validateSimCardNumber(simCardNumber);
		return SUCCESS;
	}
	
	public String validateVehicleGroupName(){
		result=vehicleGroupInfoService.validateVehilcleGroupName(vehicleGroupName);
		return SUCCESS;
	}
	
	public String validateDriverId(){
		result=driverInfoService.validateDriverId(driverId);
		return SUCCESS;
	}
	
}
