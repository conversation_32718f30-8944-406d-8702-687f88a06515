<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@taglib prefix="s" uri="/struts-tags"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>

<head>
<title>用户登录</title>
<style type="text/css">
.title {
	font-size: 20px;
	font-weight: bold;
	line-height: 40px;
}

.thColor {
	background-color: #ACD6FF;
}

.tdColor {
	color: #000000;
	background-color: #6D9FDE;
}

.tdLabel {
	width: 80px;
	font-size: 13px;
	text-align: center;
	line-height: 25px;
}

.tdCommon {
	font-size: 13px;
	height: 25px;
}

input {
	margin-left: 10px;
	width: 100px;
	font-size: 13px;
}

.buttonStyle {
	height: 25px;
	width: 80px;
	font-size: 13px;
	height: 25px;
}
</style>
<script type="text/javascript" src="/808FrontProject/js/lib.jquery.js"></script>
<script type="text/javascript">
	//added by Dawn, Che
	function loginValidation() {
		//alert("Calling the submit()!");
		var n = $("input[name='userName']");
		var p = $("input[name='userPassword']");
		if (n.val() == "") {
			alert("请输入用户名！");
			n.focus();
			return false;
		}
		if (p.val() == "") {
			alert("请输入密码！");
			p.focus();
			return false;
		}
		$("#loginForm").attr("action",
				"/808FrontProject/user/loginAction.action").submit();
	}
</script>
</head>

<body>
	<center>
		<div class="title">
			<img style="margin: 10px" src="/808FrontProject/image/tpy.jpg" />
		</div>

		<table border=0
			style="border-collapse: collapse; border-spacing: 1px; background: #E9EAEE; border-color: #CCC">

			<tr>
				<td><img style="margin: 10px"
					src="/808FrontProject/image/traffic3.jpg" width=306 height=200 /></td>
				<td><s:form method="post" theme="simple" id="loginForm">
						<table border=0 style="margin: 10px;">
							<tr>
								<td class="tdColor tdLabel">用户名</td>
								<td><s:textfield name="userName"></s:textfield></td>
							</tr>
							<tr>
								<td class="tdColor tdLabel">密&nbsp;码</td>
								<td><s:password name="userPassword"></s:password></td>
							</tr>
							<tr>

								<td class="tdColor tdLabel">验证码</td>
								<td><s:textfield name="checkCode"></s:textfield> <img
									src="/808FrontProject/common/createCheckCodeImageAction.action"
									onclick="this.src='/808FrontProject/common/createCheckCodeImageAction.action?'+ Math.random()"
									title="点击图片刷新验证码" /></td>
							</tr>
							<tr>
								<td class="tdCommon"></td>
								<td class="tdCommon"><s:actionerror cssStyle="color:red" /></td>
							</tr>
							<tr>
								<td colspan="2"><input type="button" value="登录"
									style="margin-left: 0px; margin-top: 5px" class="buttonStyle"
									onclick="loginValidation();" /> <input type="button"
									value="忘记密码" class="buttonStyle" /></td>

							</tr>
						</table>
					</s:form></td>
			</tr>
			<tr style="background: #FFFFFF">
				<td class="tdCommon"><div
						style="margin: 10px; float: left; line-height: 20px;">
						符合道路运输车辆卫星定位系统标准<br />平台编号：<br />
					</div></td>
				<td class="tdCommon"><div
						style="margin: 10px; float: left; line-height: 20px; color: #666">版本号：V1.0
						版权所有 © 2016-2025<br />
						技术支持：南通大学地理科学学院</div></td>
			</tr>

		</table>
	</center>
</body>
</html>