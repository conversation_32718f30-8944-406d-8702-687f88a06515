<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@taglib prefix="s" uri="/struts-tags"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">

<title>车辆跟踪</title>
<meta http-equiv="pragma" content="no-cache">
<meta http-equiv="cache-control" content="no-cache">
<meta http-equiv="expires" content="0">
<meta http-equiv="keywords" content="太平洋,北斗,位置服务,云服务,车辆定位,车辆查询,车辆导航">
<meta http-equiv="description" content="太平洋北斗位置云">


<link rel="stylesheet" type="text/css"
	href="/808FrontProject/css/searchres.css">

<script type="text/javascript" src="/808FrontProject/js/lib.jquery.js"></script>
<script type="text/javascript" src="/808FrontProject/js/lib.json2.js"></script>

<script type="text/javascript" src="/808FrontProject/js/tools.js"></script>
<script type="text/javascript" src="/808FrontProject/js/baidumap.js"></script>

<script type="text/javascript" src="/808FrontProject/js/frame.js"></script>
<script type="text/javascript" src="/808FrontProject/js/style.js"></script>

<script type="text/javascript" src="/808FrontProject/js/var.js"></script>
<script type="text/javascript" src="/808FrontProject/js/nav.js"></script>

<script type="text/javascript"
	src="http://api.map.baidu.com/api?v=2.0&ak=6ukSP6o4ff8u9SSnlVEKmiZC"></script>
<script type="text/javascript"
	src="http://api.map.baidu.com/library/DrawingManager/1.4/src/DrawingManager.js"></script>
<script type="text/javascript"
	src="http://api.map.baidu.com/library/MarkerManager/1.2/src/MarkerManager.js"></script>


<style type="text/css">
body {
	font-family: 宋体;
}

input {
	font-size: 14px;
}

.commonFontSize {
	font-size: 14px;
}
</style>
<script type="text/javascript">
	var p = parseParametersFromURL("p");
	if (p != null) {
		p = decodeUTF8(p);
	}

	var latArray = [];
	var lonArray = [];
	var labelArray = [];
	var infoArray = [];
	var dataArray = [];
	var optArray = [];

	$(window).load(function() {
		InitInfoTable();
		showMapPanel();

		mapFocusMonitor = new BaiduMap("mapDiv");
		mapFocusMonitor.init();

		showFocusMonitorTrack(p);
		intervalVar = setInterval(function() {
			showFocusMonitorTrack(p);
		}, 30000);
	});

	$(window).resize(function() {
		showMapPanel();
	});

	function showFocusMonitorTrack(carNumber) {
		$
				.ajax({
					url : "/808FrontProject/showVehicle/showSingleVehicleAction",
					data : {
						"carNumber" : carNumber
					},
					dataType : "json",
					type : "post",
					success : function(data) {
						mapFocusMonitor.delOverlays();
						//居中当前位置
						mapFocusMonitor.centerView(data.latWithOffset,
								data.lonWithOffset);
						latArray.push(data.latWithOffset);
						lonArray.push(data.lonWithOffset);
						labelArray.push(data.carNumber);
						var info = "<div style='font-size:13px;line-height:22px;'>"
								+ "<div style='font-weight:bold;background-color:#c1e0ff;width:200px;height:30px'>车辆详细信息</div>"
								+ "车牌号: "
								+ data.carNumber
								+ "<br/>"
								+ "经度: "
								+ data.lon
								+ "<br/>"
								+ "纬度: "
								+ data.lat
								+ "</br>"
								+ "速度："
								+ data.speed
								+ " 公里/时<br/>"
								+ "行车方向： "
								+ getAzimuth(data.direction)
								+ " ("
								+ data.direction
								+ " deg)<br/>"
								+ "GPS采集时间："
								+ data.gpsTime
								+ "<br/>"
								+ "当前位置："
								+ "<span id='currentAddress' style='cursor:pointer' onclick=\"new BaiduMap().parseAddress("
								+ data.latWithOffset
								+ ","
								+ data.lonWithOffset
								+ ",'showAddress')\">"
								+ "点击显示位置..."
								+ "</span>" + "</div>";
						var opt = "offline";
						infoArray.push(info);
						optArray.push(opt);

						mapFocusMonitor.addMarkersWithLabel(latArray, lonArray,
								labelArray, infoArray, optArray);

						mapFocusMonitor.addMarkerWithLabel(data.latWithOffset,
								data.lonWithOffset, data.carNumber, info,
								"online")
						mapFocusMonitor.addPolyLine(latArray, lonArray, "red",
								2);

						dataArray.push(data);
						parseResultsOnInfoDiv(dataArray);

					}
				});
	}
</script>
</head>

<body>
	<table border=0
		style="width: 100%; height: 100%; border-collapse: collapse; border-spacing: 0px;">
		<tr>
			<td>
				<div id="mapDiv"></div>
			</td>
		</tr>
		<tr>
			<td style="width: 100%; height: 150px; vertical-align: top">
				<div id="infoOpenDiv"
					style="overflow: auto; height: 100%; width: 100%"></div>
			</td>
		</tr>
	</table>

</body>

</html>
