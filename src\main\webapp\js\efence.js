function clearFenceInfo() {
	mapFence.delOverlays();
	document.getElementById("fenceCds").innerHTML = "&nbsp正在绘制电子围栏图形...";
	document.getElementById("fenceParam").innerHTML = "&nbsp正在绘制电子围栏图形...";
	// 追加需要清空的内容

}

function addCircle() {
	// 增加圆形区域
	clearFenceInfo();

	var pts = [];
	var pt;
	var latArr = [];
	var lonArr = [];
	var lat0, lon0, lat1, lon1;
	var isEditorCircle = true;
	
	mapFence.getMap().addEventListener("click", function(e) {
		if (isEditorCircle) {
			if (pts.length < 1) {
				pt = new BMap.Point(e.point.lng, e.point.lat);
				// alert(e.point.lng+","+e.point.lat);
				pts.push(pt);
				lat0 = pts[0].lat;
				lon0 = pts[0].lng;
				mapFence.addMarkerWithLabel(lat0, lon0, "圆心", "", "");
				latArr[0] = lat0;
				lonArr[0] = lon0;
			}
		}
	});

	mapFence.getMap().addEventListener("mousemove", function(e) {
		if (isEditorCircle) {
			// pt = new BMap.Point(e.point.lng, e.point.lat);
			if (pts.length == 1) {
				lat1 = e.point.lat;
				lon1 = e.point.lng;
				latArr[1] = lat1;
				lonArr[1] = lon1;

				mapFence.delOverlays();
				mapFence.addPolyLine(latArr, lonArr, "#00FF00", 3);
				mapFence.addMarkerWithLabel(lat0, lon0, "圆心", "", "");
			}
		}

	});

	mapFence
			.getMap()
			.addEventListener(
					"rightclick",
					function(e) {
						if (isEditorCircle) {
							if (latArr.length == 2) {
								var dis = mapFence.getDistance(lat0, lon0,
										lat1, lon1);

								mapFence.addCircle(lat0, lon0, dis);
								isEditorCircle = false;

								// 处理属性数据
								document.getElementById("fenceCds").innerHTML = "";
								var fenceCdsCodes = "<table width='100%' class='tabStyle'>"
										+ "<tr class='trStyle' >"
										+ "<td>&nbsp;序号</td>"
										+ "<td>经度</td>"
										+ "<td>纬度</td>"
										+ "<td>位置</td>"
										+ "<td>备注</td>" + "</tr>";

								fenceCdsCodes += "<tr align='center' onmouseover='styleCellOnMouseOver(this, cellHoverColor)' onmouseout='styleCellOnMouseOut(this,cellDefaultColor)'>"
										+ "<td>&nbsp;1</td>"
										+ "<td>"
										+ lonArr[0]
										+ "</td>"
										+ "<td>"
										+ latArr[0]
										+ "</td>"
										+ "<td style='cursor:pointer' onclick=\"new BaiduMap().parseAddress("
										+ latArr[0]
										+ ","
										+ lonArr[0]
										+ ",'showAddress',this)\">点击查询位置</td>"
										+ "<td>圆心点</td>" + "</tr>";
								fenceCdsCodes += "</table>";
								document.getElementById("fenceCds").innerHTML = fenceCdsCodes;

								var fenceParamCodes = "<table width='100%' class='tableCommon'>"
										+ "<tr>"
										+ "<td class='thColor'>区域类型</td>"
										+ "<td ><select id='areaType'><option value='1'>进区域报警</option><option value='2'>出区域报警</option></select></td>"
										+ "<td class='thColor'>区域名称</td>"
										+ "<td ><input style='width:120px' id='areaName' value=''/></td>"
										+ "<td class='thColor'>区域别名</td>"
										+ "<td ><input style='width:120px' id='areaAlias' value=''/></td>"
										+ "</tr>"
										+ "<tr>"
										+ "<td class='thColor'>开始时间</td>"
										+ "<td ><input style='width:120px' id='startTime' value=''/></td>"
										+ "<td class='thColor'>结束时间</td>"
										+ "<td ><input style='width:120px' id='endTime' value=''/></td>"
										+ "<td class='thColor'>限速(Km/h)</td>"
										+ "<td ><input style='width:120px' id='maxSpeed' value=''/></td>"
										+ "</tr>"
										+ "<tr>"
										+ "<td class='thColor'>超速持续时间(s)</td>"
										+ "<td ><input style='width:120px' id='lastTimeOverSpeed' value=''/></td>"
										+ "<td ></td>"
										+ "<td ></td>"
										+ "<td ></td>"
										+ "<td ></td>"
										+ "</tr>"
										+ "<tr>"
										+ "<td colspan=6 style='text-align:right'><input type='button' id='saveSettingButton' style='width:80px' value='保存' >"
										+ " &nbsp;"
										+ "<input type='button' style='width:80px' value='取消'>"
										+ " &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>"
										+ "</tr>" + "</table>";
								document.getElementById("fenceParam").innerHTML = fenceParamCodes;

								$("#saveSettingButton")
										.click(
												function() {

													var areaType = $(
															"#areaType").val();
													var areaName = $(
															"#areaName").val();
													var areaAlias = $(
															"#areaAlias").val();
													var startTime = $(
															"#startTime").val();
													var endTime = $("#endTime")
															.val();
													var maxSpeed = $(
															"#maxSpeed").val();
													var lastTimeOverSpeed = $(
															"#lastTimeOverSpeed")
															.val();
													var radius = parseInt(dis);
													var centerLat = latArr[0];
													var centerLng = lonArr[0];

													$
															.ajax({
																url : "/808FrontProject/eFence/addRoundAreaSettingAction.action",
																data : {
																	areaType : areaType,
																	areaName : areaName,
																	areaAlias : areaAlias,
																	startTime : startTime,
																	endTime : endTime,
																	maxSpeed : maxSpeed,
																	lastTimeOverSpeed : lastTimeOverSpeed,
																	radius : radius,
																	centerLat : centerLat,
																	centerLng : centerLng
																},
																type : "post"
															});

												});
							}
						}
					});
}

function addRectangleOld() {
	clearFenceInfo();

	var pts = [];
	var pt;
	var latArr = [];
	var lonArr = [];
	var tipArr = [];
	var infArr = [];
	var optArr = [];

	var latNode, lonNode;
	var latArrTmp = [];
	var lonArrTmp = [];

	var lat0, lon0;

	var isEditorRec = true;
	mapFence.getMap().addEventListener(
			"click",
			function(e) {
				// alert(e.point.lng+","+e.point.lat);
				if (isEditorRec) {
					if (pts.length < 1) {
						pt = new BMap.Point(e.point.lng, e.point.lat);
					} else {
						pt = new BMap.Point(lonNode, latNode);
					}
					pts.push(pt);
					// alert("点数：" + pts.length);

					if (pts.length >= 1 && pts.length <= 3) {
						var ptIdx = pts.length;
						lat0 = pts[ptIdx - 1].lat;
						lon0 = pts[ptIdx - 1].lng;
						// alert(lat+", "+lon);
						mapFence.addMarkerWithLabel(lat0, lon0, "", "");
						latArr[ptIdx - 1] = lat0;
						lonArr[ptIdx - 1] = lon0;

						tipArr[ptIdx - 1] = "节点" + ptIdx;
						infArr[ptIdx - 1] = "";
						optArr[ptIdx - 1] = "";

						if (ptIdx == 1) {
							mapFence.addMarkersWithLabel(latArr, lonArr,
									tipArr, infArr, optArr);
						} else {
							mapFence.delOverlays();
							mapFence.addPolyLine(latArr, lonArr, "#FF0000", 3);
							mapFence.addMarkersWithLabel(latArr, lonArr,
									tipArr, infArr, optArr);
						}

					}

				}
			});

	mapFence.getMap()
			.addEventListener(
					"mousemove",
					function(e) {
						if (isEditorRec) {
							pt = new BMap.Point(e.point.lng, e.point.lat);
							var latTmp = pt.lat;
							var lonTmp = pt.lng;
							if (pts.length >= 1 && pts.length <= 2) {
								var ptIdx = pts.length;
								var latRef = pts[ptIdx - 1].lat;
								var lonRef = pts[ptIdx - 1].lng;
								var def = mapFence.getDeflection(latRef,
										lonRef, latTmp, lonTmp);

								var olLen = mapFence.getOverlaysLen();
								if (olLen > (ptIdx + 1)) {
									mapFence.delLastOverlays(1);
								}

								latArrTmp[0] = latRef;
								lonArrTmp[0] = lonRef;
								if (def >= 0 && def < 45 || def >= 315
										&& def <= 360) {
									lonArrTmp[1] = lonArr[ptIdx - 1];
									latArrTmp[1] = latTmp;
								}
								if (def >= 45 && def < 135) {
									latArrTmp[1] = latArr[ptIdx - 1];
									lonArrTmp[1] = lonTmp;

								}
								if (def >= 135 && def < 225) {
									lonArrTmp[1] = lonArr[ptIdx - 1];
									latArrTmp[1] = latTmp;
								}
								if (def >= 225 && def < 315) {
									latArrTmp[1] = latArr[ptIdx - 1];
									lonArrTmp[1] = lonTmp;
								}

								latNode = latArrTmp[1];
								lonNode = lonArrTmp[1];

								mapFence.addPolyLine(latArrTmp, lonArrTmp,
										"#00FF00", 3);
								// console.log("Deflection"+ptIdx+": " + def);
							}

							if (pts.length == 3) {
								var ptIdx = pts.length;
								var latRef = pts[ptIdx - 1].lat;
								var lonRef = pts[ptIdx - 1].lng;
								latTmp = latRef;
								lonTmp = lonArr[0];
								var def = mapFence.getDeflection(latRef,
										lonRef, latTmp, lonTmp);

								var olLen = mapFence.getOverlaysLen();
								if (olLen > (ptIdx + 1)) {
									mapFence.delLastOverlays(1);
								}

								latArrTmp[0] = latRef;
								lonArrTmp[0] = lonRef;
								latArrTmp[1] = latTmp;
								lonArrTmp[1] = lonTmp;

								latNode = latArrTmp[1];
								lonNode = lonArrTmp[1];

								mapFence.addPolyLine(latArrTmp, lonArrTmp,
										"#00FF00", 3);
								// console.log("Deflection3: "+def+", lat3:
								// "+latNode+", lon3:
								// "+lonNode
								// +", lat2: "+latArr[1]+", lon2: "+lonArr[1]
								// +", lat1: "+latArr[0]+", lon1: "+lonArr[0]);
							}
						}
					});

	mapFence
			.getMap()
			.addEventListener(
					"rightclick",
					function(e) {
						if (isEditorRec) {
							if (pts.length < 1) {
								pt = new BMap.Point(e.point.lng, e.point.lat);
							} else {
								pt = new BMap.Point(lonNode, latNode);
							}
							pts.push(pt);
							if (pts.length == 4) {
								latArr[3] = pts[3].lat;
								lonArr[3] = pts[3].lng;
								tipArr[3] = "节点4";
								infArr[3] = "";
								optArr[3] = "";

								latArr[4] = pts[0].lat;
								lonArr[4] = pts[0].lng;
								tipArr[4] = "节点1";
								infArr[4] = "";
								optArr[4] = "";

								mapFence.delOverlays();
								mapFence.addPolygon(latArr, lonArr, "#FF0000",
										3);
								mapFence.addMarkersWithLabel(latArr, lonArr,
										tipArr, infArr, optArr);

								// 处理属性数据
								document.getElementById("fenceCds").innerHTML = "";
								var fenceCdsCodes = "<table width='100%' class='tabStyle'>"
										+ "<tr class='trStyle' >"
										+ "<td>&nbsp;序号</td>"
										+ "<td>经度</td>"
										+ "<td>纬度</td>"
										+ "<td>位置</td>"
										+ "<td>备注</td>" + "</tr>";

								for (var i = 0; i < latArr.length - 1; i++) {
									fenceCdsCodes += "<tr align='center' onmouseover='styleCellOnMouseOver(this, cellHoverColor)' onmouseout='styleCellOnMouseOut(this,cellDefaultColor)'>"
											+ "<td>&nbsp;"
											+ (i + 1)
											+ "</td>"
											+ "<td>"
											+ lonArr[i]
											+ "</td>"
											+ "<td>"
											+ latArr[i]
											+ "</td>"
											+ "<td style='cursor:pointer' onclick=\"new BaiduMap().parseAddress("
											+ latArr[i]
											+ ","
											+ lonArr[i]
											+ ",'showAddress',this)\">点击查询位置</td>"
											+ "<td>"
											+ tipArr[i]
											+ "</td>"
											+ "</tr>";
								}
								fenceCdsCodes += "</table>";
								document.getElementById("fenceCds").innerHTML = fenceCdsCodes;

								var fenceParamCodes = "<table width='100%' class='tableCommon'>"
										+ "<tr>"
										+ "<td class='thColor'>区域类型</td>"
										+ "<td ><select id='areaType'><option value='1'>进区域报警</option><option value='2'>出区域报警</option></select></td>"
										+ "<td class='thColor'>区域名称</td>"
										+ "<td ><input style='width:120px' id='areaName' value=''/></td>"
										+ "<td class='thColor'>区域别名</td>"
										+ "<td ><input style='width:120px' id='areaAlias' value=''/></td>"
										+ "</tr>"
										+ "<tr>"
										+ "<td class='thColor'>开始时间</td>"
										+ "<td ><input style='width:120px' id='startTime' value=''/></td>"
										+ "<td class='thColor'>结束时间</td>"
										+ "<td ><input style='width:120px' id='endTime' value=''/></td>"
										+ "<td class='thColor'>限速(Km/h)</td>"
										+ "<td ><input style='width:120px' id='maxSpeed' value=''/></td>"
										+ "</tr>"
										+ "<tr>"
										+ "<td class='thColor'>超速持续时间(s)</td>"
										+ "<td ><input style='width:120px' id='lastTimeOverSpeed' value=''/></td>"
										+ "<td ></td>"
										+ "<td ></td>"
										+ "<td ></td>"
										+ "<td ></td>"
										+ "</tr>"
										+ "<tr>"
										+ "<td colspan=6 style='text-align:right'><input type='button' id='saveSettingButton' style='width:80px' value='保存' >"
										+ " &nbsp;"
										+ "<input type='button' style='width:80px' value='取消'>"
										+ " &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>"
										+ "</tr>" + "</table>";
								document.getElementById("fenceParam").innerHTML = fenceParamCodes;

								$("#saveSettingButton")
										.click(
												function() {
													var areaType = $(
															"#areaType").val();
													var areaName = $(
															"#areaName").val();
													var areaAlias = $(
															"#areaAlias").val();
													var startTime = $(
															"#startTime").val();
													var endTime = $("#endTime")
															.val();
													var maxSpeed = $(
															"#maxSpeed").val();
													var lastTimeOverSpeed = $(
															"#lastTimeOverSpeed")
															.val();
													var latOnLeftAndTop = latArr[0];
													var lngOnLeftAndTop = lonArr[0];
													var latOnRightAndBottom = latArr[2];
													var lngOnRightAndBottom = lonArr[2];

													$
															.ajax({
																url : "/808FrontProject/eFence/addRectangleAreaSettingAction.action",
																data : {
																	areaType : areaType,
																	areaName : areaName,
																	areaAlias : areaAlias,
																	startTime : startTime,
																	endTime : endTime,
																	maxSpeed : maxSpeed,
																	lastTimeOverSpeed : lastTimeOverSpeed,
																	latOnLeftAndTop : latOnLeftAndTop,
																	lngOnLeftAndTop : lngOnLeftAndTop,
																	latOnRightAndBottom : latOnRightAndBottom,
																	lngOnRightAndBottom : lngOnRightAndBottom,
																},
																type : "post"
															});
												});
							}
						}
						isEditorRec = false;
					});

}

function addRectangle() {
	clearFenceInfo();

	var pts = [];
	var pt;
	var latArr = [];
	var lonArr = [];
	var tipArr = [];
	var infArr = [];
	var optArr = [];

	var latArrTmp = [];
	var lonArrTmp = [];

	var isEditorRec = true;
	mapFence.getMap().addEventListener("click", function(e) {
		// alert(e.point.lng+","+e.point.lat);
		if (isEditorRec) {
			if (pts.length < 1) {
				pt = new BMap.Point(e.point.lng, e.point.lat);
				pts.push(pt);

				var latTmp = e.point.lat;
				var lonTmp = e.point.lng;
				mapFence.addMarkerWithLabel(latTmp, lonTmp, "起点", "", "");
			}
			// alert("点数：" + pts.length);
		}
	});

	mapFence.getMap().addEventListener(
			"mousemove",
			function(e) {
				if (isEditorRec) {
					// pt = new BMap.Point(e.point.lng, e.point.lat);
					var latTmp = e.point.lat;
					var lonTmp = e.point.lng;

					if (pts.length == 1) {
						var latRef = pts[0].lat;
						var lonRef = pts[0].lng;

						latArr[0] = latRef;
						lonArr[0] = lonRef;

						latArr[1] = latRef;
						lonArr[1] = lonTmp;

						latArr[2] = latTmp;
						lonArr[2] = lonTmp;

						latArr[3] = latTmp;
						lonArr[3] = lonRef;

						latArr[4] = latRef;
						lonArr[4] = lonRef;

						tipArr[0] = "";
						infArr[0] = "";
						optArr[0] = "";

						tipArr[1] = "";
						infArr[1] = "";
						optArr[1] = "";

						tipArr[2] = "";
						infArr[2] = "";
						optArr[2] = "";

						tipArr[3] = "";
						infArr[3] = "";
						optArr[3] = "";

						tipArr[4] = "";
						infArr[4] = "";
						optArr[4] = "";

						mapFence.delOverlays();
						mapFence.addMarkerWithLabel(latArr[0], lonArr[0], "起点",
								"", "");
						mapFence.addPolyLine(latArr, lonArr, "#00FF00", 3);
						// console.log("Deflection"+ptIdx+": " + def);
					}

				}
			});

	mapFence
			.getMap()
			.addEventListener(
					"rightclick",
					function(e) {
						if (isEditorRec) {
							// alert(latArr.length);
							if (latArr.length == 5) {// 矩形保存5个点后
								mapFence.delOverlays();
								mapFence.addPolygon(latArr, lonArr, "#FF0000",
										3);

								// 处理属性数据
								document.getElementById("fenceCds").innerHTML = "";
								var fenceCdsCodes = "<table width='100%' class='tabStyle'>"
										+ "<tr class='trStyle' >"
										+ "<td>&nbsp;序号</td>"
										+ "<td>经度</td>"
										+ "<td>纬度</td>"
										+ "<td>位置</td>"
										+ "<td>备注</td>" + "</tr>";

								for (var i = 0; i < latArr.length - 1; i++) {
									fenceCdsCodes += "<tr align='center' onmouseover='styleCellOnMouseOver(this, cellHoverColor)' onmouseout='styleCellOnMouseOut(this,cellDefaultColor)'>"
											+ "<td>&nbsp;"
											+ (i + 1)
											+ "</td>"
											+ "<td>"
											+ lonArr[i]
											+ "</td>"
											+ "<td>"
											+ latArr[i]
											+ "</td>"
											+ "<td style='cursor:pointer' onclick=\"new BaiduMap().parseAddress("
											+ latArr[i]
											+ ","
											+ lonArr[i]
											+ ",'showAddress',this)\">点击查询位置</td>"
											+ "<td>"
											+ tipArr[i]
											+ "</td>"
											+ "</tr>";
								}
								fenceCdsCodes += "</table>";
								document.getElementById("fenceCds").innerHTML = fenceCdsCodes;

								var fenceParamCodes = "<table width='100%' class='tableCommon'>"
										+ "<tr>"
										+ "<td class='thColor'>区域类型</td>"
										+ "<td ><select id='areaType'><option value='1'>进区域报警</option><option value='2'>出区域报警</option></select></td>"
										+ "<td class='thColor'>区域名称</td>"
										+ "<td ><input style='width:120px' id='areaName' value=''/></td>"
										+ "<td class='thColor'>区域别名</td>"
										+ "<td ><input style='width:120px' id='areaAlias' value=''/></td>"
										+ "</tr>"
										+ "<tr>"
										+ "<td class='thColor'>开始时间</td>"
										+ "<td ><input style='width:120px' id='startTime' value=''/></td>"
										+ "<td class='thColor'>结束时间</td>"
										+ "<td ><input style='width:120px' id='endTime' value=''/></td>"
										+ "<td class='thColor'>限速(Km/h)</td>"
										+ "<td ><input style='width:120px' id='maxSpeed' value=''/></td>"
										+ "</tr>"
										+ "<tr>"
										+ "<td class='thColor'>超速持续时间(s)</td>"
										+ "<td ><input style='width:120px' id='lastTimeOverSpeed' value=''/></td>"
										+ "<td ></td>"
										+ "<td ></td>"
										+ "<td ></td>"
										+ "<td ></td>"
										+ "</tr>"
										+ "<tr>"
										+ "<td colspan=6 style='text-align:right'><input type='button' id='saveSettingButton' style='width:80px' value='保存' >"
										+ " &nbsp;"
										+ "<input type='button' style='width:80px' value='取消'>"
										+ " &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>"
										+ "</tr>" + "</table>";
								document.getElementById("fenceParam").innerHTML = fenceParamCodes;

								$("#saveSettingButton")
										.click(
												function() {
													var areaType = $(
															"#areaType").val();
													var areaName = $(
															"#areaName").val();
													var areaAlias = $(
															"#areaAlias").val();
													var startTime = $(
															"#startTime").val();
													var endTime = $("#endTime")
															.val();
													var maxSpeed = $(
															"#maxSpeed").val();
													var lastTimeOverSpeed = $(
															"#lastTimeOverSpeed")
															.val();
													var latOnLeftAndTop = latArr[0];
													var lngOnLeftAndTop = lonArr[0];
													var latOnRightAndBottom = latArr[2];
													var lngOnRightAndBottom = lonArr[2];

													$
															.ajax({
																url : "/808FrontProject/eFence/addRectangleAreaSettingAction.action",
																data : {
																	areaType : areaType,
																	areaName : areaName,
																	areaAlias : areaAlias,
																	startTime : startTime,
																	endTime : endTime,
																	maxSpeed : maxSpeed,
																	lastTimeOverSpeed : lastTimeOverSpeed,
																	latOnLeftAndTop : latOnLeftAndTop,
																	lngOnLeftAndTop : lngOnLeftAndTop,
																	latOnRightAndBottom : latOnRightAndBottom,
																	lngOnRightAndBottom : lngOnRightAndBottom,
																},
																type : "post"
															});
												});
							}
						}
						isEditorRec = false;
					});

}

function addPolyline() {
	clearFenceInfo();

	var pts = [];
	var pt;
	var latArr = [];
	var lonArr = [];
	var tipArr = [];
	var infArr = [];
	var optArr = [];

	var latNode, lonNode;
	var latArrTmp = [];
	var lonArrTmp = [];

	var isEditorPL = true;

	var lat0, lon0, lat1, lon1;
	mapFence.getMap().addEventListener(
			"click",
			function(e) {
				// alert(e.point.lng+","+e.point.lat);
				if (isEditorPL) {
					pt = new BMap.Point(e.point.lng, e.point.lat);
					pts.push(pt);
					// alert("点数：" + pts.length);
					if (pts.length == 1) {
						lat0 = pts[0].lat;
						lon0 = pts[0].lng;
						// alert(lat+", "+lon);
						latArr[0] = lat0;
						lonArr[0] = lon0;
						tipArr[0] = "起点";
						infArr[0] = "";
						optArr[0] = "";
						mapFence.addMarkersWithLabel(latArr, lonArr, tipArr,
								infArr, optArr);
					} else {
						var ptLen = pts.length;
						lat1 = pts[ptLen - 1].lat;
						lon1 = pts[ptLen - 1].lng;
						// alert(lat+", "+lon);
						mapFence.addMarkerWithLabel(lat1, lon1, "节点1", "");
						latArr[ptLen - 1] = lat1;
						lonArr[ptLen - 1] = lon1;
						tipArr[ptLen - 1] = "节点" + ptLen;
						;
						infArr[ptLen - 1] = "";
						optArr[ptLen - 1] = "";

						mapFence.delOverlays();
						mapFence.addPolyLine(latArr, lonArr, "#FF0000", 3);
						mapFence.addMarkersWithLabel(latArr, lonArr, tipArr,
								infArr, optArr);
					}
				}
			});

	mapFence.getMap().addEventListener("mousemove", function(e) {
		if (isEditorPL) {
			pt = new BMap.Point(e.point.lng, e.point.lat);
			var latTmp = pt.lat;
			var lonTmp = pt.lng;
			if (pts.length >= 1) {
				var ptLen = pts.length;
				var latRef = pts[ptLen - 1].lat;
				var lonRef = pts[ptLen - 1].lng;

				var olLen = mapFence.getOverlaysLen();
				if (olLen > (ptLen + 1)) {
					mapFence.delLastOverlays(1);
				}

				latArrTmp[0] = latRef;
				lonArrTmp[0] = lonRef;
				latArrTmp[1] = latTmp;
				lonArrTmp[1] = lonTmp;

				mapFence.addPolyLine(latArrTmp, lonArrTmp, "#00FF00", 3);
			}
		}

	});

	mapFence
			.getMap()
			.addEventListener(
					"rightclick",
					function(e) {
						if (isEditorPL) {
							mapFence.delOverlays();
							mapFence.addPolyLine(latArr, lonArr, "#FF0000", 3);
							tipArr[tipArr.length - 1] = "终点";
							mapFence.addMarkersWithLabel(latArr, lonArr,
									tipArr, infArr, optArr);

							// 处理属性数据
							document.getElementById("fenceCds").innerHTML = "";
							var fenceCdsCodes = "<table width='100%' class='tabStyle'>"
									+ "<tr class='trStyle' >"
									+ "<td>&nbsp;序号</td>"
									+ "<td>经度</td>"
									+ "<td>纬度</td>"
									+ "<td>位置</td>"
									+ "<td>备注</td>" + "</tr>";

							for (var i = 0; i < latArr.length; i++) {
								fenceCdsCodes += "<tr align='center' onmouseover='styleCellOnMouseOver(this, cellHoverColor)' onmouseout='styleCellOnMouseOut(this,cellDefaultColor)'>"
										+ "<td>&nbsp;"
										+ (i + 1)
										+ "</td>"
										+ "<td>"
										+ lonArr[i]
										+ "</td>"
										+ "<td>"
										+ latArr[i]
										+ "</td>"
										+ "<td style='cursor:pointer' onclick=\"new BaiduMap().parseAddress("
										+ latArr[i]
										+ ","
										+ lonArr[i]
										+ ",'showAddress',this)\">点击查询位置</td>"
										+ "<td>"
										+ tipArr[i]
										+ "</td>"
										+ "</tr>";
							}
							fenceCdsCodes += "</table>";
							document.getElementById("fenceCds").innerHTML = fenceCdsCodes;
						}
						isEditorPL = false;
						// console.log("鼠标右击: "+isEditor);
					});

}

function addPolygon() {
	clearFenceInfo();

	var pts = [];
	var pt;
	var latArr = [];
	var lonArr = [];
	var tipArr = [];
	var infArr = [];
	var optArr = [];
	var latNode, lonNode;
	var latArrTmp = [];
	var lonArrTmp = [];

	var isEditorPG = true;

	var lat0, lon0;
	mapFence.getMap().addEventListener(
			"click",
			function(e) {
				// alert(e.point.lng+","+e.point.lat);
				if (isEditorPG) {
					pt = new BMap.Point(e.point.lng, e.point.lat);
					pts.push(pt);
					// alert("点数：" + pts.length);
					if (pts.length >= 1) {
						var ptLen = pts.length;
						lat0 = pts[ptLen - 1].lat;
						lon0 = pts[ptLen - 1].lng;
						// alert(lat+", "+lon);

						latArr[ptLen - 1] = lat0;
						lonArr[ptLen - 1] = lon0;
						tipArr[ptLen - 1] = "节点" + ptLen;
						infArr[ptLen - 1] = "";
						optArr[ptLen - 1] = "";

						if (ptLen == 1) {
							mapFence.addMarkersWithLabel(latArr, lonArr,
									tipArr, infArr, optArr);
						} else {
							mapFence.delOverlays();
							mapFence.addPolyLine(latArr, lonArr, "#FF0000", 3);
							mapFence.addMarkersWithLabel(latArr, lonArr,
									tipArr, infArr, optArr);
						}

					}
				}

			});

	mapFence.getMap().addEventListener("mousemove", function(e) {
		if (isEditorPG) {
			pt = new BMap.Point(e.point.lng, e.point.lat);
			var latTmp = pt.lat;
			var lonTmp = pt.lng;
			if (pts.length >= 1) {
				var ptLen = pts.length;
				var latRef = pts[ptLen - 1].lat;
				var lonRef = pts[ptLen - 1].lng;

				var olLen = mapFence.getOverlaysLen();
				if (olLen > (ptLen + 1)) {
					mapFence.delLastOverlays(1);
				}

				latArrTmp[0] = latRef;
				lonArrTmp[0] = lonRef;
				latArrTmp[1] = latTmp;
				lonArrTmp[1] = lonTmp;

				mapFence.addPolyLine(latArrTmp, lonArrTmp, "#00FF00", 3);
			}
		}
	});

	mapFence
			.getMap()
			.addEventListener(
					"rightclick",
					function(e) {
						if (isEditorPG) {
							var ptLen = pts.length;
							latArr[ptLen] = latArr[0];
							lonArr[ptLen] = lonArr[0];
							tipArr[ptLen] = tipArr[0];
							infArr[ptLen] = infArr[0];
							optArr[ptLen] = optArr[0];

							mapFence.delOverlays();
							mapFence.addPolygon(latArr, lonArr, "#FF0000", 3);

							mapFence.addMarkersWithLabel(latArr, lonArr,
									tipArr, infArr, optArr);
							// console.log("鼠标右击: "+isEditor);

							// 处理属性数据
							document.getElementById("fenceCds").innerHTML = "";
							var fenceCdsCodes = "<table width='100%' class='tabStyle'>"
									+ "<tr class='trStyle' >"
									+ "<td>&nbsp;序号</td>"
									+ "<td>经度</td>"
									+ "<td>纬度</td>"
									+ "<td>位置</td>"
									+ "<td>备注</td>" + "</tr>";

							for (var i = 0; i < latArr.length; i++) {
								fenceCdsCodes += "<tr align='center' onmouseover='styleCellOnMouseOver(this, cellHoverColor)' onmouseout='styleCellOnMouseOut(this,cellDefaultColor)'>"
										+ "<td>&nbsp;"
										+ (i + 1)
										+ "</td>"
										+ "<td>"
										+ lonArr[i]
										+ "</td>"
										+ "<td>"
										+ latArr[i]
										+ "</td>"
										+ "<td style='cursor:pointer' onclick=\"new BaiduMap().parseAddress("
										+ latArr[i]
										+ ","
										+ lonArr[i]
										+ ",'showAddress',this)\">点击查询位置</td>"
										+ "<td>"
										+ tipArr[i]
										+ "</td>"
										+ "</tr>";
							}
							fenceCdsCodes += "</table>";
							document.getElementById("fenceCds").innerHTML = fenceCdsCodes;

							var fenceParamCodes = "<table width='100%' class='tableCommon'>"
									+ "<tr>"
									+ "<td class='thColor'>区域类型</td>"
									+ "<td ><select id='areaType'><option value='1'>进区域报警</option><option value='2'>出区域报警</option></select></td>"
									+ "<td class='thColor'>区域名称</td>"
									+ "<td ><input style='width:120px' id='areaName' value=''/></td>"
									+ "<td class='thColor'>区域别名</td>"
									+ "<td ><input style='width:120px' id='areaAlias' value=''/></td>"
									+ "</tr>"
									+ "<tr>"
									+ "<td class='thColor'>开始时间</td>"
									+ "<td ><input style='width:120px' id='startTime' value=''/></td>"
									+ "<td class='thColor'>结束时间</td>"
									+ "<td ><input style='width:120px' id='endTime' value=''/></td>"
									+ "<td class='thColor'>限速(Km/h)</td>"
									+ "<td ><input style='width:120px' id='maxSpeed' value=''/></td>"
									+ "</tr>"
									+ "<tr>"
									+ "<td class='thColor'>超速持续时间(s)</td>"
									+ "<td ><input style='width:120px' id='lastTimeOverSpeed' value=''/></td>"
									+ "<td ></td>"
									+ "<td ></td>"
									+ "<td ></td>"
									+ "<td ></td>"
									+ "</tr>"
									+ "<tr>"
									+ "<td colspan=6 style='text-align:right'><input type='button' id='saveSettingButton' style='width:80px' value='保存' >"
									+ " &nbsp;"
									+ "<input type='button' style='width:80px' value='取消'>"
									+ " &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>"
									+ "</tr>" + "</table>";
							document.getElementById("fenceParam").innerHTML = fenceParamCodes;

							$("#saveSettingButton")
									.click(
											function() {
												var areaType = $("#areaType")
														.val();
												var areaName = $("#areaName")
														.val();
												var areaAlias = $("#areaAlias")
														.val();
												var startTime = $("#startTime")
														.val();
												var endTime = $("#endTime")
														.val();
												var maxSpeed = $("#maxSpeed")
														.val();
												var lastTimeOverSpeed = $(
														"#lastTimeOverSpeed")
														.val();
												var numberOfVertex = latArr.length - 1;
												var params = {
													"areaType" : areaType,
													"areaName" : areaName,
													"areaAlias" : areaAlias,
													"startTime" : startTime,
													"endTime" : endTime,
													"maxSpeed" : maxSpeed,
													"lastTimeOverSpeed" : lastTimeOverSpeed,
													"numberOfVertex" : numberOfVertex,
													"latArr" : latArr,
													"lonArr" : lonArr
												};
												var paramString = $.param(
														params, true);
												$
														.ajax({
															url : "/808FrontProject/eFence/addPolygonAreaSettingAction",
															data : paramString,
															type : "post"
														});

											});

						}

						isEditorPG = false;
					});

}

function openRule() {
	// 测距
	mapFence.rule();
}