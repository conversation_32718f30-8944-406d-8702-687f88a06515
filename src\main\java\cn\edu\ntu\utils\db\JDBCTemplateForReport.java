package cn.edu.ntu.utils.db;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.struts2.components.Else;
import org.springframework.stereotype.Component;

import cn.edu.ntu.dao.mapper.interfaces.ObjectMapper;

/**
 * JDBC Template for Report
 *
 * <AUTHOR>
 * @date 2017-05-12 09:11:40
 */

@Component
public class JDBCTemplateForReport {
	
	public List query(String sql, ObjectMapper om, Object... params) {
		List list = new ArrayList();
		PreparedStatement pstmt = null;
		ResultSet rs = null;
		try {
			Connection conn = JDBCUtilForReport.getConn();
			pstmt = conn.prepareStatement(sql);
			setParams(pstmt, params);
			rs = pstmt.executeQuery();
			if (null != rs) {
				while (rs.next()) {
					Object obj = om.rowMapper(rs);
					list.add(obj);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		} 
		
		return list;
	}

	
	
	public boolean update(String sql, Object... params) {
		PreparedStatement pstmt = null;
		try {
			Connection conn = JDBCUtilForReport.getConn();
			pstmt = conn.prepareStatement(sql);
			setParams(pstmt, params);
			return pstmt.executeUpdate() > 0;
		} catch (SQLException e) {
			e.printStackTrace();
		} 
		
		return false;
	}



	public List queryH(String database, String sql, ObjectMapper om, Object... params) {
		List list = new ArrayList();
		PreparedStatement pstmt = null;
		ResultSet rs = null;
		try {
			JDBCUtilForReport.setDatabase(database);
			Connection conn = JDBCUtilForReport.getConnForHistory();
			pstmt = conn.prepareStatement(sql);
			setParams(pstmt, params);
			rs = pstmt.executeQuery();
			if (null != rs) {
				while (rs.next()) {
					Object obj = om.rowMapper(rs);
					list.add(obj);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		} 
		
		return list;
	}

	public void close(){
		JDBCUtilForReport.close();
	}
	
	
	private void setParams(PreparedStatement pstmt, Object... params) {
		try {
			if (null != params && params.length > 0) {
				SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				for (int i = 0; i < params.length; i++) {
					    if(params[i] instanceof Date)
					    	pstmt.setString(i+1, sdf.format(params[i]));
					    else if(params[i]==null)
					    	pstmt.setString(i + 1, null);
					    else
					        pstmt.setString(i + 1, params[i].toString());
				}
			}
		} catch (SQLException e) {
			e.printStackTrace();
		}
	}
	
}
