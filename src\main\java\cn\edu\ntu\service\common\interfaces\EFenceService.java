package cn.edu.ntu.service.common.interfaces;

import cn.edu.ntu.entity.common.PolygonAreaSettingParam;
import cn.edu.ntu.entity.common.RectangleAreaSettingParam;
import cn.edu.ntu.entity.common.RoundAreaSettingParam;

public interface EFenceService {

	public void addRoundAreaSetting(RoundAreaSettingParam param,String userName);

	public void addRectangleAreaSetting(RectangleAreaSettingParam param, String userName);

	public void addPolygonAreaSetting(PolygonAreaSettingParam param, String userName);

	

}
