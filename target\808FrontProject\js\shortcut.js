//
//Written by <PERSON>, <PERSON><PERSON> in 2016-08-11
//Function name: openShortcut Parameters:
//object and mouse event
//

function openShortcut(obj, event) {
	var e = event || window.event;
	var jqMenu = $("#shortcutMenuL1");
	var jqMenuDiv = $("#shortcutMenuL1 > div");

	if (3 == event.which) {
		closeShortcutL2();
		// alert("点击右键!");
		var topCur = e.clientY;
		var leftCur = e.clientX;

		var objTop = getPageY(obj);
		var objLeft = getPageX(obj);

		var wWin = 295;
		var hWin = getWinHeight() - 2;

		var hMenu = jqMenuDiv.length * 30;
		var wMenu = 110;

		var leftPos = (leftCur + wMenu) > wWin ? (wWin - wMenu) : leftCur;
		var topPos = topCur;

		if (topPos > (hWin - hMenu)) {
			topPos = hWin - hMenu;
		}

		jqMenu.css("display", "block");
		jqMenu.css("top", topPos);
		jqMenu.css("left", leftPos);
		jqMenu.css("width", wMenu);
		jqMenu.css("height", hMenu);
		jqMenu.css("z-index", 2);

		for (var i = 0; i < jqMenuDiv.length; i++) {
			// if(i<jqMenuDiv.length-1){
			// $(jqMenuDiv[i]).css("border-bottom", " 1px #fff solid");
			// }
			jqMenuDiv.get(i).setAttribute("onmouseover",
					"styleDivMOver(this);openShortcutL2(this);");
			jqMenuDiv.get(i).setAttribute("onmouseout", "styleDivMOut(this)");

		}
		var carNumberStringArray=$(obj).html().split(";");
		var p = carNumberStringArray[0];
		$("#menuParameterL1").val(p);
	}

	document.onclick = function() {
		closeShortcut();
		closeShortcutL2();
	};
}

function closeShortcut() {
	var t = 0;
	setTimeout(function() {
		var oMenu = document.getElementById("shortcutMenuL1");
		oMenu.style.display = "none";
	}, t);
}

function closeShortcutL2() {
	closeShortcutL2_QF();
	closeShortcutL2_ZD();
	closeShortcutL2_ZK();
}

function closeShortcutL2_QF() {
	var t = 0;
	setTimeout(function() {
		var oMenuQF = document.getElementById("shortcutMenuL2_QF");
		oMenuQF.style.display = "none";
	}, t);
}
function closeShortcutL2_ZD() {
	var t = 0;
	setTimeout(function() {
		var oMenuZD = document.getElementById("shortcutMenuL2_ZD");
		oMenuZD.style.display = "none";
	}, t);
}
function closeShortcutL2_ZK() {
	var t = 0;
	setTimeout(function() {
		var oMenuZK = document.getElementById("shortcutMenuL2_ZK");
		oMenuZK.style.display = "none";
	}, t);
}

function openShortcutL2(obj) {
	// alert(obj.innerHTML);
	if (obj.innerHTML.indexOf("群发操作") > -1) {
		closeShortcutL2_ZD();
		closeShortcutL2_ZK();
		
		var jqMenu = $("#shortcutMenuL2_QF");
		var jqMenuDiv = $("#shortcutMenuL2_QF > div");
		var objTop = getPageY(obj);
		var objLeft = getPageX(obj);
		var wWin = 295;
		var hWin = getWinHeight() - 2;

		var hMenu = jqMenuDiv.length * 30;
		var wMenu = 100;
		var leftPos = objLeft + 110 + 2;
		var topPos = objTop;
		if (topPos > (hWin - hMenu)) {
			topPos = hWin - hMenu;
		}

		jqMenu.css("display", "block");
		jqMenu.css("top", topPos);
		jqMenu.css("left", leftPos);
		jqMenu.css("width", wMenu);
		jqMenu.css("height", hMenu);
		jqMenu.css("z-index", 3);
		jqMenu.css("border-left", " 1px #fff solid");

		for (var i = 0; i < jqMenuDiv.length; i++) {
			jqMenuDiv.get(i).setAttribute("onmouseover",
					"styleDivMOver(this);writeValueInMenuL2()");
			jqMenuDiv.get(i).setAttribute("onmouseout", "styleDivMOut(this)");
		}

	} else if (obj.innerHTML.indexOf("配置终端") > -1) {
		closeShortcutL2_QF();
		closeShortcutL2_ZK();
		
		var jqMenu = $("#shortcutMenuL2_ZD");
		var jqMenuDiv = $("#shortcutMenuL2_ZD > div");
		var objTop = getPageY(obj);
		var objLeft = getPageX(obj);
		var wWin = 295;
		var hWin = getWinHeight() - 2;

		var hMenu = jqMenuDiv.length * 30;
		var wMenu = 150;
		var leftPos = objLeft + 110 + 2;
		var topPos = objTop;
		if (topPos > (hWin - hMenu)) {
			topPos = hWin - hMenu;
		}

		jqMenu.css("display", "block");
		jqMenu.css("top", topPos);
		jqMenu.css("left", leftPos);
		jqMenu.css("width", wMenu);
		jqMenu.css("height", hMenu);
		jqMenu.css("z-index", 3);
		jqMenu.css("border-left", " 1px #fff solid");

		for (var i = 0; i < jqMenuDiv.length; i++) {
			jqMenuDiv.get(i).setAttribute("onmouseover",
					"styleDivMOver(this);writeValueInMenuL2()");
			jqMenuDiv.get(i).setAttribute("onmouseout", "styleDivMOut(this)");
		}

		// var p = $(obj).attr("id");
		// $("#menuParameterL2").val(p);
	} else if (obj.innerHTML.indexOf("终端控制") > -1) {
		closeShortcutL2_QF();
		closeShortcutL2_ZD();
		
		var jqMenu = $("#shortcutMenuL2_ZK");
		var jqMenuDiv = $("#shortcutMenuL2_ZK > div");
		var objTop = getPageY(obj);
		var objLeft = getPageX(obj);
		var wWin = 295;
		var hWin = getWinHeight() - 2;

		var hMenu = jqMenuDiv.length * 30;
		var wMenu = 100;
		var leftPos = objLeft + 110 + 2;
		var topPos = objTop;
		if (topPos > (hWin - hMenu)) {
			topPos = hWin - hMenu;
		}

		jqMenu.css("display", "block");
		jqMenu.css("top", topPos);
		jqMenu.css("left", leftPos);
		jqMenu.css("width", wMenu);
		jqMenu.css("height", hMenu);
		jqMenu.css("z-index", 3);
		jqMenu.css("border-left", " 1px #fff solid");

		for (var i = 0; i < jqMenuDiv.length; i++) {
			jqMenuDiv.get(i).setAttribute("onmouseover",
					"styleDivMOver(this);writeValueInMenuL2()");
			jqMenuDiv.get(i).setAttribute("onmouseout", "styleDivMOut(this)");
		}

	} else {
		closeShortcutL2();
	}

	document.onclick = function() {
		closeShortcut();
		closeShortcutL2();
	};
}

function writeValueInMenuL2() {
	// $("#menuParameterL2").val("f1;f2;f3");
	// alert($("#menuParameterL2").val());

	var selectedArrayString = "";
	for (var i = 0; i < selectedArray.length; i++) {
		selectedArrayString += selectedArray[i] + ",";
	}
	seletedArrayString = selectedArrayString.substring(0,
			selectedArrayString.length - 1);
	$("#menuParameterL2").val(seletedArrayString);
}

function initShortcut() {
	// alert("Calling the initShortcut()!");
	$("#shortcutMenuL1").hide();
	$("#shortcutMenuL2_QF").hide();
	$("#shortcutMenuL2_ZD").hide();
	$("#shortcutMenuL2_ZK").hide();
}

function styleDivMOver(obj) {
	obj.className = "menuFocus";
}

function styleDivMOut(obj) {
	obj.className = "menuDefault";
}
