<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@taglib prefix="s" uri="/struts-tags"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>司乘人员管理</title>

<link rel="stylesheet" type="text/css"
	href="/808FrontProject/css/user.css">

<!-- date component [s] -->
<script type="text/javascript"
	src="/808FrontProject/js/lib.jsdate/jscal2.js"></script>
<script type="text/javascript"
	src="/808FrontProject/js/lib.jsdate/en.js"></script>
<link rel="stylesheet" type="text/css"
	href="/808FrontProject/js/lib.jsdate/jscal2.css">
<link rel="stylesheet" type="text/css"
	href="/808FrontProject/js/lib.jsdate/border-radius.css">
<link rel="stylesheet" type="text/css"
	href="/808FrontProject/js/lib.jsdate/steel.css">
<!-- date component [e] -->

<script type="text/javascript" src="/808FrontProject/js/lib.jquery.js"></script>
<script type="text/javascript">
	function driverAdd() {
		$("#modifyDriverInfoForm").attr("action","/808FrontProject/driver/addAction.action").submit();
	}

	function driverEdit() {
		var driverId=$('input:radio[name="selectedDriverInfo"]:checked').val();
		if (undefined ==driverId ||driverId == "") {
			alert("请在单选按钮中选择要编辑的司乘人员！");
			return false;
		}
		
		var path="/808FrontProject/driver/editAction.action?driverId="+driverId;
		$("#modifyDriverInfoForm").attr("action",path).submit();
	}
	
	function driverDelete(){
		var driverId=$('input:radio[name="selectedDriverInfo"]:checked').val();
		if (undefined == driverId ||driverId == "") {
			alert("请在单选按钮中选择要删除的司乘人员！");
			return false;
		}
		
		if (window.confirm("确定要删除当前司乘人员吗？")) {
		var path="/808FrontProject/driver/deleteAction.action?driverId="+driverId;
		$("#modifyDriverInfoForm").attr("action",path).submit();
		}else {
			return false;
		}
	}

	
</script>
</head>

<body>
	<span class="title1">司乘人员管理</span>
	<s:form action="queryAction.action" theme="simple" namespace="/driver">
	<table class="tableCommon">
		<tr>
		<!-- 	<td width="150px">分类</td>
			<td><s:select name="driverInfo.driverType" list="#{1:'司机',2:'乘务员'}" 
			        listKey="value" listValue="value" headerKey="0" headerValue="请选择"></s:select>
            </td>
         -->
			<td width="150px">姓名</td>
			<td><s:textfield name="driverInfo.driverName"></s:textfield></td>
			<td width="150px">从业资格证编号</td>
			<td><s:textfield name="driverInfo.qualificationCertificateId"></s:textfield></td>
			<td style="padding-left: 0px; text-align: center;"><input
				style="width: 100px; font-size: 14px" type="submit" name=""
				value="查询" /></td>
		</tr>
	</table>
	</s:form>
	<s:form id="modifyDriverInfoForm">
	<table class="tableCommon">
		<tr class="thSize thColor">
		<td width="5%">序号</td>
			<td>编号</td>
			<td>姓名</td>
			<td>性别</td>
			<td>身份证号</td>
			<td>移动电话</td>
			<td>从业资格证编号</td>
			<td>选择</td>
		</tr>
		 <s:iterator value="driverInfoList" status="st">
		<tr>
		   <tr <s:if test="#st.even">bgcolor="#ECF5FF"</s:if>>
					<td style="padding-left: 0px; text-align: center"><s:property
							value="#st.index+1" /></td>
			<td><s:property value="driverId"/></td>
			<td><s:property value="driverName"/></td>
			<td><s:property value="driverSex"/></td>
			<td><s:property value="identificationCardNumber"/></td>
			<td><s:property value="contactPhoneNumber"/></td>
			<td><s:property value="qualificationCertificateId"/></td>
			<td><input type="radio" class="radioStyle" name="selectedDriverInfo" value="<s:property value='driverId'/>"/></td>
			
		</tr>
		</s:iterator>
		<tr>
			<td colspan="9" style="padding-left: 0px; text-align: center;"><input
				class="buttonStyle" type="button" value="添加"
				onclick="driverAdd()" />&nbsp; <input
				class="buttonStyle" type="button" value="编辑"
				onclick="driverEdit()" />&nbsp; <input
				class="buttonStyle" type="button" value="删除"
				onclick="driverDelete()" />
				</td>
		</tr>
	</table>
	</s:form>


</body>
</html>