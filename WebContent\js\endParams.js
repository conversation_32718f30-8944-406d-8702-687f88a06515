// 服务器参数设置
function serverParam() {
	
	var vId = $("#menuParameterL1").val();
	var codes = "<table>"
			+ "<tr>"
			+ "<td class='thColor'>主服务器APN</td><td style='padding-right:10px'><input type='text'  id='masterServerApn' style='width: 200px'/></td>"
			+ "<td class='thColor'>拨号用户名</td><td style='padding-right:0px'><input type='text' id='masterUserName' style='width: 200px'/></td>"
			+ "</tr><tr>"
			+ "<td class='thColor'>拨号密码</td><td style='padding-right:10px'><input type='text' id='masterPassword' style='width: 200px'/></td>"
			+ "<td class='thColor'>IP地址域名</td><td style='padding-right:0px'><input type='text' id='masterServerAddress' style='width: 200px'/></td>"
			+ "</tr><tr>"
			+ "<td class='thColor'>备份服务器APN</td><td style='padding-right:10px'><input type='text' id='backUpServerApn' style='width: 200px'/></td>"
			+ "<td class='thColor'>拨号用户名</td><td style='padding-right:0px'><input type='text' id='backUpUserName' style='width: 200px'/></td>"
			+ "</tr><tr>"
			+ "<td class='thColor'>拨号密码</td><td style='padding-right:10px'><input type='text'  id='backUpPassword' style='width: 200px'/></td>"
			+ "<td class='thColor'>IP地址域名</td><td style='padding-right:0px'><input type='text' id='backUpServerAddress' style='width: 200px'/></td>"
			+ "</tr><tr>"
			+ "<td class='thColor'>服务器TCP端口</td><td style='padding-right:10px'> <input type='text'  id='serverTcpPort' style='width: 200px'/></td>"
			+ "<td class='thColor'>服务器UDP端口</td><td style='padding-right:0px'><input type='text'  id='serverUdpPort' style='width: 200px'/></td>"
			+ "</tr>" + "</table";
	var d = dialog({
		title : '服务器参数设置_' + vId,
		content : codes,
		button : [ {
			value : '查询',
			callback : function() {
				queryServerParams(vId);
				return false;
			}
		}, {
			value : '设置',
			callback : function() {
				setServerParams(vId);
				return true;
			},
			autofocus : true
		} ]
	});
	d.showModal();
}

//查询服务器参数
function queryServerParams(carNumber) {

	initCommandResult();
	clearInterval(serverParamsCommandInterval);

	var dateString1 = getFormatDate();
	// 命令正在执行
	var commandContent1 = "<tr><td>" + carNumber + "</td><td>查询服务器参数</td><td>"
			+ dateString1 + "</td><td>发送成功</td></tr>";

	$("#cmdTable").append(commandContent1);

	$.ajax({
		url : "/808FrontProject/endParams/queryEndParamsAction",
		data : {
			carNumber : carNumber
		},
		type : "post",
		success : function(data) {
			// alert("sim卡号:"+data.simCardString+";流水号:"+data.commandSerialNumber);
			if (data.simCardString != null && data.commandSerialNumber != 0) {
				serverParamsCommandInterval = window.setInterval(function() {
					if (commandResult == 0) {
						queryCommandResult(data.simCardString,
								data.commandSerialNumber);
					} else {
						// 命令执行成功
						var dateString2 = getFormatDate();
						var commandContent2 = "<tr><td>" + carNumber
								+ "</td><td>查询服务器参数</td><td>" + dateString2
								+ "</td><td>执行成功</td></tr>";
						$("#cmdTable").append(commandContent2);
						
						clearInterval(serverParamsCommandInterval);
						
						initServerParams(data.simCardString);
					}

				}, 1000);
				setTimeout(function(){
					clearInterval(serverParamsCommandInterval);
				}, 15000);

			}

		}
	});
}

function initServerParams(simCardString) {
	$.ajax({
		url : "/808FrontProject/endParams/queryServerParamsAction",
		data : {
			simCardString : simCardString
		},
		dataType : "json",
		type : "post",
		success : function(data) {
			$("#masterServerApn").val(data.masterServerApn);
			$("#masterUserName").val(data.masterUserName);
			$("#masterPassword").val(data.masterPassword);
			$("#masterServerAddress").val(data.masterServerAddress);
			$("#backUpServerApn").val(data.backUpServerApn);
			$("#backUpUserName").val(data.backUpUserName);
			$("#backUpPassword").val(data.backUpPassword);
			$("#backUpServerAddress").val(data.backUpServerAddress);
			$("#serverTcpPort").val(data.serverTcpPort);
			$("#serverUdpPort").val(data.serverUdpPort);
		}
	});
}


//设置服务器参数
function setServerParams(carNumber) {
	initCommandResult();
	clearInterval(serverParamsCommandInterval);

	var dateString1 = getFormatDate();
	// 命令正在执行
	var commandContent1 = "<tr><td>" + carNumber + "</td><td>设置服务器参数</td><td>"
			+ dateString1 + "</td><td>发送成功</td></tr>";

	$("#cmdTable").append(commandContent1);

	var masterServerApn = $("#masterServerApn").val();
	var masterUserName = $("#masterUserName").val();
	var masterPassword = $("#masterPassword").val();
	var masterServerAddress = $("#masterServerAddress").val();
	var backUpServerApn = $("#backUpServerApn").val();
	var backUpUserName = $("#backUpUserName").val();
	var backUpPassword = $("#backUpPassword").val();
	var backUpServerAddress = $("#backUpServerAddress").val();
	var serverTcpPort = $("#serverTcpPort").val();
	var serverUdpPort = $("#serverUdpPort").val();
	$.ajax({
		url : "/808FrontProject/endParams/setServerParamsAction",
		data : {
			carNumber : carNumber,
			masterServerApn : masterServerApn,
			masterUserName : masterUserName,
			masterPassword : masterPassword,
			masterServerAddress : masterServerAddress,
			backUpServerApn : backUpServerApn,
			backUpUserName : backUpUserName,
			backUpPassword : backUpPassword,
			backUpServerAddress : backUpServerAddress,
			serverTcpPort : serverTcpPort,
			serverUdpPort : serverUdpPort
		},
		dataType : "json",
		type : "post",
		success : function(data) {
			// alert("sim卡号:"+data.simCardString+"|流水号:"+data.commandSerialNumber);
			if (data.simCardString != null && data.commandSerialNumber != 0) {
				commandInterval = window.setInterval(function() {
					if (commandResult == 0) {
						queryCommandResult(data.simCardString,
								data.commandSerialNumber);
					} else {
						// 命令执行成功
						var dateString2 = getFormatDate();
						var commandContent2 = "<tr><td>" + carNumber
								+ "</td><td>设置服务器参数</td><td>" + dateString2
								+ "</td><td>执行成功</td></tr>";
						$("#cmdTable").append(commandContent2);
						
						clearInterval(serverParamsCommandInterval);
					}

				}, 1000);
				setTimeout(function(){
					clearInterval(serverParamsCommandInterval);
				}, 15000);

			}

		}
	});
}

function icCardAuthenParam() {
	// 道路运输证IC卡认证
	var vId = $("#menuParameterL1").val();
	var codes = "<table>"
			+ "<tr>"
			+ "<td class='thColor' style='padding-right:10px'>认证主服务器 IP地址或域名</td><td><input type='text'  id='masterServerAddress' style='width: 200px'/></td>"
			+ "</tr><tr>"
			+ "<td class='thColor' style='padding-right:10px'>认证主服务器 TCP端口</td><td><input type='text'  id='masterServerTcpPort' style='width: 200px'/></td>"
			+ "</tr><tr>"
			+ "<td class='thColor' style='padding-right:10px'>认证主服务器UDP端口</td><td> <input type='text'  id='masterServerUdpPort' style='width: 200px'/></td>"
			+ "</tr><tr>"
			+ "<td class='thColor' style='padding-right:10px'>认证备份服务器IP地址或域名</td><td><input type='text'  id='backUpServerAddress' style='width: 200px'/></td>"
			+ "</tr>" + "</table>";
	var d = dialog({
		title : '道路运输证IC卡认证_' + vId,
		content : codes,
		button : [ {
			value : '查询',
			callback : function() {
				queryIcCardAuthenticationParams(vId);
				return false;
			}
		}, {
			value : '设置',
			callback : function() {
				setIcCardAuthenticationParams(vId);
				return true;
			},
			autofocus : true
		} ]
	});
	d.showModal();
}

function queryIcCardAuthenticationParams(carNumber){
	initCommandResult();
	clearInterval(icCardAuthenticationParamsCommandInterval);
	
	// 命令正在执行
	var dateString1 = getFormatDate();
	var commandContent1 = "<tr><td>" + carNumber + "</td><td>查询ic卡认证服务器参数</td><td>"
			+ dateString1 + "</td><td>发送成功</td></tr>";
	$("#cmdTable").append(commandContent1);
	
	$.ajax({
		url : "/808FrontProject/endParams/queryEndParamsAction",
		data : {
			carNumber : carNumber
		},
		type : "post",
		success : function(data) {
			// alert("sim卡号:"+data.simCardString+";流水号:"+data.commandSerialNumber);
			if (data.simCardString != null && data.commandSerialNumber != 0) {
				icCardAuthenticationParamsCommandInterval = window.setInterval(function() {
					if (commandResult == 0) {
						queryCommandResult(data.simCardString,
								data.commandSerialNumber);
					} else {
						
						var dateString2 = getFormatDate();
						var commandContent2 = "<tr><td>" + carNumber
								+ "</td><td>查询ic卡认证服务器参数</td><td>" + dateString2
								+ "</td><td>执行成功</td></tr>";
						$("#cmdTable").append(commandContent2);
						
						clearInterval(icCardAuthenticationParamsCommandInterval);
						
						initIcCardAuthenticationParams(data.simCardString);
					}

				}, 1000);
				setTimeout(function(){
					clearInterval(icCardAuthenticationParamsCommandInterval);
				}, 15000);

			}

		}
	});
	
}

function initIcCardAuthenticationParams(simCardString){
	$.ajax({
		url : "/808FrontProject/endParams/queryIcCardAuthenticationParamsAction",
		data : {
			simCardString : simCardString
		},
		dataType : "json",
		type : "post",
		success : function(data) {
			$("#masterServerAddress").val(data.masterServerAddress);
			$("#masterServerTcpPort").val(data.masterServerTcpPort);
			$("#masterServerUdpPort").val(data.masterServerUdpPort);
			$("#backUpServerAddress").val(data.backUpServerAddress);
		}});
}

function setIcCardAuthenticationParams(carNumber){
	initCommandResult();
	clearInterval(icCardAuthenticationParamsCommandInterval);
	
	var masterServerAddress=$("#masterServerAddress").val();
	var masterServerTcpPort=$("#masterServerTcpPort").val();
	var masterServerUdpPort=$("#masterServerUdpPort").val();
	var backUpServerAddress=$("#backUpServerAddress").val();
	
	// 命令正在执行
	var dateString1 = getFormatDate();
	var commandContent1 = "<tr><td>" + carNumber + "</td><td>设置IC卡认证服务器参数</td><td>"
			+ dateString1 + "</td><td>发送成功</td></tr>";
	$("#cmdTable").append(commandContent1);
	
	$.ajax({
		url : "/808FrontProject/endParams/setIcCardAuthenticationParamsAction",
		data : {
			carNumber : carNumber,
			masterServerAddress : masterServerAddress,
			backUpServerAddress : backUpServerAddress,
			serverTcpPort : masterServerTcpPort,
			serverUdpPort : masterServerUdpPort
		},
		dataType : "json",
		type : "post",
		success : function(data) {
			icCardAuthenticationParamsCommandInterval = window.setInterval(function() {
				if (commandResult == 0) {
					queryCommandResult(data.simCardString,
							data.commandSerialNumber);
				} else {
				
					// 命令执行成功
					var dateString2 = getFormatDate();
					var commandContent2 = "<tr><td>" + carNumber
							+ "</td><td>设置服务器参数</td><td>" + dateString2
							+ "</td><td>执行成功</td></tr>";
					$("#cmdTable").append(commandContent2);
					
					clearInterval(icCardAuthenticationParamsCommandInterval);
				}

			}, 1000);
			setTimeout(function(){
				clearInterval(icCardAuthenticationParamsCommandInterval);
			}, 15000);

		}

		});
}

function posParam() {
	// 位置汇报参数设置
	var vId = $("#menuParameterL1").val();
	var codes = "<table>"
			+ "<tr>"
			+ "<td class='thColor'>位置汇报策略</td><td style='padding-right:10px'><select id='positionReportStrategy' style='width:150px'><option value='0'>定时汇报</option><option value='1'>定距汇报</option><option value='2'>定时和定距汇报</option></select></td>"
			+ "<td class='thColor'>位置汇报方案</td><td style='padding-right:0px'><select id='positionReportSchema' style='width:150px'><option value='0'>根据ACC状态</option><option value='1'>根据登录状态和ACC状态</option></select></td>"
			+ "</tr><tr>"
			+ "<td class='thColor'>驾驶员未登录汇报时间间隔</td><td style='padding-right:10px'> <input type='text' id='notLoginReportInterval' style='width: 200px'/> 秒</td>"
			+ "<td class='thColor'>休眠时汇报时间间隔</td><td style='padding-right:0px'><input type='text' id='sleepReportInterval' style='width: 200px'/> 秒</td>"
			+ "</tr><tr>"
			+ "<td class='thColor'>紧急报警时汇报时间间隔</td><td style='padding-right:10px'> <input type='text' id='alarmReportInterval' style='width: 200px'/> 秒</td>"
			+ "<td class='thColor'>缺省时间汇报间隔</td><td style='padding-right:0px'><input type='text' id='defaultReportInterval' style='width: 200px'/> 秒</td>"
			+ "</tr><tr>"
			+ "<td class='thColor'>缺省距离汇报间隔</td><td style='padding-right:10px'> <input type='text' id='defaultReportSpacing' style='width: 200px'/> 米</td>"
			+ "<td class='thColor'>驾驶员未登录汇报距离间隔</td style='padding-right:0px'><td><input type='text' id='notLoginReportSpacing' style='width: 200px'/> 米</td>"
			+ "</tr><tr>"
			+ "</tr><tr>"
			+ "<td class='thColor'>休眠时汇报距离间隔</td><td style='padding-right:10px'> <input type='text'  id='sleepReportSpacing' style='width: 200px'/> 米</td>"
			+ "<td class='thColor'>紧急报警时汇报距离间隔</td><td style='padding-right:0px'><input type='text' id='alarmReportSpacing' style='width: 200px'/> 米</td>"
			+ "</tr><tr>"
			+ "<td class='thColor'>拐点补传角度</td><td colspan='3'> <input type='text' id='supplementAngle' style='width: 200px'/> 度</td>"
			+ "</tr>" + "</table";
	var d = dialog({
		title : '位置汇报参数设置_' + vId,
		content : codes,
		button : [ {
			value : '查询',
			callback : function() {
				queryPosParams(vId);
				return false;
			}
		}, {
			value : '设置',
			callback : function() {
				setPosParams(vId);
				return true;
			},
			autofocus : true
		} ]
	});
	d.showModal();
}


function queryPosParams(carNumber){
	initCommandResult();
	clearInterval(posParamsCommandInterval);
	// 命令正在执行
	var dateString1 = getFormatDate();
	var commandContent1 = "<tr><td>" + carNumber + "</td><td>查询位置汇报参数</td><td>"
			+ dateString1 + "</td><td>发送成功</td></tr>";
	$("#cmdTable").append(commandContent1);
	
	$.ajax({
		url : "/808FrontProject/endParams/queryEndParamsAction",
		data : {
			carNumber : carNumber
		},
		type : "post",
		success : function(data) {
			// alert("sim卡号:"+data.simCardString+";流水号:"+data.commandSerialNumber);
			if (data.simCardString != null && data.commandSerialNumber != 0) {
				posParamsCommandInterval = window.setInterval(function() {
					if (commandResult == 0) {
						queryCommandResult(data.simCardString,
								data.commandSerialNumber);
					} else {
						var dateString2 = getFormatDate();
						var commandContent2 = "<tr><td>" + carNumber
								+ "</td><td>查询位置汇报参数</td><td>" + dateString2
								+ "</td><td>执行成功</td></tr>";
						$("#cmdTable").append(commandContent2);
						
						clearInterval(posParamsCommandInterval);
						
						initPosParams(data.simCardString);
					}

				}, 1000);
				setTimeout(function(){
					clearInterval(posParamsCommandInterval);
				}, 15000);

			}

		}
	});
}

function initPosParams(simCardString){
	$.ajax({
		url : "/808FrontProject/endParams/queryPosParamsAction",
		data : {
			simCardString : simCardString
		},
		dataType : "json",
		type : "post",
		success : function(data) {
			$("#positionReportStrategy").val(data.positionReportStrategy);
			$("#positionReportSchema").val(data.positionReportSchema);
			$("#notLoginReportInterval").val(data.notLoginReportInterval);
			$("#sleepReportInterval").val(data.sleepReportInterval);
			$("#alarmReportInterval").val(data.alarmReportInterval);
			$("#defaultReportInterval").val(data.defaultReportInterval);
			$("#defaultReportSpacing").val(data.defaultReportSpacing);
			$("#notLoginReportSpacing").val(data.notLoginReportSpacing);
			$("#sleepReportSpacing").val(data.sleepReportSpacing);
			$("#alarmReportSpacing").val(data.alarmReportSpacing);
			$("#supplementAngle").val(data.supplementAngle);
			
		}});
}


function setPosParams(carNumber){
	
	initCommandResult();
	clearInterval(posParamsCommandInterval);
	
	var positionReportStrategy=$("#positionReportStrategy").val();
	var positionReportSchema=$("#positionReportSchema").val();
	var notLoginReportInterval=$("#notLoginReportInterval").val();
	var sleepReportInterval=$("#sleepReportInterval").val();
	var alarmReportInterval=$("#alarmReportInterval").val();
	var defaultReportInterval=$("#defaultReportInterval").val();
	var defaultReportSpacing=$("#defaultReportSpacing").val();
	var notLoginReportSpacing=$("#notLoginReportSpacing").val();
	var sleepReportSpacing=$("#sleepReportSpacing").val();
	var alarmReportSpacing=$("#alarmReportSpacing").val();
	var supplementAngle=$("#supplementAngle").val();
	
	// 命令正在执行
	var dateString1 = getFormatDate();
	var commandContent1 = "<tr><td>" + carNumber + "</td><td>设置位置汇报参数</td><td>"
			+ dateString1 + "</td><td>发送成功</td></tr>";
	$("#cmdTable").append(commandContent1);
	
	$.ajax({
		url:"/808FrontProject/endParams/setPosParamsAction",
		data:{
			carNumber:carNumber,
			positionReportStrategy:positionReportStrategy,
			positionReportSchema:positionReportSchema,
			notLoginReportInterval:notLoginReportInterval,
			sleepReportInterval:sleepReportInterval,
			alarmReportInterval:alarmReportInterval,
			defaultReportInterval:defaultReportInterval,
			defaultReportSpacing:defaultReportSpacing,
			notLoginReportSpacing: notLoginReportSpacing,
			sleepReportSpacing:sleepReportSpacing,
			alarmReportSpacing:alarmReportSpacing,
			supplementAngle:supplementAngle
		},
		type:"post",
		dataType:"json",
		success:function(data){
			posParamsCommandInterval = window.setInterval(function() {
				if (commandResult == 0) {
					queryCommandResult(data.simCardString,
							data.commandSerialNumber);
				} else {
					// 命令执行成功
					var dateString2 = getFormatDate();
					var commandContent2 = "<tr><td>" + carNumber
							+ "</td><td>设置位置汇报参数</td><td>" + dateString2
							+ "</td><td>执行成功</td></tr>";
					$("#cmdTable").append(commandContent2);
					
					clearInterval(posParamsCommandInterval);
				}

			}, 1000);
			setTimeout(function(){
				clearInterval(posParamsCommandInterval);
			}, 15000);

		}
		
	});
	
}

function terminalParam() {
	// 终端电话参数设置
	var vId = $("#menuParameterL1").val();
	var codes = "<table>"
			+ "<tr>"
			+ "<td class='thColor' style='padding-right:10px'>终端电话接听策略</td><td><select id='answerStrategy' style='width:150px'><option value='0'>自动接听</option><option value='1'>ACC ON时自动接听，OFF时收到接听</option></select></td>"
			+ "</tr><tr>"
			+ "<td class='thColor' style='padding-right:10px'>每次最长通话时间</td><td><input type='text'  id='totalHoldingTimeEveryTime' style='width: 200px'/>秒</td>"
			+ "</tr><tr>"
			+ "<td class='thColor' style='padding-right:10px'>当月最长通话时间</td><td><input type='text'  id='totalHoldingTimeForTheMonth' style='width: 200px'/>秒</td>"
			+ "</tr>" + "</table";
	var d = dialog({
		title : '终端电话参数设置_' + vId,
		content : codes,
		button : [ {
			value : '查询',
			callback : function() {
				queryTerminalParams(vId);
				return false;
			}
		}, {
			value : '设置',
			callback : function() {
				setTerminalParams(vId);
				return true;
			},
			autofocus : true
		} ]
	});
	d.showModal();
}

function queryTerminalParams(carNumber){
	initCommandResult();
	clearInterval(terminalParamsCommandInterval);
	
	$.ajax({
		url : "/808FrontProject/endParams/queryEndParamsAction",
		data : {
			carNumber : carNumber
		},
		type : "post",
		success : function(data) {
			if (data.simCardString != null && data.commandSerialNumber != 0) {
				terminalParamsCommandInterval = window.setInterval(function() {
					if (commandResult == 0) {
						queryCommandResult(data.simCardString,
								data.commandSerialNumber);
					} else {
						
						clearInterval(terminalParamsCommandInterval);
						initTerminalParams(data.simCardString);
					}

				}, 1000);
				setTimeout(function(){
					clearInterval(terminalParamsCommandInterval);
				}, 15000);

			}

		}
	});
}

function initTerminalParams(simCardString){
	$.ajax({
		url : "/808FrontProject/endParams/queryTerminalParamsAction",
		data : {
			simCardString : simCardString
		},
		dataType : "json",
		type : "post",
		success : function(data) {
			$("#answerStrategy").val(data.answerStrategy);
			$("#totalHoldingTimeEveryTime").val(data.totalHoldingTimeEveryTime);
			$("#totalHoldingTimeForTheMonth").val(data.totalHoldingTimeForTheMonth);
			
		}});
}

function setTerminalParams(carNumber){
	initCommandResult();
	clearInterval(terminalParamsCommandInterval);
	
	var answerStrategy=$("#answerStrategy").val();
	var totalHoldingTimeEveryTime=$("#totalHoldingTimeEveryTime").val();
	var totalHoldingTimeForTheMonth=$("#totalHoldingTimeForTheMonth").val();

	$.ajax({
		url:"/808FrontProject/endParams/setTerminalParamsAction",
		data:{
			carNumber:carNumber,
			answerStrategy:answerStrategy,
			totalHoldingTimeEveryTime:totalHoldingTimeEveryTime,
			totalHoldingTimeForTheMonth:totalHoldingTimeForTheMonth
		},
		type:"post",
		dataType:"json",
		success:function(data){
			terminalParamsCommandInterval=setInterval(function(){
				if(commandResult==0)
					queryCommandResult(data.simCardString,data.commandSerialNumber);
				else
					clearInterval(terminalParamsCommandInterval);
			},1000);
			setTimeout(function(){
				clearInterval(terminalParamsCommandInterval);
			},15000);
		}
	});
	
}

function overspeedParam() {
	// 超速参数设置
	var vId = $("#menuParameterL1").val();
	var codes = "<table>"
			+ "<tr>"
			+ "<td class='thColor' style='padding-right:10px'>最高速度</td><td><input style='width:200px'  id='overSpeed'/> km/h</td>"
			+ "</tr><tr>"
			+ "<td class='thColor' style='padding-right:10px'>超速持续时间</td><td><input style='width:200px' id='lastTime'/> 秒</td>"
			+ "</tr><tr>"
			+ "<td class='thColor' style='padding-right:10px'>超速报警预警差值</td><td><input style='width:200px'  id='alarmDifferenceValue'/> 1/10km/h</td>"
			+ "</tr>" + "</table";
	var d = dialog({
		title : '超速参数设置_' + vId,
		content : codes,
		button : [ {
			value : '查询',
			callback : function() {
				queryOverSpeedParams(vId);
				return false;
			}
		}, {
			value : '设置',
			callback : function() {
				setOverSpeedParams(vId);
				return true;
			},
			autofocus : true
		} ]
	});
	d.showModal();
}

function queryOverSpeedParams(carNumber){
	initCommandResult();
	clearInterval(overSpeedParamsCommandInterval);
	
	$.ajax({
		url:"/808FrontProject/endParams/queryEndParamsAction",
		data:{
			carNumber:carNumber
		},
		dataType:"json",
		type:"post",
		success:function(data){
			overSpeedParamsCommandInterval=setInterval(function(){
				if(commandResult==0)
					queryCommandResult(data.simCardString,data.commandSerialNumber);
				else{
					clearInterval(overSpeedParamsCommandInterval);
					initOverSpeedParams(data.simCardString);
				}
				
			},1000);
			setTimeout(function(){
				clearInterval(overSpeedParamsCommandInterval);
			},15000);
		}
	});
}

function initOverSpeedParams(simCardString){
	$.ajax({
		url:"/808FrontProject/endParams/queryOverSpeedParamsAction",
		data:{
			simCardString:simCardString
		},
		dataType:"json",
		type:"post",
		success:function(data){
			$("#overSpeed").val(data.overSpeed);
			$("#lastTime").val(data.lastTime);
			$("#alarmDifferenceValue").val(data.alarmDifferenceValue);
		}
	});
	
}

function setOverSpeedParams(carNumber){
	initCommandResult();
	clearInterval(overSpeedParamsCommandInterval);
	
	var overSpeed=$("#overSpeed").val();
	var lastTime=$("#lastTime").val();
	var alarmDifferenceValue=$("#alarmDifferenceValue").val();
	
	$.ajax({
		url:"/808FrontProject/endParams/setOverSpeedParamsAction",
		data:{
			carNumber:carNumber,
			overSpeed:overSpeed,
			lastTime:lastTime,
			alarmDifferenceValue:alarmDifferenceValue
		},
		dataType:"json",
		type:"post",
		success:function(data){
			overSpeedParamsCommandInterval=setInterval(function(){
				if(commandResult==0)
					queryCommandResult(data.simCardString,data.commandSerialNumber);
				else
					clearInterval(overSpeedParamsCommandInterval);
			},1000);
			setTimeout(function(){
				clearInterval(overSpeedParamsCommandInterval);
			},15000);
			
		}
	
	});
}

function fatigueDrivingParam() {
	// 疲劳驾驶参数设置
	var vId = $("#menuParameterL1").val();
	var codes = "<table>"
			+ "<tr>"
			+ "<td class='thColor' style='padding-right:10px'>连续驾驶时间阈值</td><td><input style='width:200px' id='continuousDriveTime'/> 秒</td>"
			+ "</tr><tr>"
			+ "<td class='thColor' style='padding-right:10px'>当天累计驾驶时间阈值</td><td><input style='width:200px' id='totalDriveTimeForTheDay'/> 秒</td>"
			+ "</tr><tr>"
			+ "<td class='thColor' style='padding-right:10px'>最小休息时间</td><td><input style='width:200px'  id='minBreakTime'/> 秒</td>"
			+ "</tr><tr>"
			+ "<td class='thColor' style='padding-right:10px'>最长停车时间</td><td><input style='width:200px' id='maxParkTime'/> 秒</td>"
			+ "</tr><tr>"
			+ "<td class='thColor' style='padding-right:10px'>疲劳驾驶预警差值</td><td><input style='width:200px'  id='driveAlarmDifferenceValue'/> 秒</td>"
			+ "</tr>" + "</table";
	var d = dialog({
		title : '疲劳驾驶参数设置_' + vId,
		content : codes,
		button : [ {
			value : '查询',
			callback : function() {
				queryFatigueDrivingParams(vId);
				return false;
			}
		}, {
			value : '设置',
			callback : function() {
				setFatigueDrivingParams(vId);
				return true;
			},
			autofocus : true
		} ]
	});
	d.showModal();
}


function queryFatigueDrivingParams(carNumber){
	
	initCommandResult();
	clearInterval(fatigueDrivingParamsCommandInterval);
	
    $.ajax({
    	url:"/808FrontProject/endParams/queryEndParamsAction",
    	data:{
    		carNumber:carNumber
    	},
    	dataType:"json",
    	type:"post",
    	success:function(data){
    		fatigueDrivingParamsCommandInterval=setInterval(function(){
    			if(commandResult==0)
    				queryCommandResult(data.simCardString,data.commandSerialNumber);
    			else {
    				initFatigueDrivingParams(data.simCardString);
    				clearInterval(fatigueDrivingParamsCommandInterval);
    			}
    		},1000);
    		setTimeout(function(){
    			clearInterval(fatigueDrivingParamsCommandInterval);
    		},15000);
    	}
    
    });
}

function initFatigueDrivingParams(simCardString){
	$.ajax({
		url:"/808FrontProject/endParams/queryFatigueDrivingParamsAction",
		data:{
			simCardString:simCardString
		},
		type:"post",
		dataType:"json",
		success:function(data){
			$("#continuousDriveTime").val(data.continuousDriveTime);
			$("#totalDriveTimeForTheDay").val(data.totalDriveTimeForTheDay);
			$("#minBreakTime").val(data.minBreakTime);
			$("#maxParkTime").val(data.maxParkTime);
			$("#driveAlarmDifferenceValue").val(data.driveAlarmDifferenceValue);
		}
	
	});
}

function setFatigueDrivingParams(carNumber){
	initCommandResult();
	clearInterval(fatigueDrivingParamsCommandInterval);
	
	var continuousDriveTime=$("#continuousDriveTime").val();
	var totalDriveTimeForTheDay=$("#totalDriveTimeForTheDay").val();
	var minBreakTime=$("#minBreakTime").val();
	var maxParkTime=$("#maxParkTime").val();
	var driveAlarmDifferenceValue=$("#driveAlarmDifferenceValue").val();
	
	$.ajax({
		url:"/808FrontProject/endParams/setFatigueDrivingParamsAction",
		data:{
			carNumber:carNumber,
			continuousDriveTime:continuousDriveTime,
			totalDriveTimeForTheDay:totalDriveTimeForTheDay,
			minBreakTime:minBreakTime,
			maxParkTime:maxParkTime,
			driveAlarmDifferenceValue:driveAlarmDifferenceValue
		},
		type:"post",
		dataType:"json",
		success:function(data){
			fatigueDrivingParamsCommandInterval=setInterval(function(){
				if(commandResult==0)
					queryCommandResult(data.simCardString,data.commandSerialNumber);
				else 
					clearInterval(fatigueDrivingParamsCommandInterval);
			},1000);
			setTimeout(function(){
				clearInterval(fatigueDrivingParamsCommandInterval);
			},15000);
		}
	})
	
}

function licenseParam() {
	// 车牌号码参数设置
	var vId = $("#menuParameterL1").val();
	var codes = "<table>"
			+ "<tr>"
			+ "<td class='thColor' style='padding-right:10px'>车辆所在省域ID</td><td><input style='width:200px' id='provinceAreaId'/></td>"
			+ "</tr><tr>"
			+ "<td class='thColor' style='padding-right:10px'>车辆所在市域ID</td><td><input style='width:200px' id='cityAreaId'/></td>"
			+ "</tr><tr>"
			+ "<td class='thColor' style='padding-right:10px'>机动车牌照</td><td><input style='width:200px'  id='licenseNumber'/></td>"
			+ "</tr><tr>"
			+ "<td class='thColor' style='padding-right:10px'>车牌颜色</td><td><select id='licensePlateColor' style='width:150px'><option value='1'>蓝色</option><option value='2'>黄色</option><option value='3'>黑色</option><option value='4'>白色</option><option value='9'>其他</option></select></td>"
			+ "</tr>" + "</table";
	var d = dialog({
		title : '车牌号码参数设置_' + vId,
		content : codes,
		button : [ {
			value : '查询',
			callback : function() {
				queryLicenseParams(vId);
				return false;
			}
		}, {
			value : '设置',
			callback : function() {
				setLicenseParams(vId);
				return true;
			},
			autofocus : true
		} ]
	});
	d.showModal();
}

function queryLicenseParams(carNumber){
	initCommandResult();
	clearInterval(licenseParamsCommandInterval);
	
	$.ajax({
		url:"/808FrontProject/endParams/queryEndParamsAction",
		data:{
			carNumber:carNumber
		},
		dataType:"json",
		type:"post",
		success:function(data){
			licenseParamsCommandInterval=setInterval(function(){
				if(commandResult==0)
					queryCommandResult(data.simCardString,data.commandSerialNumber);
				else{
					clearInterval(licenseParamsCommandInterval);
					initLicenseParams(data.simCardString);
				}
					
			},1000);
			
			setTimeout(function(){
				clearInterval(licenseParamsCommandInterval);
			},15000);
			
		}
	});
}

function initLicenseParams(simCardString){
	
	$.ajax({
		url:"/808FrontProject/endParams/queryLicenseParamsAction",
		data:{
			simCardString:simCardString
		},
		type:"post",
		dataType:"json",
		success:function(data){
			$("#provinceAreaId").val(data.provinceAreaId);
			$("#cityAreaId").val(data.cityAreaId);
			$("#licenseNumber").val(data.licenseNumber);
			$("#licensePlateColor").val(data.licensePlateColor);
		}
	});
}

function setLicenseParams(carNumber){
	var provinceAreaId=$("#provinceAreaId").val();
	var cityAreaId=$("#cityAreaId").val();
	var licenseNumber=$("#licenseNumber").val();
	var licensePlateColor=$("#licensePlateColor").val();
	
	initCommandResult();
	clearInterval(licenseParamsCommandInterval);
	
	$.ajax({
		url:"/808FrontProject/endParams/setLicenseParamsAction",
		data:{
			carNumber:carNumber,
			provinceAreaId:provinceAreaId,
			cityAreaId:cityAreaId,
			licenseNumber:licenseNumber,
			licensePlateColor:licensePlateColor
		},
		type:"post",
		dataType:"json",
		success:function(data){
			licenseParamsCommandInterval=setInterval(function(){
				if(commandResult==0)
					queryCommandResult(data.simCardString,data.commandSerialNumber);
				else
					clearInterval(licenseParamsCommandInterval);
			},1000);
			
			setTimeout(function(){
				clearInterval(licenseParamsCommandInterval);
			},15000);
		}
	
	})
	
}