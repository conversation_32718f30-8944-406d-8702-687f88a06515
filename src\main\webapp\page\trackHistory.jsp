<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@taglib prefix="s" uri="/struts-tags"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">

<title>历史轨迹</title>
<meta http-equiv="pragma" content="no-cache">
<meta http-equiv="cache-control" content="no-cache">
<meta http-equiv="expires" content="0">
<meta http-equiv="keywords" content="太平洋,北斗,位置服务,云服务,车辆定位,车辆查询,车辆导航">
<meta http-equiv="description" content="太平洋北斗位置云">


<link rel="stylesheet" type="text/css"
	href="/808FrontProject/css/searchres.css">

<script type="text/javascript" src="/808FrontProject/js/lib.jquery.js"></script>
<script type="text/javascript" src="/808FrontProject/js/lib.json2.js"></script>

<!-- date component [S] -->
<script type="text/javascript"
	src="/808FrontProject/js/lib.jsdate/jscal2.js"></script>
<script type="text/javascript"
	src="/808FrontProject/js/lib.jsdate/en.js"></script>
<link rel="stylesheet" type="text/css"
	href="/808FrontProject/js/lib.jsdate/jscal2.css">
<link rel="stylesheet" type="text/css"
	href="/808FrontProject/js/lib.jsdate/border-radius.css">
<link rel="stylesheet" type="text/css"
	href="/808FrontProject/js/lib.jsdate/steel.css">
<!-- date component [E] -->

<script type="text/javascript" src="/808FrontProject/js/frame.js"></script>
<script type="text/javascript" src="/808FrontProject/js/tools.js"></script>
<script type="text/javascript" src="/808FrontProject/js/baidumap.js"></script>
<script type="text/javascript" src="/808FrontProject/js/style.js"></script>
<script type="text/javascript" src="/808FrontProject/js/track.js"></script>
<script type="text/javascript" src="/808FrontProject/js/var.js"></script>

<!-- interactive dialog [s] -->
<link rel="stylesheet"
	href="/808FrontProject/js/lib.artDialog/ui-dialog.css" />
<script src="/808FrontProject/js/lib.artDialog/dialog-plus-min.js"></script>
<!-- interactive dialog [e] -->

<!-- modified -->
<script type="text/javascript"
	src="https://api.map.baidu.com/api?v=2.0&ak=6ukSP6o4ff8u9SSnlVEKmiZC"></script>
<script type="text/javascript"
	src="https://api.map.baidu.com/library/DrawingManager/1.4/src/DrawingManager.js"></script>
<script type="text/javascript"
	src="https://api.map.baidu.com/library/MarkerManager/1.2/src/MarkerManager.js"></script>

<style type="text/css">
body {
	font-family: 宋体;
}

#trackDiv {
	position: absolute;
	top: 0px;
	right: 5px;
	height: 0px;
	width: 0px;
	background-color: #fff;
	z-index: 1;
	font-size: 14px;
	line-height: 30px;
	padding-left: 5px;
}

input {
	font-size: 14px;
}

.commonFontSize {
	font-size: 14px;
}

</style>
<script type="text/javascript">
	var p;

	function showTrackDiv() {
		$("#trackDiv").show();// 轨迹
		$("#trackDiv").css("height", "60px");
		$("#trackDiv").css("width", "585px");
	}

	var mapTrack;

	$(window).load(function() {
		p = parseParametersFromURL("p");
		if (p != null) {
			p = decodeUTF8(p);
		}
		$("#carNumber").html(p);
		
		mapTrack = new BaiduMap("mapDiv");
		mapTrack.init();

		showTrackDiv();
		InitInfoTable();
		showMapPanel();

		freezeAnmiation();

		//date componenent
		var cal1 = Calendar.setup({
			onSelect : function(cal1) {
				cal1.hide()
			},
			showTime : true
		});
		var cal2 = Calendar.setup({
			onSelect : function(cal2) {
				cal2.hide()
			},
			showTime : true
		});
		cal1.manageFields("date1Btn", "searchDate1", "%Y-%m-%d %H:%M");
		cal2.manageFields("date2Btn", "searchDate2", "%Y-%m-%d %H:%M");

		searchTrackInit();

	});

	$(window).resize(function() {
		showTrackDiv();
		showMapPanel();
	});
	
	
	
	
</script>
</head>

<body>
	<div id="trackDiv">
		<!-- for vehicles tracking -->
		<label id="carNumber" style="font-weight:bolder"></label>
		<input type='text' name='searchDate1' id='searchDate1'
			onFocus='initFormText(this,"","请输入起始时间...")'
			onBlur='initFormText(this,"请输入起始时间...","请输入起始时间...")'
			style='width: 100px; margin-top: 2px' value='请输入起始时间...'
			title="时间格式：yyyy-mm-dd hh:mm" /> <input id="date1Btn" type="button"
			style="height: 25px; width: 30px;" value="..." /> <input type='text'
			name='searchDate2' id='searchDate2'
			onFocus='initFormText(this,"","请输入结束时间...")'
			onBlur='initFormText(this,"请输入结束时间...","请输入结束时间...")'
			style='width: 100px; margin-top: 1px' value='请输入结束时间...'
			title="时间格式：yyyy-mm-dd hh:mm" /> <input id="date2Btn" type="button"
			style="height: 25px; width: 30px;" value="..." /> 过滤静止点 <input
			type="checkbox" id="isFilter" name="isFilter"
			style="height: 15px; width: 15px;" checked />
			查询司机信息 <input
			type="checkbox" id="isFind" name="isFind"
			style="height: 15px; width: 15px;" checked /><br />
			 <input id="trackBtn"
			type="button" style="height: 25px; width: 80px;"
			onclick="searchTrack()" value="轨迹查询" /> <input id="animationBtn1"
			style="height: 25px; width: 100px;" type="button"
			onclick="showAnimation();" value="播放" /> <input id="animationBtn2"
			style="height: 25px; width: 60px;" type="button"
			onclick="stopAnimation()" value="终止" /> <input id="saveBtn"
			style="height: 25px; width: 60px;" type="button"
			onclick="saveData()" value="保存" />
	</div>
	<table border=0
		style="width: 100%; height: 100%; border-collapse: collapse; border-spacing: 0px;">
		<tr>
			<td>
				<div id="mapDiv"></div>
			</td>
		</tr>
		<tr>
			<td style="width: 100%; height: 150px; vertical-align: top">
				<div id="infoOpenDiv"
					style="overflow: auto; height: 100%; width: 100%"></div>
			</td>
		</tr>
	</table>
</body>

</html>
