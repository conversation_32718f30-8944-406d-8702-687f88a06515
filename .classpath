<?xml version="1.0" encoding="UTF-8"?>
<classpath>
	<classpathentry kind="src" path="src"/>
	<classpathentry kind="con" path="org.eclipse.jst.j2ee.internal.web.container"/>
	<classpathentry kind="con" path="org.eclipse.jst.j2ee.internal.module.container"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/asm-3.3.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/asm-commons-3.3.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/asm-tree-3.3.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/commons-beanutils.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/commons-codec-1.8.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/commons-collections-3.2.1.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/commons-dbcp2-2.1.1.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/commons-fileupload-1.3.1.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/commons-httpclient-3.1.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/commons-io-2.2.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/commons-lang-2.4.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/commons-lang3-3.2.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/commons-logging-1.1.1.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/commons-pool2-2.4.2.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/dom4j-1.6.1.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/ezmorph-1.0.6.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/freemarker-2.3.22.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/jackson-core-2.2.1.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/jackson-core-asl-1.8.8.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/jackson-mapper-asl-1.8.8.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/javassist-3.11.0.GA.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/json-lib-2.2.3-jdk13.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/json-lib-2.3-jdk15.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/log4j-api-2.2.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/log4j-core-2.2.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/mysql-connector-java-5.1.7-bin.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/net.mapeye.core.s.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/ognl-3.0.6.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/servlet-api.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/struts2-core-********.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/struts2-json-plugin-********.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/xwork-core-********.jar"/>
	<classpathentry kind="con" path="org.eclipse.jst.server.core.container/org.eclipse.jst.server.tomcat.runtimeTarget/Apache Tomcat v8.0">
		<attributes>
			<attribute name="owner.project.facets" value="jst.web"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="con" path="org.eclipse.jdt.launching.JRE_CONTAINER"/>
	<classpathentry kind="output" path="build/classes"/>
</classpath>
