package cn.edu.ntu.utils.others;

import org.springframework.data.redis.core.RedisTemplate;

public class MultiMediaEventInfoRedisService {

	
	private RedisTemplate<String, MultiMediaEventInfo> redisTemplate;
	
	public void addOrUpdate(MultiMediaEventInfo info){
		redisTemplate.opsForValue().set("multiMediaEventInfo.endNumber."+info.getEndNumberString(), info);
	}
	
	public MultiMediaEventInfo load(String endNumberString){
		return redisTemplate.opsForValue().get("multiMediaEventInfo.endNumber."+endNumberString);
	}
}
