package cn.edu.ntu.service.common.implement;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.edu.ntu.dao.common.interfaces.EFenceDao;
import cn.edu.ntu.entity.common.PolygonAreaSettingParam;
import cn.edu.ntu.entity.common.RectangleAreaSettingParam;
import cn.edu.ntu.entity.common.RoundAreaSettingParam;
import cn.edu.ntu.service.common.interfaces.EFenceService;

@Service
public class EFenceServiceImpl implements EFenceService {

	@Autowired
	private EFenceDao eFenceDao;
	
	@Override
	public void addRoundAreaSetting(RoundAreaSettingParam param,String userName) {
		
		eFenceDao.addRoundAreaSetting(param,userName);
	}

	@Override
	public void addRectangleAreaSetting(RectangleAreaSettingParam param, String userName) {

		eFenceDao.addRectangleAreaSetting(param, userName);
	}

	@Override
	public void addPolygonAreaSetting(PolygonAreaSettingParam param, String userName) {

		eFenceDao.addPolygonAreaSetting(param,userName);
	}




}
