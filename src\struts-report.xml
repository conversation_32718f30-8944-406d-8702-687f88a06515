<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE struts PUBLIC
	"-//Apache Software Foundation//DTD Struts Configuration 2.3//EN"
	"http://struts.apache.org/dtds/struts-2.3.dtd">

<struts>

	<constant name="struts.devMode" value="true" />
	
	<constant name="struts.objectFactory" value="org.apache.struts2.spring.StrutsSpringObjectFactory" />

    <package name="report" namespace="/report" extends="json-default">
	    <action name="getDriveReportByCarNumber" class="reportAction" method="getDriveReportByCarNumber">
	        <result type="json">
	            <param name="root">driveReportList</param>
	        </result>
	    </action>
	    
	    <action name="getDriveReportByGroupName" class="reportAction" method="getDriveReportByGroupName">
	        <result type="json">
	            <param name="root">driveReportList</param>
	        </result>
	    </action>
	    
	     <action name="getIgnitionReportByCarNumber" class="reportAction" method="getIgnitionReportByCarNumber">
	        <result type="json">
	            <param name="root">ignitionReportList</param>
	        </result>
	    </action>
	    
	    <action name="getIgnitionReportByGroupName" class="reportAction" method="getIgnitionReportByGroupName">
	        <result type="json">
	            <param name="root">ignitionReportList</param>
	        </result>
	    </action>
	    
	    <action name="getParkReportByCarNumber" class="reportAction" method="getParkReportByCarNumber">
	        <result type="json">
	            <param name="root">parkReportList</param>
	        </result>
	    </action>
	    
	    <action name="getParkReportByGroupName" class="reportAction" method="getParkReportByGroupName">
	        <result type="json">
	            <param name="root">parkReportList</param>
	        </result>
	    </action>
	    
	     <action name="getVelocityReportByCarNumber" class="reportAction" method="getVelocityReportByCarNumber">
	        <result type="json">
	            <param name="root">velocityReportList</param>
	        </result>
	    </action>
	    
	    <action name="getVelocityReportByGroupName" class="reportAction" method="getVelocityReportByGroupName">
	        <result type="json">
	            <param name="root">velocityReportList</param>
	        </result>
	    </action>
	    
	    <action name="getMileageReportByCarNumber" class="reportAction" method="getMileageReportByCarNumber">
	        <result type="json">
	            <param name="root">mileageReportList</param>
	       </result>
	    </action>
	    
	    <action name="getMileageReportByGroupName" class="reportAction" method="getMileageReportByGroupName">
	       <result type="json">
	            <param name="root">mileageReportList</param>
	       </result>
	    </action>
	    
	    <action name="getWarningReportByCarNumber" class="reportAction" method="getWarningReportByCarNumber">
	        <result type="json">
	            <param name="root">warningReportList</param>
	        </result>
	    </action>
	    
	    <action name="getWarningReportByGroupName" class="reportAction" method="getWarningReportByGroupName">
	        <result type="json">
	            <param name="root">warningReportList</param>
	        </result>
	    </action>
	    
	     <action name="getDriverReportByDriverName" class="reportAction" method="getDriverReportByDriverName">
	        <result type="json">
	            <param name="root">driverReportList</param>
	        </result>
	    </action>
	    
	     <action name="getDriverReportByCarNumber" class="reportAction" method="getDriverReportByCarNumber">
	        <result type="json">
	            <param name="root">driverReportList</param>
	        </result>
	    </action>
	</package>
	
	
	
</struts>