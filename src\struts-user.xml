<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE struts PUBLIC
	"-//Apache Software Foundation//DTD Struts Configuration 2.3//EN"
	"http://struts.apache.org/dtds/struts-2.3.dtd">

<struts>

	<constant name="struts.devMode" value="true" />
	
	<constant name="struts.objectFactory" value="org.apache.struts2.spring.StrutsSpringObjectFactory" />
	
	<package name="user" namespace="/user" extends="struts-default"> 
        <action name="loginAction" class="loginAction" method="login">
			<result >/page/index.jsp</result>
			<result name="input">/page/login.jsp</result>
		</action>
		
		<action name="loginOutAction" class="loginAction" method="loginOut">
		    <result>/page/login.jsp</result>
		</action>

        <action name="listAction" class="userAction" method="list">
            <result>/page/userManage.jsp</result>
        </action>
        
        <action name="addAction" class="userAction" method="add">
            <result>/page/userEdit.jsp</result>
        </action>
        
         <action name="addStoreAction" class="userAction" method="addStore">
            <result type="redirect">listAction.action</result>
        </action>
        
        
        <action name="editAction" class="userAction" method="edit">
            <result>/page/userEdit.jsp</result>
        </action>
        
         <action name="editStoreAction" class="userAction" method="editStore">
            <result type="redirect">listAction.action</result>
        </action>
        
        
        <action name="deleteAction" class="userAction" method="delete">
            <result type="redirect">listAction.action</result>
        </action>
        
        <action name="queryAction" class="userAction" method="query">
            <result>/page/userManage.jsp</result>
        </action>
    </package>
    
  </struts>