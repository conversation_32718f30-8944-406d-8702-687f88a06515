<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@taglib prefix="s" uri="/struts-tags"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>车辆信息管理</title>

<link rel="stylesheet" type="text/css"
	href="/808FrontProject/css/user.css">

<script type="text/javascript" src="/808FrontProject/js/lib.jquery.js"></script>
<script type="text/javascript" src="/808FrontProject/js/tools.js"></script>

<!-- date component  -->
<script type="text/javascript"
	src="/808FrontProject/js/lib.jsdate/jscal2.js"></script>
<script type="text/javascript"
	src="/808FrontProject/js/lib.jsdate/en.js"></script>
<link rel="stylesheet" type="text/css"
	href="/808FrontProject/js/lib.jsdate/jscal2.css">
<link rel="stylesheet" type="text/css"
	href="/808FrontProject/js/lib.jsdate/border-radius.css">
<link rel="stylesheet" type="text/css"
	href="/808FrontProject/js/lib.jsdate/steel.css">

<!-- treeview component -->
<link rel="stylesheet"
	href="/808FrontProject/js/lib.jtree/jquery.treeview.css" />
<link rel="stylesheet" href="/808FrontProject/js/lib.jtree/screen.css" />
<script src="/808FrontProject/js/lib.jtree/jquery.cookie.js"></script>
<script src="/808FrontProject/js/lib.jtree/jquery.treeview.js"></script>

<!-- dynatree component -->
<link rel='stylesheet' type='text/css'
	href='/808FrontProject/css/ui.dynatree.css'>
<script type="text/javascript" src='/808FrontProject/js/jquery-ui.js'></script>
<script type="text/javascript"
	src='/808FrontProject/js/jquery.cookie.js'></script>
<script type="text/javascript"
	src='/808FrontProject/js/jquery.dynatree.js'></script>
<script type="text/javascript"
	src="/808FrontProject/js/jquery-migrate-1.2.1.js"></script>



<script type="text/javascript">

	$(window).load(
			function() {
				//load the treeview components
				$("#black, #gray").treeview({
					//control : "#treecontrol",
					persist : "cookie",
					cookieId : "treeview-black"
				});
                
				//经营范围列表
				var hylxe = document.getElementById("hylxMenu")
						.getElementsByTagName("a");
				for (var i = 0; i < hylxe.length; i++) {
					hylxe[i].onclick = function() {
						$("#hylx").val(this.innerHTML);
					}
				}

				//车辆类型列表
				var carTypeMenuElement = document.getElementById("carTypeMenu")
						.getElementsByTagName("a");
				for (var i = 0; i < carTypeMenuElement.length; i++) {
					carTypeMenuElement[i].onclick = function() {
						$("#carType").val(this.innerHTML);
					}
				}

				//date componenent
				var cal = Calendar.setup({
					onSelect : function(cal) {
						cal.hide()
					},
					showTime : true
				});
				cal.manageFields("date1Btn", "networkEntryTime", "%Y-%m-%d");
				cal.manageFields("date2Btn", "factoryTime", "%Y-%m-%d");

				//load the dynatree component
				$("#vehicleGroupTree").dynatree({
					checkbox : true,
					selectMode : 2,
					initAjax : {
						url : "/808FrontProject/common/showGroupTreeAction",
						dataType : "json",
						type : "post"
					},
					onSelect : function(select, dtNode) {
						var selNodes = dtNode.tree.getSelectedNodes();
						var seltitles = $.map(selNodes, function(node) {
							return node.data.title;
						});
						var selTitleString = seltitles.join(",");
						$("#vehicleGroupName").val(selTitleString);

					}
				});

				setTimeout("selectVehicleGroup()", 1000);
				
				if($("#vehicleModifyFlag").val()==0){
					$("#carNumber").blur(function(){
						validateCarNumber(this);
					});
				}else{
					$("#carNumber").focus(function(){
						this.blur();
					});
				}
				
				$("#simCardNumber").blur(function(){
					validateSimCardNumber(this);
				});
				
			});

	function selectVehicleGroup() {
		var vehicleGroupName = $("#vehicleGroupName").val();
		if (vehicleGroupName != "") {
			var vehicleGroupSelections = vehicleGroupName.split(",");
			for (var i = 0; i < vehicleGroupSelections.length; i++) {
				$("#vehicleGroupTree").dynatree("getTree").selectKey(
						vehicleGroupSelections[i]);
			}
		}
	}

	//行业类型下拉树形菜单 
	function resizeHylx() {
		var obj = document.getElementById("hylx");
		var offsetX = 0;
		var offsetY = $(obj).height() + 5;
		var x = pageX(obj) + offsetX;
		var y = pageY(obj) + offsetY;
		$("#hylxMenu").css("left", x + "px");
		$("#hylxMenu").css("top", y + "px");
	}
	function showHylx() {
		resizeHylx();
		$("#hylxMenu").css("width", "205px");
		$("#hylxMenu").css("height", "300px");
	}
	function closeHylx() {
		$("#hylxMenu").css("width", "0px");
		$("#hylxMenu").css("height", "0px");
	}
	function closeHylxDiv() {
		t = setTimeout('closeHylx()', 100);
	}
	var t;

	//车辆类型下拉树形菜单 
	function resizeCarType() {
		var obj = document.getElementById("carType");
		var offsetX = 0;
		var offsetY = $(obj).height() + 5;
		var x = pageX(obj) + offsetX;
		var y = pageY(obj) + offsetY;
		$("#carTypeMenu").css("left", x + "px");
		$("#carTypeMenu").css("top", y + "px");
	}
	function showCarType() {
		resizeCarType();
		$("#carTypeMenu").css("width", "205px");
		$("#carTypeMenu").css("height", "300px");
	}
	function closeCarType() {
		$("#carTypeMenu").css("width", "0px");
		$("#carTypeMenu").css("height", "0px");
	}
	//在这里调用setTimeout方法让它在一段很短的时间后执行  

	var t;

	function closeDiv() {
		t = setTimeout('closeCarType()', 100);
	}
	//将setTimeout事件remove掉  
	function clearTime() {
		clearTimeout(t);
	}

	//行业类型下拉树形菜单 [E] ---------------------

	function showTextField() {
		var carColorOption = $("#carColorOption").val();
		if (carColorOption == "其他")
			$("#carColorText").show();
		else
			$("#carColorText").hide();
	}

	function onSelectChange(obj, scope) {
		if (scope == "city") {
			$.ajax({
				url : "/808FrontProject/common/cityInfoAction.action",
				data : {
					"parentLocation" : obj.value
				},
				dataType : "json",
				success : function(data) {
					createCitySelectOption(data);
				}
			});
		} else if (scope == "area") {
			$.ajax({
				url : "/808FrontProject/common/areaInfoAction.action",
				data : {
					"parentLocation" : obj.value
				},
				dataType : "json",
				success : function(data) {
					createAreaSelectOption(data);
				}
			});
		}

	}

	function createCitySelectOption(data) {
		if (data != null && data.length > 0) {
			$("#city").html("");
			var noneOption = document.createElement("option");
			noneOption.setAttribute("value", "请选择：");
			noneOption.appendChild(document.createTextNode("请选择："));
			$("#city").append(noneOption);
			for (var i = 0; i < data.length; i++) {
				var option = document.createElement("option");
				option.setAttribute("value", data[i]);
				option.appendChild(document.createTextNode(data[i]));
				$("#city").append(option);
			}
		}
	}

	function createAreaSelectOption(data) {
		if (data != null && data.length > 0) {
			$("#area").html("");
			var noneOption = document.createElement("option");
			noneOption.setAttribute("value", "请选择：");
			noneOption.appendChild(document.createTextNode("请选择："));
			$("#area").append(noneOption);
			for (var i = 0; i < data.length; i++) {
				var option = document.createElement("option");
				option.setAttribute("value", data[i]);
				option.appendChild(document.createTextNode(data[i]));
				$("#area").append(option);
			}
		}
	}

	function validateCarNumber(field) {
		if($.trim(field.value)==""){
			$("#validationResultImage").attr("src","/808FrontProject/image/false.png");
			$("#storeButton").attr("disabled","disabled");
		}else{
			$.ajax({
				url : "/808FrontProject/common/validateCarNumberAction",
				data : {
					carNumber : $.trim(field.value)
				},
				type : "post",
				dataType : "json",
				success : function(data) {
					if(data=="车牌号已存在"){
						$("#validationResultImage").attr("src","/808FrontProject/image/false.png");
						$("#storeButton").attr("disabled","disabled");
					}else{
						$("#validationResultImage").attr("src","/808FrontProject/image/true.png");
						$("#storeButton").removeAttr("disabled");
					}
				}

			});
		}
	}
	
	function validateSimCardNumber(field){
		if($.trim(field.value)==""){
			$("#validationResultImage2").attr("src","/808FrontProject/image/false.png");
			$("#storeButton").attr("disabled","disabled");
		}else{
			$.ajax({
				url : "/808FrontProject/common/validateSimCardNumberAction",
				data : {
					simCardNumber : $.trim(field.value)
				},
				type : "post",
				dataType : "json",
				success : function(data) {
					if(data=="卡号已存在"){
						$("#validationResultImage2").attr("src","/808FrontProject/image/false.png");
						$("#storeButton").attr("disabled","disabled");
					}else{
						$("#validationResultImage2").attr("src","/808FrontProject/image/true.png");
						$("#storeButton").removeAttr("disabled");
					}
				}

			});
		}
	}

	function store() {
		var carColor;
		if ($("#carColorOption").val() != "其他")
			carColor = $("#carColorOption").val();
		else
			carColor = $("#carColorText").val();
		$("#carColor").val(carColor);

		var carArea = "";
		if ($("#province").val() != "请选择：") {
			carArea = $("#province").val();
			if ($("#city").val() != "请选择：") {
				carArea += $("#city").val();
				if ($("#area").val() != "请选择：")
					carArea += $("#area").val();
			}
			$("#carArea").val(carArea);
		}

		if ($.trim(carColor) == "") {
			alert("车牌颜色不能为空！");
			return false;
		}

		var simCardNumber = $("#simCardNumber").val();
		if ($.trim(simCardNumber) == "") {
			alert("sim卡号不能为空！");
			return false;
		}
		
		var carNumber = $("#carNumber");
		 $("#memberCarNumber").val(carNumber.val());

		/*
		 var carNumber = $("#carNumber");
		 //validating whether the value is null
		 if (carNumber.val() == "") {
		 alert("车牌号不为空！");
		 carNumber.focus();
		 return false;
		 }

		 var carType = $("#carType");
		 if (carType.val() == "") {
		 alert("车辆类型不为空！");
		 carType.focus();
		 return false;
		 }

		 var accessNetTime = $("#networkEntryTime");
		 if (accessNetTime.val() == "") {
		 alert("入网时间不为空！");
		 accessNetTime.focus();
		 return false;
		 }

		 if ($("#carArea").val() == "") {
		 alert("请选择所属地区！");
		 return false;
		 }

		 var vBzScope = $("input[name='vehicleInfo.businessScope']");
		 if (vBzScope.val() == "") {
		 alert("经营范围不为空！");
		 vBzScope.focus();
		 return false;
		 }

		 var vId = $("input[name='vehicleInfo.endId']");
		 if (vId.val() == "") {
		 alert("终端ID不为空！");
		 vId.focus();
		 return false;
		 }

		 var vIdType = $("select[name='vehicleInfo.endType']");
		 if (vIdType.find("option:selected").val() == 0) {
		 alert("请选择终端类型！");
		 vIdType.focus();
		 return false;
		 }

		 var carOwner = $("input[name='vehicleInfo.carOwner']");
		 var carContact = $("input[name='vehicleInfo.contact']");
		 var carContactTel = $("input[name='vehicleInfo.contactPhoneNumber']");
		 if (carOwner.val() == "") {
		 alert("车主/业主不为空！");
		 carOwner.focus();
		 return false;
		 }
		 if (carContact.val() == "") {
		 alert("联系人不为空！");
		 carContact.focus();
		 return false;
		 }
		 if (carContactTel.val() == "") {
		 alert("联系人手机不为空！");
		 carContactTel.focus();
		 return false;
		 }
		 */
		if ($("#vehicleModifyFlag").val() == 0)
			$("#modifyVehicleInfoForm").attr("action",
					"/808FrontProject/vehicle/addStoreAction.action").submit();
		else
			$("#modifyVehicleInfoForm").attr("action",
					"/808FrontProject/vehicle/editStoreAction.action").submit();
	}
</script>
</head>

<body>
	<span class="title1">车辆信息管理</span>
	<s:form id="modifyVehicleInfoForm" theme="simple">
		<s:hidden name="vehicleInfo.carColor" id="carColor"></s:hidden>
		<s:hidden name="vehicleInfo.carArea" id="carArea"></s:hidden>
		<s:hidden name="vehicleInfo.vehicleGroupName" id="vehicleGroupName"></s:hidden>
		<s:hidden name="memberInfo.carNumber" id="memberCarNumber"></s:hidden>
		<s:if test='null == vehicleInfo'>
			<s:hidden id="vehicleModifyFlag" value="0">
			</s:hidden>
		</s:if>
		<s:else>
			<s:hidden id="vehicleModifyFlag" value="1">
			</s:hidden>
		</s:else>

		<table class="tableCommon">
			<tr>
				<td colspan="6" class="title2 thColor" id="userTag">车辆信息</td>
			</tr>
			<tr>
				<td width="10%">车牌号码</td>
				<td width="17%"><s:textfield name="vehicleInfo.carNumber"
						id="carNumber" /> *<img id="validationResultImage" 
						src="/808FrontProject/image/blank.png"></td>
				<td width="10%">车牌颜色</td>
				<td width="17%"><select id="carColorOption"
					onchange="showTextField()">
						<option value="黄色">黄色</option>
						<option value="蓝色">蓝色</option>
						<option value="黑色">黑色</option>
						<option value="白色">白色</option>
						<option value="其他">其他</option>
				</select> <input type="text" id="carColorText"
					style="width: 100px; display: none;" />*</td>
				<td width="11%">车辆识别代码/车架号</td>
				<td><s:textfield name="vehicleInfo.carIdentificationCode" /></td>
			</tr>
			<tr>
				<td>车辆类型</td>
				<td><s:textfield name="vehicleInfo.carType" id="carType"
						onclick="showCarType()" onmouseout="closeDiv();"
						onmouseover="clearTime();" /> 
					<div id="carTypeMenu" onmouseout="closeDiv();"
						onmouseover="clearTime();"
						style="position: absolute; z-index: 9000; display: block; overflow-y: auto; background-color: #ffffff; height: 0px; font-size: 14px; line-height: 20px;">
						<ul id="black" class="treeview-black">
							<li></li>
							<li><span><a>出租车</a></span></li>
							<li><span><a>危险品运输车</a></span></li>
							<li><span><a>农用车</a></span></li>
							<li><span><a>拖拉机</a></span></li>
							<li><span><a>客车</a></span>
								<ul>
									<li><a>大型客车</a></li>
									<li><a>中型客车</a></li>
									<li><a>小型客车</a></li>
									<li><a>大型卧铺客车</a></li>
									<li><a>小型卧铺客车</a></li>
								</ul></li>
							<li><span><a>普通货车</a></span>
								<ul>
									<li><a>大型普通货车</a></li>
									<li><a>中型普通货车</a></li>
									<li><a>小型普通货车</a></li>
								</ul></li>
							<li><span><a>专用运输车</a></span>
								<ul>
									<li><a>集装箱车</a></li>
									<li><a>大件运输车</a></li>
									<li><a>保温冷藏车</a></li>
									<li><a>商品车专用运输车</a></li>
									<li><a>罐车</a></li>
									<li><a>牵引车</a></li>
									<li><a>挂车</a></li>
									<li><a>平板车</a></li>
								</ul></li>
							<li><span><a>其他车辆</a></span></li>
						</ul>
					</div></td>
				<td>入网时间</td>
				<td><s:textfield name="vehicleInfo.networkEntryTime"
						id='networkEntryTime' style='width: 100px; margin-top: 2px'></s:textfield>
					<button id="date1Btn" type="button">...</button> </td>
				<td>所属地区</td>
				<td>省份：<select name="province" id="province"
					onchange="onSelectChange(this,'city');">
						<option value="请选择：">请选择：</option>
						<option value="北京市">北京市</option>
						<option value="天津市">天津市</option>
						<option value="河北省">河北省</option>
						<option value="山西省">山西省</option>
						<option value="内蒙古自治区">内蒙古自治区</option>
						<option value="辽宁省">辽宁省</option>
						<option value="吉林省">吉林省</option>
						<option value="黑龙江省">黑龙江省</option>
						<option value="上海市">上海市</option>
						<option value="江苏省">江苏省</option>
						<option value="浙江省">浙江省</option>
						<option value="安徽省">安徽省</option>
						<option value="福建省">福建省</option>
						<option value="江西省">江西省</option>
						<option value="山东省">山东省</option>
						<option value="河南省">河南省</option>
						<option value="湖北省">湖北省</option>
						<option value="湖南省">湖南省</option>
						<option value="广东省">广东省</option>
						<option value="广西壮族自治区">广西壮族自治区</option>
						<option value="海南省">海南省</option>
						<option value="重庆市">重庆市</option>
						<option value="四川省">四川省</option>
						<option value="贵州省">贵州省</option>
						<option value="云南省">云南省</option>
						<option value="西藏自治区">西藏自治区</option>
						<option value="陕西省">陕西省</option>
						<option value="甘肃省">甘肃省</option>
						<option value="青海省">青海省</option>
						<option value="宁夏回族自治区">宁夏回族自治区</option>
						<option value="新疆维吾尔自治区">新疆维吾尔自治区</option>
				</select> 城市：<select name="city" id="city"
					onchange="onSelectChange(this,'area');"></select> 区(县)：<select
					name="area" id="area">
				</select> 

				</td>
			</tr>
			<tr>
				<td>总质量</td>
				<td><s:textfield name="vehicleInfo.totalWeight"></s:textfield>(kg)</td>
				<td>核定载质量</td>
				<td><s:textfield name="vehicleInfo.approvedLoadWeight"></s:textfield>(kg)</td>
				<td>准牵引总质量</td>
				<td><s:textfield name="vehicleInfo.approvedTractionWeight"></s:textfield>(kg)</td>
			</tr>
			<tr>
				<td>车辆外廓尺寸</td>
				<td width="200px">长<s:textfield style="width:40px"
						name="vehicleInfo.outsideLength"></s:textfield> 宽<s:textfield
						style="width:40px" name="vehicleInfo.outsideWidth"></s:textfield>
					高<s:textfield style="width:40px" name="vehicleInfo.outsideHeight"></s:textfield>
					(mm)
				</td>
				<td>货厢内部尺寸</td>
				<td>长<s:textfield style="width:40px"
						name="vehicleInfo.insideLength"></s:textfield> 宽<s:textfield
						style="width:40px" name="vehicleInfo.insideWidth"></s:textfield> 高<s:textfield
						style="width:40px" name="vehicleInfo.insideHeight"></s:textfield>
					(mm)
				</td>
			<tr>
				<td>车辆出厂时间</td>
				<td><s:textfield name="vehicleInfo.factoryTime"
						id='factoryTime' style='width: 100px; margin-top: 2px'></s:textfield>

					<button id="date2Btn" type="button">...</button></td>
				<td>经营范围</td>
				<td><s:textfield name="vehicleInfo.businessScope" id="hylx"
						onclick="showHylx()" onmouseout="closeHylxDiv();"
						onmouseover="clearTime();"></s:textfield> 
					<div id="hylxMenu" onmouseout="closeHylxDiv();"
						onmouseover="clearTime();"
						style="position: absolute; z-index: 9000; display: block; overflow-y: auto; background-color: #ffffff; height: 0px; font-size: 14px; line-height: 20px;">
						<ul id="black" class="treeview-black">
							<li></li>
							<li><span><a>道路旅客运输</a></span>
								<ul>
									<li><a>班车客运</a></li>
									<li><a>包车客运</a></li>
									<li><a>定线旅游</a></li>
									<li><a>非定线旅游</a></li>
								</ul></li>
							<li><span><a>道路货物运输</a></span>
								<ul>
									<li><a>道路普通货物运输</a></li>
									<li><a>货物专用运输</a></li>
									<li><a>大型物件运输</a></li>
								</ul></li>
							<li><span><a>道路危险货物运输</a></span>
								<ul>
									<li><a>营运性危险货物运输</a></li>
									<li><a>非经营性危险货物运输</a></li>
								</ul></li>
							<li><span><a>机动车维修</a></span>
								<ul>
									<li><a>汽车维修</a></li>
									<li><a>危险货物运输车辆维修</a></li>
									<li><a>摩托车维修</a></li>
									<li><a>其他机动车维修</a></li>
								</ul></li>
							<li><span><a>机动车驾驶员培训</a></span>
								<ul>
									<li><a>普通机动车驾驶员培训</a></li>
									<li><a>道路运输驾驶员从业资格培训</a></li>
									<li><a>机动车驾驶员培训教练场</a></li>
								</ul></li>
							<li><span><a>公交运输</a></span>

								<ul>
									<li><a>公交运输</a></li>
								</ul></li>
							<li><span><a>出租运输</a></span>
								<ul>
									<li><a>客运出租运输</a></li>
									<li><a>货运出租运输</a></li>
								</ul></li>
							<li><span><a>汽车租凭</a></span>
								<ul>
									<li><a>客运汽车租赁</a></li>
									<li><a>货运汽车租凭</a></li>
								</ul></li>
						</ul>
					</div></td>
				<td>SIM卡号</td>
				<td><s:textfield name="vehicleInfo.simCardNumber" id="simCardNumber"></s:textfield> *<img id="validationResultImage2" 
						src="/808FrontProject/image/blank.png"></td>
			<tr>
				<td>终端ID</td>
				<td><s:textfield name="vehicleInfo.endId"></s:textfield> </td>
				<td>终端类型</td>
				<td><s:select name="vehicleInfo.endType"
						list="#{1:'VMSMS',2:'VMGPRS',3:'VMYWT',4:'VMBB808' }"
						listKey="value" listValue="value" headerKey="0" headerValue="请选择"></s:select>
					</td>
				<td>终端厂商</td>
				<td><s:textfield name="vehicleInfo.endmanufacturer"></s:textfield></td>
			</tr>
			<tr>
				<td>车主/业主</td>
				<td><s:textfield name="vehicleInfo.carOwner"></s:textfield> </td>
				<td>联系人</td>
				<td><s:textfield name="vehicleInfo.contact"></s:textfield> </td>
				<td>联系人手机</td>
				<td><s:textfield name="vehicleInfo.contactPhoneNumber"></s:textfield>
					</td>
			</tr>
			<tr>
				<td colspan="6" class="title2 thColor">会员信息</td>
			</tr>
			<tr>
				<td>会员号</td>
				<td><s:textfield name="memberInfo.name"></s:textfield></td>
				<td>服务密码</td>
				<td><s:textfield name="memberInfo.password"></s:textfield></td>
			</tr>
			<tr>
				<td colspan="6" class="title2 thColor">车组信息</td>
			</tr>
			<tr>
				<td>车辆组</td>
				<td colspan="5">

					<div id="vehicleGroupTree"
						style="width: 100%; height: 300px; overflow: auto; font-size: 13px; line-height: 20px;">
					</div>

				</td>
			</tr>
			<tr>
				<td colspan="6" style="padding-left: 0px; text-align: center;"><input
					class="buttonStyle" id="storeButton" type="button" value="保存" onclick="store()" />&nbsp;<input
					class="buttonStyle" type="button" value="返回"
					onclick="history.go(-1);" /></td>
			</tr>
		</table>
	</s:form>


</body>
</html>