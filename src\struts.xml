<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE struts PUBLIC
	"-//Apache Software Foundation//DTD Struts Configuration 2.3//EN"
	"http://struts.apache.org/dtds/struts-2.3.dtd">

<struts>

	<constant name="struts.devMode" value="true" />
	
	<constant name="struts.objectFactory" value="org.apache.struts2.spring.StrutsSpringObjectFactory" />
	
	<constant name="struts.i18n.encoding" value="UTF-8"/> 

	<include file="struts-endControl.xml"/>
	
	<include file="struts-vehicle.xml"/>
	
	<include file="struts-user.xml"/>
	
	<include file="struts-driver.xml"/>
    
    <include file="struts-vehicleGroup.xml"/>
    
	<include file="struts-showVehicle.xml"/>
	
	<include file="struts-report.xml"/>
	
	<include file="struts-endParams.xml"/>

	<include file="struts-eFence.xml"/>
    
    <package name="common" namespace="/common" extends="json-default">
     
	    <action name="showGroupTreeAction" class="vehicleGroupTreeAction" method="showGroupTree">
	    </action>
	    
	    <action name="showUserRelateGroupTreeAction" class="vehicleGroupTreeAction" method="showUserRelateGroupTree">
	    </action>
	    
		<action name="cityInfoAction" class="locationAction" method="getCityInfo">
		    <result type="json">
				<param name="root">cityInfoList</param>
			</result>
		</action>
        
        <action name="areaInfoAction" class="locationAction" method="getAreaInfo">
            <result type="json">
                <param name="root">areaInfoList</param>
            </result>
        </action>
        
        <action name="uploadDriverCertificatePhotoAction" class="uploadFileAction" method="uploadDriverCertificatePhoto">
            <result type="json" name="success">
                 <param name="contentType">
                    text/html
                </param>
            </result>
            <result type="json" name="error">
                <param name="contentType">
                    text/html
                </param>
            </result>
        </action>

        <action name="getCertificatePhotoAction" class="photoAction" method="getCertificatePhoto">
        </action>       
 
        <action name="queryCurrentPhotoAction" class="photoAction" method="queryCurrentPhotoFromDB">
            <result type="json">
                <param name="root">webImagePath</param>
            </result>
        </action>
        
        <action name="queryHistoryPhotoAction" class="photoAction" method="queryHistoryPhotoFromDB">
            <result type="json">
                <param name="root">photoDescriptionList</param>
            </result>
        </action>
        
        <action name="createCheckCodeImageAction" class="loginAction" method="createCheckCodeImage">
            <result name="success" type="stream">
                <param name="contentType">image/jpeg</param>
                <param name="inputName">inputStream</param>
            </result>        
        </action>
        
        <action name="selectAllGroupAction" class="vehicleGroupAction" method="list">
             <result type="json">
                <param name="root">vehicleGroupInfoList</param>
            </result>
        </action>
        
        <action name="selectVehicleAction" class="vehicleAction" method="selectVehicle">
            <result type="json">
                <param name="root">vehicleInfoList</param>
            </result>
        </action>
        
        <action name="selectAllVehicleAction" class="vehicleAction" method="list">
            <result type="json">
                <param name="root">vehicleInfoList</param>
            </result>
        </action>
        
        <action name="selectAllDriverAction" class="driverAction" method="list">
            <result type="json">
                <param name="root">driverInfoList</param>
            </result>
        </action>
        
         <action name="getVehicleInfoAction" class="vehicleAction" method="getInfo">
             <result type="json">
                 <param name="root">vehicleInfo</param>
             </result>
         </action>
         
         <action name="getCurrentMultiMediaEventInfoAction" class="multiMediaEventAction" method="getCurrentMultiMediaEventInfoInRedis">
             <result type="json">
                 <param name="root">multiMediaEventInfoList</param>
             </result>
         </action>
         
         <action name="getMultiMediaEventInfoBySerialNumberAction" class="multiMediaEventAction" method="getMultiMediaEventInfoBySerialNumber">
             <result type="json">
                 <param name="root">multiMediaEventInfo</param>
             </result>
         </action>
         
         <action name="getMultiMediaEventInfoByCreateTimeAction" class="multiMediaEventAction" method="getMultiMediaEventInfoByCreateTime">
             <result type="json">
                 <param name="root">multiMediaEventInfo</param>
             </result>
         </action>
         
          <action name="getMultiMediaEventInfoBySimCardNumberAction" class="multiMediaEventAction" method="getMultiMediaEventInfoBySimCardNumber">
             <result type="json">
                 <param name="root">multiMediaEventInfo</param>
             </result>
         </action>
         
          <action name="queryCommandResultAction" class="commandInfoAction" method="queryCommandResult">
             <result type="json">
                 <param name="root">commandInfo</param>
             </result>
         </action>
         
         <action name="queryCommandResultsAction" class="commandInfoAction" method="queryCommandResults">
             <result type="json">
                 <param name="root">commandInfo</param>
             </result>
         </action>
         
         <action name="validateUserNameAction" class="validationAction" method="validateUserName">
             <result type="json">
                 <param name="root">result</param>
             </result>
         </action>
         
          <action name="validateCarNumberAction" class="validationAction" method="validateCarNumber">
             <result type="json">
                 <param name="root">result</param>
             </result>
         </action>
         
          <action name="validateSimCardNumberAction" class="validationAction" method="validateSimCardNumber">
             <result type="json">
                 <param name="root">result</param>
             </result>
         </action>
         
         <action name="validateVehicleGroupNameAction" class="validationAction" method="validateVehicleGroupName">
             <result type="json">
                 <param name="root">result</param>
             </result>
         </action>
         
         <action name="validateDriverIdAction" class="validationAction" method="validateDriverId">
             <result type="json">
                 <param name="root">result</param>
             </result>
         </action>
         
         <action name="answerMessageQueryAction" class="messageQueryAction" method="answerMessageQuery">
         </action>
         
	</package>
</struts>
