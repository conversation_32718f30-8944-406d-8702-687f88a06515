// Written by <PERSON>, <PERSON><PERSON> in 2016-08-13
// Version: 1.2
// Modified by <PERSON>, Che in 2017-05-27
//
// Class name: MapeyeWin
// Method names: winAdd, resizeWinLayout
// Copyright by www.mapeye.net
//
function MapeyeWin(winId, winNameId, winFormId) {
	this.winId = winId;
	this.winNameId = winNameId;
	this.winFormId = winFormId;

	var obj = document.getElementById(winId);
	var wint = document.getElementById(winNameId);
	var winc = document.getElementById(winFormId);
	// alert(obj+", "+wint+", "+winc);

	// 设置winId的样式宽度
	obj.setAttribute("width", obj.offsetWidth);

	wint.className = "winName";
	winc.className = "winForm";

	// var tabs = wint.getElementsByClassName("tag");
	// var divs = winc.getElementsByTagName("div");

	this.getChildNodes = function(obj) {
		var childArr = obj.children;
		var childArrTem = new Array(); // 临时数组，用来存储符合条件的节点
		for (var i = 0, len = childArr.length; i < len; i++) {
			if (childArr[i].nodeType == 1) {
				childArrTem.push(childArr[i]); // push() 方法将节点添加到数组尾部
			}
		}
		return childArrTem;
	}

	var tabs = this.getChildNodes(wint);
	var divs = this.getChildNodes(winc);

	for (var i = 0; i < tabs.length; i++) {
		tabs[i].setAttribute("onclick", "new MapeyeWin('" + winId + "','"
				+ winNameId + "','" + winFormId + "').winClick(this)");
	}

	var winNameW = 100;
	var winNameH = 30;

	var winNameFocus = "";

	var imgPath = "./lib.mewin/";
	this.setImgPath = function(picPath) {
		imgPath = picPath + imgPath;
	}

	this.init = function() {
		// alert("Calling the init();");
		this.resizeWinLayout();
		this.setWinStyle();
		this.setFWinStyle();
		if (tabs.length > 0) {
			winNameFocus = tabs[0].innerHTML;
		}
	}

	this.getWinNameFocus = function() {
		//alert(winNameFocus);
		return winNameFocus;
	}

	this.winClick = function(obj) {
		tabs = this.getChildNodes(wint);
		divs = this.getChildNodes(winc);
		this.change(obj);
		winNameFocus = obj.innerHTML;
	}

	this.setFWinStyle = function() { // FWin: first wintag
		tabs = this.getChildNodes(wint);
		divs = this.getChildNodes(winc);
		var n = tabs.length;
		var m = divs.length;
		if (n > 0) {
			tabs[0].className = "winNameFocus";
		}
		if (m > 0) {
			divs[0].className = "winFormFocus";
		}
	}

	this.setLWinStyle = function() {// LWin: last wintag
		tabs = this.getChildNodes(wint);
		divs = this.getChildNodes(winc);
		var n = tabs.length;
		var m = divs.length;
		if (n > 0) {
			tabs[n - 1].className = "winNameFocus";
		}
		if (m > 0) {
			divs[m - 1].className = "winFormFocus";
		}
	}

	this.setCurWinStyle = function(obj) {// CurWin: current wintag
		obj.className = "winNameFocus";
	}

	this.setWinStyle = function() {// LWin: last wintag
		tabs = this.getChildNodes(wint);
		divs = this.getChildNodes(winc);
		var n = tabs.length;
		for (var i = 0; i < n; i++) {
			tabs[i].className = "winNameDefault";
		}
		var m = divs.length;
		for (var j = 0; j < m; j++) {
			divs[j].className = "winFormDefault";
		}
	}

	this.cmdCloseStyle = function(obj, cmdTag, imgPath) {
		if (cmdTag == "onmouseover") {
			obj.src = imgPath + "/closehover.png";
		}
		if (cmdTag == "onmouseout") {
			obj.src = imgPath + "/closedefault.png";
		}
	}

	this.winAdd = function(winName, winHTML) {
		var li = document.createElement("li");
		li.setAttribute("id", "name_" + winName);
		li.setAttribute("onclick", "new MapeyeWin('" + winId + "','"
				+ winNameId + "','" + winFormId + "').winClick(this)");
		li.setAttribute("width", winNameW + "px");
		if (winName.length < 5)
			li.innerHTML = winName;
		else
			li.innerHTML = "<a title='" + winName + "'>"
					+ winName.substring(0, 4) + "..." + "</a>";
		wint.appendChild(li);

		var img = document.createElement("img");
		img.setAttribute("src", imgPath + "/closedefault.png");
		img.setAttribute("width", "22px");
		img.setAttribute("height", "16px");
		img.setAttribute("onmouseover", "new MapeyeWin('" + winId + "','"
				+ winNameId + "','" + winFormId
				+ "').cmdCloseStyle(this,'onmouseover','" + imgPath + "')");
		img.setAttribute("onmouseout", "new MapeyeWin('" + winId + "','"
				+ winNameId + "','" + winFormId
				+ "').cmdCloseStyle(this,'onmouseout','" + imgPath + "')");
		img.setAttribute("onclick", "new MapeyeWin('" + winId + "','"
				+ winNameId + "','" + winFormId + "').winDel('" + winName
				+ "',event)");
		img.setAttribute("style", "display: inline; float: right;");
		img.setAttribute("title", "关闭");
		li.appendChild(img);

		var div = document.createElement("div");
		div.setAttribute("id", "form_" + winName);
		div.innerHTML = winHTML;
		winc.appendChild(div);

		this.resizeWinLayout();
		this.setWinStyle();
		this.setLWinStyle();

	}

	this.winDel = function(winName, event) {
		// 取消冒泡
		event.cancelBubble = true; // 屏蔽父元素的点击事件
		var liDel = document.getElementById("name_" + winName);
		wint.removeChild(liDel);
		var divDel = document.getElementById("form_" + winName);
		winc.removeChild(divDel);

		this.resizeWinLayout();
		this.setWinStyle();
		this.setFWinStyle();
	}

	this.setObjWidth = function(width) {
		// 设置winId的样式宽度
		obj.setAttribute("width", width);
	}

	this.resizeWinLayout = function() {
		// alert("Calling the resizeWinLayout()!");
		var winW = this.getObjWidth(obj);// obj.offsetWidth;
		// alert("Width of "+winId + ": " + winW);
		// tabs = wint.getElementsByTagName("li");
		tabs = this.getChildNodes(wint);
		var n = tabs.length;
		var wCur = winW;
		var offset = 1;
		var w = wCur - offset;
		if (n * winNameW > w) {
			var tabW = parseInt(w / n);
			for (var i = 0; i < n; i++) {
				tabs[i].style.width = tabW + "px";
				// alert("w: "+tabs[i].style.width);
			}
		} else {
			for (var i = 0; i < n; i++) {
				tabs[i].style.width = winNameW + "px";
				// alert("w: "+tabs[i].style.width);
			}
		}
	}

	this.change = function(obj) {
		// alert("Calling the change()!");
		this.setWinStyle();
		this.setCurWinStyle(obj);
		for (var i = 0; i < tabs.length; i++) {
			if (tabs[i] == obj) {
				divs[i].className = "winFormFocus";
				// tabName = obj.innerHTML.substr(0, 2);
			} else {
				divs[i].className = "winFormDefault";
			}
		}
	}

	this.getWinHeight = function() {
		var winHeight = 0;
		if (window.innerHeight) {
			winHeight = window.innerHeight;
		} else if ((document.body) && (document.body.clientHeight)) {
			winHeight = document.body.clientHeight;
		}
		return winHeight;
	}

	this.getWinWidth = function() {
		// alert("Calling the getWinWidth()!");
		var winWidth = 0;
		if (window.innerWidth) {
			winWidth = window.innerWidth;
		} else if ((document.body) && (document.body.clientWidth)) {
			winWidth = document.body.clientWidth;
		}
		return winWidth;
	}

	this.getObjWidth = function(obj) {
		// alert(obj.getAttribute("id"));
		var w = obj.getAttribute("width");

		if (w != null) {
			if (w.indexOf("px") > 0) {
				w = w.substring(0, w.indexOf("px"));
			}
			if (w.indexOf("%") > 0) {
				w = w.substring(0, w.indexOf("%"));
				w = parseInt(this.getObjWidth(obj.parentNode)) * parseInt(w)
						/ 100;
			}
		} else {
			w = obj.style.width;
			if (w == "") {
				w = obj.offsetWidth;
			} else {
				if (w.indexOf("px") > 0) {
					w = w.substring(0, w.indexOf("px"));
				}
				if (w.indexOf("%") > 0) {
					w = w.substring(0, w.indexOf("%"));
					w = parseInt(this.getObjWidth(obj.parentNode))
							* parseInt(w) / 100;
				}
			}
		}
		// alert(obj.getAttribute("id")+".width: "+w);
		return w;
	}

}