<?xml version="1.0" encoding="UTF-8"?>  
<beans xmlns="http://www.springframework.org/schema/beans"  
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"  
       xmlns:context="http://www.springframework.org/schema/context"  
       xsi:schemaLocation="http://www.springframework.org/schema/beans  
       http://www.springframework.org/schema/beans/spring-beans-4.2.xsd  
       http://www.springframework.org/schema/context  
       http://www.springframework.org/schema/context/spring-context-4.2.xsd">  
    <!-- 
    <bean id="trackRelateReportJobDetail" class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">  
        <property name="targetObject">  
            <bean class="cn.edu.ntu.quartz.TrackRelateReportJob" />  
        </property>  
        <property name="targetMethod" value="execute" />  
    </bean>
     
    <bean id="createTableJobDetail" class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean"  >
        <property name="targetObject">
            <bean class="cn.edu.ntu.quartz.CreateTableJob" />
        </property>
        <property name="targetMethod" value="execute" />
    </bean>    
    
    <bean id="trackRelateReportTrigger" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">  
        <property name="jobDetail" ref="trackRelateReportJobDetail" />  
        <property name="cronExpression" value="0 0 0 * * ?"/>  
    </bean> 
    
    <bean id="createTableTrigger" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
        <property name="jobDetail" ref="createTableJobDetail" />
        <property name="cronExpression" value="0 0 0 28 * ?" />
    </bean>
  
    <bean class="org.springframework.scheduling.quartz.SchedulerFactoryBean">
      
        <property name="jobDetails">  
            <list>  
                <ref bean="trackRelateReportJobDetail" />
                <ref bean="createTableJobDetail" />
            </list>  
        </property>   
        <property name="triggers">  
            <list>  
                <ref bean="trackRelateReportTrigger" />
                <ref bean="createTableTrigger" />
            </list>  
        </property> 
      
    </bean>
     -->  
</beans>  