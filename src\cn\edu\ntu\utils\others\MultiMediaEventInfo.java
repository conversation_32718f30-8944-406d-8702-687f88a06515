package cn.edu.ntu.utils.others;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import org.apache.struts2.json.annotations.JSON;

public class MultiMediaEventInfo implements Serializable{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 2261602489425943728L;
	
	private String carNumber;
	private String endNumberString;
	private int commandSerialNumber;
	private String eventType;
	private String mediaType;
	private String fileType;
	private int channelId;
	private int multiMediaId;
	private Date createTime;
	private List<String> visitorIpList;
	
	
	public String getCarNumber() {
		return carNumber;
	}
	public void setCarNumber(String carNumber) {
		this.carNumber = carNumber;
	}
	public String getEndNumberString() {
		return endNumberString;
	}
	public void setEndNumberString(String endNumberString) {
		this.endNumberString = endNumberString;
	}
	public int getCommandSerialNumber() {
		return commandSerialNumber;
	}
	public void setCommandSerialNumber(int commandSerialNumber) {
		this.commandSerialNumber = commandSerialNumber;
	}
	public String getEventType() {
		return eventType;
	}
	public void setEventType(String eventType) {
		this.eventType = eventType;
	}
	public String getMediaType() {
		return mediaType;
	}
	public void setMediaType(String mediaType) {
		this.mediaType = mediaType;
	}
	public String getFileType() {
		return fileType;
	}
	public void setFileType(String fileType) {
		this.fileType = fileType;
	}
	public int getChannelId() {
		return channelId;
	}
	public void setChannelId(int channelId) {
		this.channelId = channelId;
	}
	public int getMultiMediaId() {
		return multiMediaId;
	}
	public void setMultiMediaId(int multiMediaId) {
		this.multiMediaId = multiMediaId;
	}
	@JSON(format="yyyy-MM-dd HH:mm:ss")
	public Date getCreateTime() {
		return createTime;
	}
	
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public List<String> getVisitorIpList() {
		return visitorIpList;
	}
	public void setVisitorIpList(List<String> visitorIpList) {
		this.visitorIpList = visitorIpList;
	}

}
