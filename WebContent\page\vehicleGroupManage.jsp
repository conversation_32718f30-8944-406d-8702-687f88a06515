<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@taglib prefix="s" uri="/struts-tags"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>车辆分组管理</title>

<link rel="stylesheet" type="text/css"
	href="/808FrontProject/css/user.css">


<script type="text/javascript" src="/808FrontProject/js/lib.jquery.js"></script>
<script type="text/javascript">
	function vehicleGroupAdd() {

		$("#modifyVehicleGroupInfoForm").attr("action",
				"808FrontProject/vehicleGroup/addAction.action").submit();
	}

	function vehicleGroupEdit() {
		var selectedVehicleGroupName = $(
				'input:radio[name="selectedVehicleGroupName"]:checked').val();
		if (undefined == selectedVehicleGroupName ||selectedVehicleGroupName == "") {
			alert("请在单选按钮中选择要编辑的车组名称！");
			return false;
		}
		
		var path = "808FrontProject/vehicleGroup/editAction.action?vehicleGroupName="
				+ selectedVehicleGroupName;
		$("#modifyVehicleGroupInfoForm").attr("action", path).submit();
	}

	function vehicleGroupDelete() {
		var selectedVehicleGroupName = $(
				'input:radio[name="selectedVehicleGroupName"]:checked').val();
		if (undefined == selectedVehicleGroupName ||selectedVehicleGroupName == "") {
			alert("请在单选按钮中选择要删除的车组名称！");
			return false;
		}
		
		if (window.confirm("确定要删除当前车组名称吗？")) {
		var path = "808FrontProject/vehicleGroup/deleteAction.action?vehicleGroupName="
				+ selectedVehicleGroupName;
		$("#modifyVehicleGroupInfoForm").attr("action", path).submit();
		} else {
			return false;
		}
	}
</script>
</head>

<body>
	<span class="title1">车辆分组管理</span>
	<s:form action="queryAction.action" namespace="/vehicleGroup"
		theme="simple">
		<table class="tableCommon">
			<tr>
				<td width="150px">车组名称</td>
				<td><s:textfield name="vehicleGroupInfo.name" /></td>
				<td width="150px">所属业户</td>
				<td><s:textfield name="vehicleGroupInfo.owner" /></td>
				<td style="padding-left: 0px; text-align: center;"><input
					class="buttonStyle" type="submit" name="" value="查询" /></td>
			</tr>
		</table>
	</s:form>
	<s:form id="modifyVehicleGroupInfoForm">
		<table class="tableCommon">
			<tr class="thSize thColor">
				<td width="5%">序号</td>
				<td>车组名称</td>
				<td>所属业户</td>
				<td>车组类型</td>
				<td>经营许可证字</td>
				<td>经营许可证号</td>
				<td>联系人</td>
				<td>联系电话</td>
				<td>所属地区</td>
				<td>父车辆组</td>
				<td>选择</td>
			</tr>
			<s:iterator value="vehicleGroupInfoList" status="st">
				<tr <s:if test="#st.even">bgcolor="#ECF5FF"</s:if>>
					<td style="padding-left: 0px; text-align: center"><s:property
							value="#st.index+1" /></td>
					<td><s:property value="name" /></td>
					<td><s:property value="owner" /></td>
					<td><s:property value="type" /></td>
					<td><s:property value="certificateCity" /></td>
					<td><s:property value="certificateNumber" /></td>
					<td><s:property value="contact" /></td>
					<td><s:property value="contactTelephoneNumber" /></td>
					<td><s:property value="area" /></td>
					<td><s:property value="parentGroupName" /></td>
					<td><input type="radio" class="radioStyle" name="selectedVehicleGroupName"
						value='<s:property value="name" />' /></td>
				</tr>
			</s:iterator>
			<tr>
				<td colspan="11" style="padding-left: 0px; text-align: center;"><input
					class="buttonStyle" type="button" value="添加"
					onclick="vehicleGroupAdd()" />&nbsp;<input class="buttonStyle"
					type="button" value="编辑" onclick="vehicleGroupEdit()" />&nbsp;<input
					class="buttonStyle" type="button" value="删除"
					onclick="vehicleGroupDelete()" /></td>
			</tr>
		</table>
	</s:form>


</body>
</html>