package cn.edu.ntu.action;

import java.io.InputStream;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;

import javax.servlet.ServletResponse;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.struts2.ServletActionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.opensymphony.xwork2.ActionContext;
import com.opensymphony.xwork2.ActionSupport;

import cn.edu.ntu.entity.common.MemberInfo;
import cn.edu.ntu.entity.common.UserInfo;
import cn.edu.ntu.service.common.interfaces.CommonService;
import cn.edu.ntu.service.common.interfaces.MemberInfoService;
import cn.edu.ntu.utils.others.CheckCodeImageUtil;
import cn.edu.ntu.utils.others.CheckCodeUtil;

/**
 *
 * 
 * <AUTHOR>
 * @date 2016��12��9�� ����9:22:06
 */
@Controller
@Scope("prototype")
public class LoginAction extends ActionSupport {
	
	private static final int MAX_AGE=60*60*24*30;

	private String userName;
	private String userPassword;
	private String checkCode;
	
	private UserInfo userInfo;
	private MemberInfo memberInfo;
	
	private InputStream inputStream;

	@Autowired
	private CommonService commonService;
	@Autowired
	private MemberInfoService memberInfoService;
	
  
	
	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getUserPassword() {
		return userPassword;
	}

	public void setUserPassword(String userPassword) {
		this.userPassword = userPassword;
	}

	public String getCheckCode() {
		return checkCode;
	}

	public void setCheckCode(String checkCode) {
		this.checkCode = checkCode;
	}
	public UserInfo getUserInfo() {
		return userInfo;
	}

	public void setUserInfo(UserInfo userInfo) {
		this.userInfo = userInfo;
	}

	public MemberInfo getMemberInfo() {
		return memberInfo;
	}

	public void setMemberInfo(MemberInfo memberInfo) {
		this.memberInfo = memberInfo;
	}

	public InputStream getInputStream() {
		return inputStream;
	}

	public void setInputStream(InputStream inputStream) {
		this.inputStream = inputStream;
	}

	public String login() throws Exception{

		if (commonService.validateCheckCode(checkCode)) {
			if ((userInfo = commonService.validateUserInfo(userName, userPassword)) != null) {
//				String accountType = commonService.getAccountType(userName, userPassword);
//				int roleCreateAllowed = commonService.getRoleCreateAllowed(userName);
//				int reloginAllowed = commonService.getReloginAllowed(userName);

//				ServletActionContext.getContext().getSession().put("accountName", userName);
//				ServletActionContext.getContext().getSession().put("accountType", accountType);
//				ServletActionContext.getContext().getSession().put("roleCreateAllowed",roleCreateAllowed);
				
				HttpServletResponse servletResponse=ServletActionContext.getResponse();
				
                Cookie accountNameCookie=new Cookie("accountName", userName);
//              Cookie accountTypeCookie=new Cookie("accountType", accountType);
//              Cookie roleCreateAllowedCookie=new Cookie("roleCreateAllowed", String.valueOf(roleCreateAllowed));
                accountNameCookie.setMaxAge(MAX_AGE);
//                accountTypeCookie.setMaxAge(MAX_AGE);
//               roleCreateAllowedCookie.setMaxAge(MAX_AGE);
                accountNameCookie.setPath("/808FrontProject/");
//                accountTypeCookie.setPath("/808FrontProject/");
//                roleCreateAllowedCookie.setPath("/808FrontProject/");
                servletResponse.addCookie(accountNameCookie);
//                servletResponse.addCookie(accountTypeCookie);
//                servletResponse.addCookie(roleCreateAllowedCookie);
				
                /**
				//���������ظ���¼
				if (reloginAllowed == 0) {
					Map appMap = ActionContext.getContext().getApplication();
					Map<String, HttpSession> userOnline = (Map<String, HttpSession>) appMap.get("userOnline");
					if (userOnline == null) {
						userOnline = new HashMap<String, HttpSession>();
						appMap.put("userOnline", userOnline);
					}
					Set<String> keySet = userOnline.keySet();
					for (String name : keySet) {
						if (name.equals(userName)) {
							addActionError("��ֹ�û��ظ���¼");
							return INPUT;
						}
					}
					HttpSession session = ServletActionContext.getRequest().getSession();
					userOnline.put(userName, session);
					
				}
				*/

				return SUCCESS;
			} 
			/**
			else if ((memberInfo = commonService.validateMemberInfo(userName, userPassword)) != null) {
				String accountType = commonService.getAccountType(userName, userPassword);
				
				ServletActionContext.getContext().getSession().put("accountName", userName);
				ServletActionContext.getContext().getSession().put("accountType", accountType);
				return SUCCESS;
			} 
			*/
			else {
				addActionError("用户名或密码错误");
				return INPUT;
			}
		} else {
			addActionError("验证码错误");
			return INPUT;
		}

	}

	public String loginOut() throws Exception {
		
		/**
		Map<String, Object> sessionMap = ServletActionContext.getContext().getSession();
		String userName = (String) sessionMap.get("accountName");
		
		int reloginAllowed = commonService.getReloginAllowed(userName);
		if (reloginAllowed == 0) {
			Map appMap = ActionContext.getContext().getApplication();
			Map<String, HttpSession> userOnline = (Map<String, HttpSession>) appMap.get("userOnline");
			Set<String> keySet = userOnline.keySet();
			for (Iterator it = keySet.iterator(); it.hasNext();) {
				String name = (String) it.next();
				if (name.equals(userName)) {
					HttpSession session = userOnline.get(userName);
					it.remove();
				}
			}

		}

		sessionMap.remove("accountName");
		sessionMap.remove("accountType");
		*/
		
		HttpServletResponse servletResponse=ServletActionContext.getResponse();
		
		Cookie disableUserNameCookie=new Cookie("accountName", "");
//		Cookie disableAccountTypeCookie=new Cookie("accountType", "");
		disableUserNameCookie.setMaxAge(0);
//		disableAccountTypeCookie.setMaxAge(0);
        disableUserNameCookie.setPath("/");
//      disableAccountTypeCookie.setPath("/");
        servletResponse.addCookie(disableUserNameCookie);
//      servletResponse.addCookie(disableAccountTypeCookie);

		return SUCCESS;
	}
	

	public String createCheckCodeImage() throws Exception{
		
		HttpServletResponse response=ServletActionContext.getResponse();
		
		response.setHeader("Pragma", "no-cache");
        response.setHeader("Cache-Control", "no-cache");
        response.setDateHeader("Expires", 0);
        
        String checkCode=CheckCodeUtil.generateCheckCode();
        ServletActionContext.getContext().getSession().put("checkCode", checkCode);
        
        inputStream=CheckCodeImageUtil.getCheckCodeImageStream(checkCode);
        
        return SUCCESS;
	}

}
