<?xml version="1.0" encoding="UTF-8"?>
<web-app id="WebApp_9" version="2.4" xmlns="http://java.sun.com/xml/ns/j2ee"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://java.sun.com/xml/ns/j2ee http://java.sun.com/xml/ns/j2ee/web-app_2_4.xsd">
    
    <display-name>808FrontProject</display-name>
    <description>808 Front Project - 车辆监控系统</description>
    
    <!-- Quartz调度器初始化 (可选) -->
    <!-- 
    <servlet>  
      <servlet-name>QuartzInitializer</servlet-name>  
      <servlet-class>org.quartz.ee.servlet.QuartzInitializerServlet</servlet-class>  
      <init-param>  
          <param-name>config-file</param-name>  
          <param-value>/quartz.properties</param-value>  
      </init-param>  
      <init-param>    
          <param-name>shutdown-on-unload</param-name>    
          <param-value>true</param-value>    
      </init-param>  
      <init-param>     
          <param-name>start-scheduler-on-load</param-name>     
          <param-value>true</param-value>     
      </init-param>    
      <load-on-startup>1</load-on-startup>  
    </servlet>  
    -->
    
    <!-- Struts2过滤器 -->
    <filter>
        <filter-name>struts2</filter-name>
        <filter-class>org.apache.struts2.dispatcher.ng.filter.StrutsPrepareAndExecuteFilter</filter-class>
    </filter>

    <filter-mapping>
        <filter-name>struts2</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <!-- Spring上下文配置 -->
    <context-param>
        <param-name>contextConfigLocation</param-name>
        <param-value>classpath:spring-*.xml</param-value>
    </context-param>
    
    <!-- Spring上下文监听器 -->
    <listener>
        <listener-class>org.springframework.web.context.ContextLoaderListener</listener-class>
    </listener>
    
    <!-- 欢迎页面 -->
    <welcome-file-list>
        <welcome-file>page/index.jsp</welcome-file>
        <welcome-file>page/login.jsp</welcome-file>
    </welcome-file-list>
    
    <!-- 错误页面配置 -->
    <error-page>
        <error-code>404</error-code>
        <location>/page/error.jsp</location>
    </error-page>
    
    <error-page>
        <error-code>500</error-code>
        <location>/page/error.jsp</location>
    </error-page>
    
    <!-- 会话超时配置 (30分钟) -->
    <session-config>
        <session-timeout>30</session-timeout>
    </session-config>
    
</web-app>
