<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@taglib prefix="s" uri="/struts-tags"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">

<title>位置查询</title>
<meta http-equiv="pragma" content="no-cache">
<meta http-equiv="cache-control" content="no-cache">
<meta http-equiv="expires" content="0">
<meta http-equiv="keywords" content="太平洋,北斗,位置服务,云服务,车辆定位,车辆查询,车辆导航">
<meta http-equiv="description" content="太平洋北斗位置云">


<link rel="stylesheet" type="text/css"
	href="/808FrontProject/css/searchres.css">

<script type="text/javascript" src="/808FrontProject/js/lib.jquery.js"></script>
<script type="text/javascript" src="/808FrontProject/js/lib.json2.js"></script>


<script type="text/javascript" src="/808FrontProject/js/frame.js"></script>
<script type="text/javascript" src="/808FrontProject/js/tools.js"></script>
<script type="text/javascript" src="/808FrontProject/js/baidumap.js"></script>
<script type="text/javascript" src="/808FrontProject/js/style.js"></script>
<script type="text/javascript" src="/808FrontProject/js/endControl.js"></script>
<script type="text/javascript" src="/808FrontProject/js/var.js"></script>
<script type="text/javascript" src="/808FrontProject/js/nav.js"></script>

<script type="text/javascript"
	src="http://api.map.baidu.com/api?v=2.0&ak=6ukSP6o4ff8u9SSnlVEKmiZC"></script>
<script type="text/javascript"
	src="http://api.map.baidu.com/library/DrawingManager/1.4/src/DrawingManager.js"></script>
<script type="text/javascript"
	src="http://api.map.baidu.com/library/MarkerManager/1.2/src/MarkerManager.js"></script>


<style type="text/css">
body {
	font-family: 宋体;
}

#mapDiv {
	position: absolute;
	top: 0px;
	right: 0px;
	height: 0px;
	width: 0px;
	background-color: #fff;
	z-index: 0;
	font-size: 14px;
}

#infoOpenDiv {
	position: absolute;
	bottom: 0px;
	right: 0px;
	height: 0px;
	width: 0px;
	background-color: #fff;
	z-index: 1;
	font-size: 14px;
	padding: 2px;
	border-width: 1px 0px 0px 1px;
	border-style: solid none none solid;
	border-color: #ccc;
}

#infoCloseDiv, #infoOpenBtnDiv {
	position: absolute;
	bottom: 0px;
	right: 0px;
	height: 15px;
	width: 15px;
	background-color: #eee;
	z-index: 1;
	font-size: 13px;
	padding: 2px;
	border-width: 1px 0px 0px 1px;
	border-style: solid none none solid;
	border-color: #ccc;
}

input {
	font-size: 14px;
}

.commonFontSize {
	font-size: 14px;
}
</style>
<script type="text/javascript">
	var mapPosQuery;
	
	

	$(window).load(function() {

		InitInfoTable();
		infoPanelOpen()

		showMapPanel();

		mapPosQuery = new BaiduMap("mapDiv");
		mapPosQuery.init();

		var p = parseParametersFromURL("p");
		if (p != null) {
			p = decodeUTF8(p);
		}
		
		positionEnquire(p);

	});

	$(window).resize(function() {
		resizeInfoPanel();
		showMapPanel();
	});
</script>
</head>

<body>

	<div id="mapDiv" style="width: 100%; height: 100%"></div>
	<!-- 地图右下脚信息栏 [S]-->
	<div id="infoOpenDiv" style="overflow: auto"></div>
	<div id="infoOpenBtnDiv" onmouseover="styleInfoMOver(this)"
		onmouseout="styleInfoMOut(this)" onclick="infoPanelClose()"
		title="隐藏信息栏">
		<img src="/808FrontProject/image/close.png" style="margin-top: 70px;" />
	</div>
	<div id="infoCloseDiv" onmouseover="styleInfoMOver(this)"
		onmouseout="styleInfoMOut(this)" onclick="infoPanelOpen()"
		title="展开信息栏">
		<img src="/808FrontProject/image/open.png" />
	</div>
	<!-- 地图右下脚信息栏 [E]-->

</body>

</html>
