/**
 *                                                        ____   _____
 *  Dynarch Calendar -- JSCal2, version 1.9               \  /_  /   /
 *  Built at 2013/03/04 09:35 GMT                          \  / /   /
 *                                                          \/ /_  /
 *  (c) Dynarch.com 2009-2012                                \  / /
 *  All rights reserved.                                       / /
 *  Visit www.dynarch.com/projects/calendar for details        \/
 *
 */
Calendar=function(){function t(e){var n,a,s
e=e||{},this.args=e=E(e,{animation:!ce,cont:null,bottomBar:!0,date:!0,fdow:_("fdow"),min:null,max:null,reverseWheel:!1,selection:[],selectionType:t.SEL_SINGLE,weekNumbers:!1,align:"Bl/ / /T/r",inputField:null,trigger:null,dateFormat:"%Y-%m-%d",multiCtrl:!0,fixed:!1,opacity:le?1:3,titleFormat:"%b %Y",showTime:!1,timePos:"right",time:!0,minuteStep:5,noScroll:!1,disabled:se,checkRange:!1,dateInfo:se,onChange:se,onSelect:se,onTimeChange:se,onFocus:se,onBlur:se,onClose:se}),this.handlers={},n=this,a=new Date,e.min=k(e.min),e.max=k(e.max),e.date===!0&&(e.date=a),e.time===!0&&(e.time=a.getHours()*100+Math.floor(a.getMinutes()/e.minuteStep)*e.minuteStep),this.date=k(e.date),this.time=e.time,this.fdow=e.fdow,q("onChange onSelect onTimeChange onFocus onBlur onClose".split(/\s+/),function(t){var a=e[t]
a instanceof Array||(a=[a]),n.handlers[t]=a}),this.selection=new t.Selection(e.selection,e.selectionType,C,this),s=l(this),e.cont&&J(e.cont).appendChild(s),e.trigger&&this.manageFields(e.trigger,e.inputField,e.dateFormat)}function e(t){var e,n=["<table",Q,"><tr>"],a=0
for(t.args.weekNumbers&&n.push("<td><div class='DynarchCalendar-weekNumber'>",_("wk"),"</div></td>");7>a;)e=(a++ +t.fdow)%7,n.push("<td><div",_("weekend").indexOf(e)<0?">":" class='DynarchCalendar-weekend'>",_("sdn")[e],"</div></td>")
return n.push("</tr></table>"),n.join("")}function n(t,e,n){var a,s,i,r,o,l,c,h,u,d,f,y,m,p,g,v,D
for(e=e||t.date,n=n||t.fdow,e=new Date(e.getFullYear(),e.getMonth(),e.getDate(),12,0,0,0),a=e.getMonth(),s=[],i=0,r=t.args.weekNumbers,e.setDate(1),o=(e.getDay()-n)%7,0>o&&(o+=7),e.setDate(0-o),e.setDate(e.getDate()+1),l=new Date,c=l.getDate(),h=l.getMonth(),u=l.getFullYear(),s[i++]="<table class='DynarchCalendar-bodyTable'"+Q+">",d=0;6>d;++d){for(s[i++]="<tr class='DynarchCalendar-week",0==d&&(s[i++]=" DynarchCalendar-first-row"),5==d&&(s[i++]=" DynarchCalendar-last-row"),s[i++]="'>",r&&(s[i++]="<td class='DynarchCalendar-first-col'><div class='DynarchCalendar-weekNumber'>"+T(e)+"</div></td>"),f=0;7>f;++f)y=e.getDate(),m=e.getMonth(),p=e.getFullYear(),g=1e4*p+100*(m+1)+y,v=t.selection.isSelected(g),D=t.isDisabled(e),s[i++]="<td class='",0!=f||r||(s[i++]=" DynarchCalendar-first-col"),0==f&&0==d&&(t._firstDateVisible=g),6==f&&(s[i++]=" DynarchCalendar-last-col",5==d&&(t._lastDateVisible=g)),v&&(s[i++]=" DynarchCalendar-td-selected"),s[i++]="'><div dyc-type='date' unselectable='on' dyc-date='"+g+"' ",D&&(s[i++]="disabled='1' "),s[i++]="class='DynarchCalendar-day",_("weekend").indexOf(e.getDay())<0||(s[i++]=" DynarchCalendar-weekend"),m!=a&&(s[i++]=" DynarchCalendar-day-othermonth"),y==c&&m==h&&p==u&&(s[i++]=" DynarchCalendar-day-today"),D&&(s[i++]=" DynarchCalendar-day-disabled"),v&&(s[i++]=" DynarchCalendar-day-selected"),D=t.args.dateInfo(e),D&&D.klass&&(s[i++]=" "+D.klass),s[i++]="'>"+y+"</div></td>",e=new Date(p,m,y+1,12,0,0,0)
s[i++]="</tr>"}return s[i++]="</table>",s.join("")}function a(t){var n=["<table class='DynarchCalendar-topCont'",Q,"><tr><td>","<div class='DynarchCalendar'>",le?"<a class='DynarchCalendar-focusLink' href='#'></a>":"<button class='DynarchCalendar-focusLink'></button>","<div class='DynarchCalendar-topBar'>","<div dyc-type='nav' dyc-btn='-Y' dyc-cls='hover-navBtn,pressed-navBtn' ","class='DynarchCalendar-navBtn DynarchCalendar-prevYear'><div></div></div>","<div dyc-type='nav' dyc-btn='+Y' dyc-cls='hover-navBtn,pressed-navBtn' ","class='DynarchCalendar-navBtn DynarchCalendar-nextYear'><div></div></div>","<div dyc-type='nav' dyc-btn='-M' dyc-cls='hover-navBtn,pressed-navBtn' ","class='DynarchCalendar-navBtn DynarchCalendar-prevMonth'><div></div></div>","<div dyc-type='nav' dyc-btn='+M' dyc-cls='hover-navBtn,pressed-navBtn' ","class='DynarchCalendar-navBtn DynarchCalendar-nextMonth'><div></div></div>","<table class='DynarchCalendar-titleCont'",Q,"><tr><td>","<div dyc-type='title' dyc-btn='menu' dyc-cls='hover-title,pressed-title' class='DynarchCalendar-title'>",s(t),"</div></td></tr></table>","<div class='DynarchCalendar-dayNames'>",e(t),"</div>","</div>","<div class='DynarchCalendar-body'></div>"]
return(t.args.bottomBar||t.args.showTime)&&n.push("<div class='DynarchCalendar-bottomBar'>",o(t),"</div>"),n.push("<div class='DynarchCalendar-menu' style='display: none'>",i(t),"</div>","<div class='DynarchCalendar-tooltip'></div>","</div>","</td></tr></table>"),n.join("")}function s(t){return"<div unselectable='on'>"+S(t.date,t.args.titleFormat)+"</div>"}function i(t){for(var e,n=["<table height='100%'",Q,"><tr><td>","<table style='margin-top: 1.5em'",Q,">","<tr><td colspan='3'><input dyc-btn='year' class='DynarchCalendar-menu-year' size='6' value='",t.date.getFullYear(),"' /></td></tr>","<tr><td><div dyc-type='menubtn' dyc-cls='hover-navBtn,pressed-navBtn' dyc-btn='today'>",_("goToday"),"</div></td></tr>","</table>","<p class='DynarchCalendar-menu-sep'>&nbsp;</p>","<table class='DynarchCalendar-menu-mtable'",Q,">"],a=_("smn"),s=0,i=n.length;12>s;){for(n[i++]="<tr>",e=4;--e>0;)n[i++]="<td><div dyc-type='menubtn' dyc-cls='hover-navBtn,pressed-navBtn' dyc-btn='m"+s+"' class='DynarchCalendar-menu-month'>"+a[s++]+"</div></td>"
n[i++]="</tr>"}return n[i++]="</table></td></tr></table>",n.join("")}function r(t,e){e.push("<table class='DynarchCalendar-time'"+Q+"><tr>","<td rowspan='2'><div dyc-type='time-hour' dyc-cls='hover-time,pressed-time' class='DynarchCalendar-time-hour'></div></td>","<td dyc-type='time-hour+' dyc-cls='hover-time,pressed-time' class='DynarchCalendar-time-up'></td>","<td rowspan='2' class='DynarchCalendar-time-sep'></td>","<td rowspan='2'><div dyc-type='time-min' dyc-cls='hover-time,pressed-time' class='DynarchCalendar-time-minute'></div></td>","<td dyc-type='time-min+' dyc-cls='hover-time,pressed-time' class='DynarchCalendar-time-up'></td>"),t.args.showTime==12&&e.push("<td rowspan='2' class='DynarchCalendar-time-sep'></td>","<td rowspan='2'><div class='DynarchCalendar-time-am' dyc-type='time-am' dyc-cls='hover-time,pressed-time'></div></td>"),e.push("</tr><tr>","<td dyc-type='time-hour-' dyc-cls='hover-time,pressed-time' class='DynarchCalendar-time-down'></td>","<td dyc-type='time-min-' dyc-cls='hover-time,pressed-time' class='DynarchCalendar-time-down'></td>","</tr></table>")}function o(t){function e(){a.showTime&&(n.push("<td>"),r(t,n),n.push("</td>"))}var n=[],a=t.args
return n.push("<table",Q," style='width:100%'><tr>"),a.timePos=="left"&&e(),a.bottomBar&&(n.push("<td>"),n.push("<table",Q,"><tr><td>","<div dyc-btn='today' dyc-cls='hover-bottomBar-today,pressed-bottomBar-today' dyc-type='bottomBar-today' ","class='DynarchCalendar-bottomBar-today'>",_("today"),"</div>","</td></tr></table>"),n.push("</td>")),a.timePos=="right"&&e(),n.push("</tr></table>"),n.join("")}function l(t){var e=V("div"),n=t.els={},s={mousedown:O(m,t,!0),mouseup:O(m,t,!1),mouseover:O(D,t,!0),mouseout:O(D,t,!1),keypress:O(w,t)}
return t.args.noScroll||(s[he?"DOMMouseScroll":"mousewheel"]=O(b,t)),le&&(s.dblclick=s.mousedown,s.keydown=s.keypress),e.innerHTML=a(t),W(e.firstChild,function(t){var e=Z[t.className]
e&&(n[e]=t),le&&t.setAttribute("unselectable","on")}),B(n.main,s),B([n.focusLink,n.yearInput],t._focusEvents={focus:O(c,t),blur:O(u,t)}),t.moveTo(t.date,!1),t.setTime(null,!0),n.topCont}function c(){this._bluringTimeout&&clearTimeout(this._bluringTimeout),this.focused=!0,P(this.els.main,"DynarchCalendar-focused"),this.callHooks("onFocus",this)}function h(){this.focused=!1,Y(this.els.main,"DynarchCalendar-focused"),this._menuVisible&&y(this,!1),this.args.cont||this.hide(),this.callHooks("onBlur",this)}function u(){this._bluringTimeout=setTimeout(O(h,this),50)}function d(t){switch(t){case"time-hour+":this.setHours(this.getHours()+1)
break
case"time-hour-":this.setHours(this.getHours()-1)
break
case"time-min+":this.setMinutes(this.getMinutes()+this.args.minuteStep)
break
case"time-min-":this.setMinutes(this.getMinutes()-this.args.minuteStep)
break
default:return}}function f(t,e,n){this._bodyAnim&&this._bodyAnim.stop()
var a
if(0!=e)switch(a=new Date(t.date),a.setDate(1),e){case"-Y":case-2:a.setFullYear(a.getFullYear()-1)
break
case"+Y":case 2:a.setFullYear(a.getFullYear()+1)
break
case"-M":case-1:a.setMonth(a.getMonth()-1)
break
case"+M":case 1:a.setMonth(a.getMonth()+1)}else a=new Date
return t.moveTo(a,!n)}function y(t,e){var n,a
t._menuVisible=e,R(e,t.els.title,"DynarchCalendar-pressed-title"),n=t.els.menu,ce&&(n.style.height=t.els.main.offsetHeight+"px"),t.args.animation?(t._menuAnim&&t._menuAnim.stop(),a=t.els.main.offsetHeight,ce&&(n.style.width=t.els.topBar.offsetWidth+"px"),e&&(n.firstChild.style.marginTop=-a+"px",t.args.opacity>0&&$(n,0),j(n,!0)),t._menuAnim=K({onUpdate:function(s,i){n.firstChild.style.marginTop=i(ae.accel_b(s),-a,0,!e)+"px",t.args.opacity>0&&$(n,i(ae.accel_b(s),0,.85,!e))},onStop:function(){t.args.opacity>0&&$(n,.85),n.firstChild.style.marginTop="",t._menuAnim=null,e||(j(n,!1),t.focused&&t.focus())}})):(j(n,e),t.focused&&t.focus())}function m(e,n){var a,s,i,r,o,l,c,h,u
n=n||window.event,a=g(n),a&&!a.getAttribute("disabled")&&(i=a.getAttribute("dyc-btn"),r=a.getAttribute("dyc-type"),o=a.getAttribute("dyc-date"),l=this.selection,c={mouseover:N,mousemove:N,mouseup:function(){var t=a.getAttribute("dyc-cls")
t&&Y(a,v(t,1)),clearTimeout(s),F(document,c,!0),c=null}},e?(setTimeout(O(this.focus,this),1),h=a.getAttribute("dyc-cls"),h&&P(a,v(h,1)),"menu"==i?this.toggleMenu():a&&/^[+-][MY]$/.test(i)?f(this,i)?(u=O(function(){f(this,i,!0)?s=setTimeout(u,40):(c.mouseup(),f(this,i))},this),s=setTimeout(u,350),B(document,c,!0)):c.mouseup():"year"==i?(this.els.yearInput.focus(),this.els.yearInput.select()):"time-am"==r?B(document,c,!0):/^time/.test(r)?(u=O(function(t){d.call(this,t),s=setTimeout(u,100)},this,r),d.call(this,r),s=setTimeout(u,350),B(document,c,!0)):(o&&l.type&&(l.type==t.SEL_MULTIPLE?n.shiftKey&&this._selRangeStart?l.selectRange(this._selRangeStart,o):(n.ctrlKey||l.isSelected(o)||!this.args.multiCtrl||l.clear(!0),l.set(o,!0),this._selRangeStart=o):(l.set(o),this.moveTo(A(o),2)),a=this._getDateDiv(o),D.call(this,!0,{target:a})),B(document,c,!0)),le&&c&&/dbl/i.test(n.type)&&c.mouseup(),this.args.fixed||!/^(DynarchCalendar-(topBar|bottomBar|weekend|weekNumber|menu(-sep)?))?$/.test(a.className)||this.args.cont||(c.mousemove=O(p,this),this._mouseDiff=z(n,G(this.els.topCont)),B(document,c,!0))):"today"==i?(this._menuVisible||l.type!=t.SEL_SINGLE||l.set(new Date),this.moveTo(new Date,!0),y(this,!1)):/^m([0-9]+)/.test(i)?(o=new Date(this.date),o.setDate(1),o.setMonth(RegExp.$1),o.setFullYear(this._getInputYear()),this.moveTo(o,!0),y(this,!1)):"time-am"==r&&this.setHours(this.getHours()+12),le||N(n))}function p(t){t=t||window.event
var e=this.els.topCont.style,n=z(t,this._mouseDiff)
e.left=n.x+"px",e.top=n.y+"px"}function g(t){for(var e=t.target||t.srcElement,n=e;e&&e.getAttribute&&!e.getAttribute("dyc-type");)e=e.parentNode
return e.getAttribute&&e||n}function v(t,e){return"DynarchCalendar-"+t.split(/,/)[e]}function D(t,e){var n,a,s
e=e||window.event,n=g(e),n&&(a=n.getAttribute("dyc-type"),a&&!n.getAttribute("disabled")&&(t&&this._bodyAnim&&"date"==a||(s=n.getAttribute("dyc-cls"),s=s?v(s,0):"DynarchCalendar-hover-"+a,("date"!=a||this.selection.type)&&R(t,n,s),"date"==a&&(R(t,n.parentNode.parentNode,"DynarchCalendar-hover-week"),this._showTooltip(n.getAttribute("dyc-date"))),/^time-hour/.test(a)&&R(t,this.els.timeHour,"DynarchCalendar-hover-time"),/^time-min/.test(a)&&R(t,this.els.timeMinute,"DynarchCalendar-hover-time"),Y(this._getDateDiv(this._lastHoverDate),"DynarchCalendar-hover-date"),this._lastHoverDate=null))),t||this._showTooltip()}function b(t){var e,n,a,s
if(t=t||window.event,e=g(t))if(n=e.getAttribute("dyc-btn"),a=e.getAttribute("dyc-type"),s=t.wheelDelta?t.wheelDelta/120:-t.detail/3,s=0>s?-1:s>0?1:0,this.args.reverseWheel&&(s=-s),/^(time-(hour|min))/.test(a)){switch(RegExp.$1){case"time-hour":this.setHours(this.getHours()+s)
break
case"time-min":this.setMinutes(this.getMinutes()+this.args.minuteStep*s)}N(t)}else/Y/i.test(n)&&(s*=2),f(this,-s),N(t)}function C(){var t,e,n
this.refresh(),t=this.inputField,e=this.selection,t&&(n=e.print(this.dateFormat),/input|textarea/i.test(t.tagName)?t.value=n:t.innerHTML=n),this.callHooks("onSelect",this,e)}function w(e){var n,a,s,i,r,o,l,c,h,u,d,m,p
if(!this._menuAnim){if(e=e||window.event,n=e.target||e.srcElement,a=n.getAttribute("dyc-btn"),s=e.keyCode,i=e.charCode||s,r=ee[s],"year"==a&&13==s)return o=new Date(this.date),o.setDate(1),o.setFullYear(this._getInputYear()),this.moveTo(o,!0),y(this,!1),N(e)
if(this._menuVisible){if(27==s)return y(this,!1),N(e)}else{if(e.ctrlKey||(r=null),null!=r||e.ctrlKey||(r=ne[s]),36==s&&(r=0),null!=r)return f(this,r),N(e)
if(i=String.fromCharCode(i).toLowerCase(),l=this.els.yearInput,c=this.selection," "==i)return y(this,!0),this.focus(),l.focus(),l.select(),N(e)
if(i>="0"&&"9">=i)return y(this,!0),this.focus(),l.value=i,l.focus(),N(e)
for(u=_("mn"),d=e.shiftKey?-1:this.date.getMonth(),m=0;++m<12;)if(h=u[(d+m)%12].toLowerCase(),h.indexOf(i)==0)return o=new Date(this.date),o.setDate(1),o.setMonth((d+m)%12),this.moveTo(o,!0),N(e)
if(s>=37&&40>=s){if(o=this._lastHoverDate,o||c.isEmpty()||(o=39>s?c.getFirstDate():c.getLastDate(),(o<this._firstDateVisible||o>this._lastDateVisible)&&(o=null)),o){for(p=o,o=A(o),d=100;d-->0;){switch(s){case 37:o.setDate(o.getDate()-1)
break
case 38:o.setDate(o.getDate()-7)
break
case 39:o.setDate(o.getDate()+1)
break
case 40:o.setDate(o.getDate()+7)}if(!this.isDisabled(o))break}o=L(o),(o<this._firstDateVisible||o>this._lastDateVisible)&&this.moveTo(o)}else o=39>s?this._lastDateVisible:this._firstDateVisible
return Y(this._getDateDiv(p),P(this._getDateDiv(o),"DynarchCalendar-hover-date")),this._lastHoverDate=o,N(e)}if(13==s&&this._lastHoverDate)return c.type==t.SEL_MULTIPLE&&(e.shiftKey||e.ctrlKey)?(e.shiftKey&&this._selRangeStart&&(c.clear(!0),c.selectRange(this._selRangeStart,this._lastHoverDate)),e.ctrlKey&&c.set(this._selRangeStart=this._lastHoverDate,!0)):c.reset(this._selRangeStart=this._lastHoverDate),N(e)
27!=s||this.args.cont||this.hide()}}}function M(t,e){return t.replace(/\$\{([^:\}]+)(:[^\}]+)?\}/g,function(t,n,a){var s,i=e[n]
return a&&(s=a.substr(1).split(/\s*\|\s*/),i=(i<s.length?s[i]:s[s.length-1]).replace(/##?/g,function(t){return t.length==2?"#":i})),i})}function _(t,e){var n=de.__.data[t]
return e&&"string"==typeof n&&(n=M(n,e)),n}function T(t){var e,n
return t=new Date(t.getFullYear(),t.getMonth(),t.getDate(),12,0,0),e=t.getDay(),t.setDate(t.getDate()-(e+6)%7+3),n=t.valueOf(),t.setMonth(0),t.setDate(4),Math.round((n-t.valueOf())/6048e5)+1}function x(t){var e,n
return t=new Date(t.getFullYear(),t.getMonth(),t.getDate(),12,0,0),e=new Date(t.getFullYear(),0,1,12,0,0),n=t-e,Math.floor(n/864e5)}function L(t){return t instanceof Date?1e4*t.getFullYear()+100*(t.getMonth()+1)+t.getDate():"string"==typeof t?parseInt(t,10):t}function A(t,e,n,a,s){var i,r
return t instanceof Date||(t=parseInt(t,10),i=Math.floor(t/1e4),t%=1e4,r=Math.floor(t/100),t%=100,t=new Date(i,r-1,t,null==e?12:e,null==n?0:n,null==a?0:a,null==s?0:s)),t}function H(t,e,n){var a=t.getFullYear(),s=t.getMonth(),i=t.getDate(),r=e.getFullYear(),o=e.getMonth(),l=e.getDate()
return r>a?-3:a>r?3:o>s?-2:s>o?2:n?0:l>i?-1:i>l?1:0}function S(t,e){var n,a=t.getMonth(),s=t.getDate(),i=t.getFullYear(),r=T(t),o=t.getDay(),l=t.getHours(),c=l>=12,h=c?l-12:l,u=x(t),d=t.getMinutes(),f=t.getSeconds(),y=/%./g
return 0===h&&(h=12),n={"%a":_("sdn")[o],"%A":_("dn")[o],"%b":_("smn")[a],"%B":_("mn")[a],"%C":1+Math.floor(i/100),"%d":10>s?"0"+s:s,"%e":s,"%H":10>l?"0"+l:l,"%I":10>h?"0"+h:h,"%j":10>u?"00"+u:100>u?"0"+u:u,"%k":l,"%l":h,"%m":9>a?"0"+(1+a):1+a,"%o":1+a,"%M":10>d?"0"+d:d,"%n":"\n","%p":c?"PM":"AM","%P":c?"pm":"am","%s":Math.floor(t.getTime()/1e3),"%S":10>f?"0"+f:f,"%t":"	","%U":10>r?"0"+r:r,"%W":10>r?"0"+r:r,"%V":10>r?"0"+r:r,"%u":o+1,"%w":o,"%y":(""+i).substr(2,2),"%Y":i,"%%":"%"},e.replace(y,function(t){return n.hasOwnProperty(t)?n[t]:t})}function k(t){if(t){if("number"==typeof t)return A(t)
if(!(t instanceof Date)){var e=t.split(/-/)
return new Date(parseInt(e[0],10),parseInt(e[1],10)-1,parseInt(e[2],10),12,0,0,0)}}return t}function I(t){function e(e){for(var n=e.length;--n>=0;)if(e[n].toLowerCase().indexOf(t)==0)return n+1}return/\S/.test(t)?(t=t.toLowerCase(),e(_("smn"))||e(_("mn"))):void 0}function E(t,e,n,a){a={}
for(n in e)e.hasOwnProperty(n)&&(a[n]=e[n])
for(n in t)t.hasOwnProperty(n)&&(a[n]=t[n])
return a}function B(t,e,n,a){var s
if(t instanceof Array)for(s=t.length;--s>=0;)B(t[s],e,n,a)
else if("object"==typeof e)for(s in e)e.hasOwnProperty(s)&&B(t,s,e[s],n)
else t.addEventListener?t.addEventListener(e,n,le?!0:!!a):t.attachEvent?t.attachEvent("on"+e,n):t["on"+e]=n}function F(t,e,n,a){var s
if(t instanceof Array)for(s=t.length;--s>=0;)F(t[s],e,n)
else if("object"==typeof e)for(s in e)e.hasOwnProperty(s)&&F(t,s,e[s],n)
else t.removeEventListener?t.removeEventListener(e,n,le?!0:!!a):t.detachEvent?t.detachEvent("on"+e,n):t["on"+e]=null}function N(t){return t=t||window.event,le?(t.cancelBubble=!0,t.returnValue=!1):(t.preventDefault(),t.stopPropagation()),!1}function Y(t,e,n){if(t){var a,s=t.className.replace(/^\s+|\s+$/,"").split(/\x20/),i=[]
for(a=s.length;a>0;)s[--a]!=e&&i.push(s[a])
n&&i.push(n),t.className=i.join(" ")}return n}function P(t,e){return Y(t,e,e)}function R(t,e,n){if(e instanceof Array)for(var a=e.length;--a>=0;)R(t,e[a],n)
else Y(e,n,t?n:null)
return t}function V(t,e,n){var a=null
return a=document.createElementNS?document.createElementNS("http://www.w3.org/1999/xhtml",t):document.createElement(t),e&&(a.className=e),n&&n.appendChild(a),a}function U(t,e){null==e&&(e=0)
var n,a,s
try{n=Array.prototype.slice.call(t,e)}catch(i){for(n=Array(t.length-e),a=e,s=0;a<t.length;++a,++s)n[s]=t[a]}return n}function O(t,e){var n=U(arguments,2)
return void 0==e?function(){return t.apply(this,n.concat(U(arguments)))}:function(){return t.apply(e,n.concat(U(arguments)))}}function W(t,e){if(!e(t))for(var n=t.firstChild;n;n=n.nextSibling)n.nodeType==1&&W(n,e)}function K(t,e,n){function a(t,e,n,a){return a?n+t*(e-n):e+t*(n-e)}function s(){e&&i(),n=0,e=setInterval(r,1e3/t.fps)}function i(){e&&(clearInterval(e),e=null),t.onStop(n/t.len,a)}function r(){var e=t.len
t.onUpdate(n/e,a),n==e&&i(),++n}return t=E(t,{fps:50,len:15,onUpdate:se,onStop:se}),le&&(t.len=Math.round(t.len/2)),s(),{start:s,stop:i,update:r,args:t,map:a}}function $(t,e){return""===e?le?t.style.filter="":t.style.opacity="":null!=e?le?t.style.filter="alpha(opacity="+100*e+")":t.style.opacity=e:le?/alpha\(opacity=([0-9.])+\)/.test(t.style.opacity)&&(e=parseFloat(RegExp.$1)/100):e=parseFloat(t.style.opacity),e}function j(t,e){var n=t.style
return null!=e&&(n.display=e?"":"none"),n.display!="none"}function z(t,e){var n=le?t.clientX+document.body.scrollLeft:t.pageX,a=le?t.clientY+document.body.scrollTop:t.pageY
return e&&(n-=e.x,a-=e.y),{x:n,y:a}}function G(t){var e,n,a
if(t.getBoundingClientRect)return e=t.getBoundingClientRect(),{x:e.left-document.documentElement.clientLeft+document.body.scrollLeft,y:e.top-document.documentElement.clientTop+document.body.scrollTop}
n=0,a=0
do n+=t.offsetLeft-t.scrollLeft,a+=t.offsetTop-t.scrollTop
while(t=t.offsetParent)
return{x:n,x:a}}function X(){var t=document.documentElement,e=document.body
return{x:t.scrollLeft||e.scrollLeft,y:t.scrollTop||e.scrollTop,w:t.clientWidth||window.innerWidth||e.clientWidth,h:t.clientHeight||window.innerHeight||e.clientHeight}}function q(t,e,n){for(n=0;n<t.length;++n)e(t[n])}function J(t){return"string"==typeof t&&(t=document.getElementById(t)),t}var Q,Z,te,ee,ne,ae,se,ie=navigator.userAgent,re=/opera/i.test(ie),oe=/Konqueror|Safari|KHTML/i.test(ie),le=/msie/i.test(ie)&&!re&&!/mac_powerpc/i.test(ie),ce=le&&/msie 6/i.test(ie),he=/gecko/i.test(ie)&&!oe&&!re&&!le,ue=t.prototype,de=t.I18N={}
return t.SEL_NONE=0,t.SEL_SINGLE=1,t.SEL_MULTIPLE=2,t.SEL_WEEK=3,t.dateToInt=L,t.intToDate=A,t.printDate=S,t.formatString=M,t.i18n=_,t.LANG=function(t,e,n){de.__=de[t]={name:e,data:n}},t.setup=function(e){return new t(e)},ue.moveTo=function(t,e){var a,s,i,r,o,l,c,h,u,d,f,y,m,p,g,v,D=this
return t=k(t),s=H(t,D.date,!0),i=D.args,r=i.min&&H(t,i.min),o=i.max&&H(t,i.max),i.animation||(e=!1),R(null!=r&&1>=r,[D.els.navPrevMonth,D.els.navPrevYear],"DynarchCalendar-navDisabled"),R(null!=o&&o>=-1,[D.els.navNextMonth,D.els.navNextYear],"DynarchCalendar-navDisabled"),-1>r&&(t=i.min,a=1,s=0),o>1&&(t=i.max,a=2,s=0),D.date=t,D.refresh(!!e),D.callHooks("onChange",D,t,e),!e||0==s&&2==e||(D._bodyAnim&&D._bodyAnim.stop(),l=D.els.body,c=V("div","DynarchCalendar-animBody-"+te[s],l),h=l.firstChild,$(h)||.7,u=a?ae.brakes:0==s?ae.shake:ae.accel_ab2,d=s*s>4,f=d?h.offsetTop:h.offsetLeft,y=c.style,m=d?l.offsetHeight:l.offsetWidth,0>s?m+=f:s>0?m=f-m:(m=Math.round(m/7),2==a&&(m=-m)),a||0==s||(p=c.cloneNode(!0),g=p.style,v=2*m,p.appendChild(h.cloneNode(!0)),g[d?"marginTop":"marginLeft"]=m+"px",l.appendChild(p)),h.style.visibility="hidden",c.innerHTML=n(D),D._bodyAnim=K({onUpdate:function(t,e){var n,i=u(t)
p&&(n=e(i,m,v)+"px"),a?y[d?"marginTop":"marginLeft"]=e(i,m,0)+"px":((d||0==s)&&(y.marginTop=e(0==s?u(t*t):i,0,m)+"px",0!=s&&(g.marginTop=n)),d&&0!=s||(y.marginLeft=e(i,0,m)+"px",0!=s&&(g.marginLeft=n))),D.args.opacity>2&&p&&($(p,1-i),$(c,i))},onStop:function(){l.innerHTML=n(D,t),D._bodyAnim=null}})),D._lastHoverDate=null,r>=-1&&1>=o},ue.isDisabled=function(t){var e=this.args
return e.min&&H(t,e.min)<0||e.max&&H(t,e.max)>0||e.disabled(t)},ue.toggleMenu=function(){y(this,!this._menuVisible)},ue.refresh=function(t){var e=this.els
t||(e.body.innerHTML=n(this)),e.title.innerHTML=s(this),e.yearInput.value=this.date.getFullYear()},ue.redraw=function(){var t=this,n=t.els
t.refresh(),n.dayNames.innerHTML=e(t),n.menu.innerHTML=i(t),n.bottomBar&&(n.bottomBar.innerHTML=o(t)),W(n.topCont,function(e){var a=Z[e.className]
a&&(n[a]=e),e.className=="DynarchCalendar-menu-year"?(B(e,t._focusEvents),n.yearInput=e):le&&e.setAttribute("unselectable","on")}),t.setTime(null,!0)},ue.setLanguage=function(e){var n=t.setLanguage(e)
n&&(this.fdow=n.data.fdow,this.redraw())},t.setLanguage=function(t){var e=de[t]
return e&&(de.__=e),e},ue.focus=function(){try{this.els[this._menuVisible?"yearInput":"focusLink"].focus()}catch(t){}c.call(this)},ue.blur=function(){this.els.focusLink.blur(),this.els.yearInput.blur(),h.call(this)},ue.showAt=function(t,e,n){this._showAnim&&this._showAnim.stop(),n=n&&this.args.animation
var a=this.els.topCont,s=this,i=this.els.body.firstChild,r=i.offsetHeight,o=a.style
o.position="absolute",o.left=t+"px",o.top=e+"px",o.zIndex=1e4,o.display="",n&&(i.style.marginTop=-r+"px",this.args.opacity>1&&$(a,0),this._showAnim=K({onUpdate:function(t,e){i.style.marginTop=-e(ae.accel_b(t),r,0)+"px",s.args.opacity>1&&$(a,t)},onStop:function(){s.args.opacity>1&&$(a,""),s._showAnim=null}}))},ue.hide=function(){this.callHooks("onClose",this)
var t=this.els.topCont,e=this,n=this.els.body.firstChild,a=n.offsetHeight,s=G(t).y
this.args.animation?(this._showAnim&&this._showAnim.stop(),this._showAnim=K({onUpdate:function(i,r){e.args.opacity>1&&$(t,1-i),n.style.marginTop=-r(ae.accel_b(i),0,a)+"px",t.style.top=r(ae.accel_ab(i),s,s-10)+"px"},onStop:function(){t.style.display="none",n.style.marginTop="",e.args.opacity>1&&$(t,""),e._showAnim=null}})):t.style.display="none",this.inputField=null},ue.popup=function(t,e){function n(e){var n={x:l.x,y:l.y}
return e?(/B/.test(e)&&(n.y+=t.offsetHeight),/b/.test(e)&&(n.y+=t.offsetHeight-a.y),/T/.test(e)&&(n.y-=a.y),/l/.test(e)&&(n.x-=a.x-t.offsetWidth),/L/.test(e)&&(n.x-=a.x),/R/.test(e)&&(n.x+=t.offsetWidth),/c/i.test(e)&&(n.x+=(t.offsetWidth-a.x)/2),/m/i.test(e)&&(n.y+=(t.offsetHeight-a.y)/2),n):n}var a,s,i,r,o,l
t=J(t),e||(e=this.args.align),e=e.split(/\x2f/),s=G(t),i=this.els.topCont,r=i.style,o=X(),r.visibility="hidden",r.display="",this.showAt(0,0),document.body.appendChild(i),a={x:i.offsetWidth,y:i.offsetHeight},l=s,l=n(e[0]),l.y<o.y&&(l.y=s.y,l=n(e[1])),l.x+a.x>o.x+o.w&&(l.x=s.x,l=n(e[2])),l.y+a.y>o.y+o.h&&(l.y=s.y,l=n(e[3])),l.x<o.x&&(l.x=s.x,l=n(e[4])),this.showAt(l.x,l.y,!0),r.visibility="",this.focus()},ue.popupForField=function(e,n,a){var s,i,r,o,l=this
n=J(n),e=J(e),l.inputField=n,l.dateFormat=a,l.selection.type==t.SEL_SINGLE&&(s=/input|textarea/i.test(n.tagName)?n.value:n.innerText||n.textContent,s&&(i=/(^|[^%])%[bBmo]/.exec(a),r=/(^|[^%])%[de]/.exec(a),i&&r&&(o=i.index<r.index),s=Calendar.parseDate(s,o),s&&(l.selection.set(s,!1,!0),l.args.showTime&&(l.setHours(s.getHours()),l.setMinutes(s.getMinutes())),l.moveTo(s)))),l.popup(e)},ue.manageFields=function(t,e,n){var a=this
e=J(e),t=J(t),/^button$/i.test(t.tagName)&&t.setAttribute("type","button"),B(t,"click",function(s){return a.popupForField(t,e,n),N(s)})},ue.callHooks=function(t){for(var e=U(arguments,1),n=this.handlers[t],a=0;a<n.length;++a)n[a].apply(this,e)},ue.addEventListener=function(t,e){this.handlers[t].push(e)},ue.removeEventListener=function(t,e){for(var n=this.handlers[t],a=n.length;--a>=0;)n[a]===e&&n.splice(a,1)},ue.getTime=function(){return this.time},ue.setTime=function(t,e){var n,a,s,i,r,o
this.args.showTime&&(t=null!=t?t:this.time,this.time=t,n=this.getHours(),a=this.getMinutes(),s=12>n,this.args.showTime==12&&(0==n&&(n=12),n>12&&(n-=12),this.els.timeAM.innerHTML=_(s?"AM":"PM")),10>n&&(n="0"+n),10>a&&(a="0"+a),this.els.timeHour.innerHTML=n,this.els.timeMinute.innerHTML=a,e||(this.callHooks("onTimeChange",this,t),i=this.inputField,r=this.selection,i&&(o=r.print(this.dateFormat),/input|textarea/i.test(i.tagName)?i.value=o:i.innerHTML=o)))},ue.getHours=function(){return Math.floor(this.time/100)},ue.getMinutes=function(){return this.time%100},ue.setHours=function(t){0>t&&(t+=24),this.setTime(100*(t%24)+this.time%100)},ue.setMinutes=function(t){0>t&&(t+=60),t=Math.floor(t/this.args.minuteStep)*this.args.minuteStep,this.setTime(100*this.getHours()+t%60)},ue._getInputYear=function(){var t=parseInt(this.els.yearInput.value,10)
return isNaN(t)&&(t=this.date.getFullYear()),t},ue._showTooltip=function(t){var e,n="",a=this.els.tooltip
t&&(t=A(t),e=this.args.dateInfo(t),e&&e.tooltip&&(n="<div class='DynarchCalendar-tooltipCont'>"+S(t,e.tooltip)+"</div>")),a.innerHTML=n},Q=" align='center' cellspacing='0' cellpadding='0'",Z={"DynarchCalendar-topCont":"topCont","DynarchCalendar-focusLink":"focusLink",DynarchCalendar:"main","DynarchCalendar-topBar":"topBar","DynarchCalendar-title":"title","DynarchCalendar-dayNames":"dayNames","DynarchCalendar-body":"body","DynarchCalendar-menu":"menu","DynarchCalendar-menu-year":"yearInput","DynarchCalendar-bottomBar":"bottomBar","DynarchCalendar-tooltip":"tooltip","DynarchCalendar-time-hour":"timeHour","DynarchCalendar-time-minute":"timeMinute","DynarchCalendar-time-am":"timeAM","DynarchCalendar-navBtn DynarchCalendar-prevYear":"navPrevYear","DynarchCalendar-navBtn DynarchCalendar-nextYear":"navNextYear","DynarchCalendar-navBtn DynarchCalendar-prevMonth":"navPrevMonth","DynarchCalendar-navBtn DynarchCalendar-nextMonth":"navNextMonth"},te={"-3":"backYear","-2":"back",0:"now",2:"fwd",3:"fwdYear"},ee={37:-1,38:-2,39:1,40:2},ne={33:-1,34:1},ue._getDateDiv=function(t){var e=null
if(t)try{W(this.els.body,function(n){if(n.getAttribute("dyc-date")==t)throw e=n})}catch(n){}return e},(t.Selection=function(t,e,n,a){this.type=e,this.sel=t instanceof Array?t:[t],this.onChange=O(n,a),this.cal=a}).prototype={get:function(){return this.type==t.SEL_SINGLE?this.sel[0]:this.sel},isEmpty:function(){return this.sel.length==0},set:function(e,n,a){var s=this.type==t.SEL_SINGLE
e instanceof Array?(this.sel=e,this.normalize(),a||this.onChange(this)):(e=L(e),s||!this.isSelected(e)?(s?this.sel=[e]:this.sel.splice(this.findInsertPos(e),0,e),this.normalize(),a||this.onChange(this)):n&&this.unselect(e,a))},reset:function(){this.sel=[],this.set.apply(this,arguments)},countDays:function(){for(var t,e,n,a=0,s=this.sel,i=s.length;--i>=0;)t=s[i],t instanceof Array&&(e=A(t[0]),n=A(t[1]),a+=Math.round(Math.abs(n.getTime()-e.getTime())/864e5)),++a
return a},unselect:function(t,e){var n,a,s,i,r,o,l
for(t=L(t),n=!1,s=this.sel,i=s.length;--i>=0;)a=s[i],a instanceof Array?t<a[0]||t>a[1]||(r=A(t),o=r.getDate(),t==a[0]?(r.setDate(o+1),a[0]=L(r),n=!0):t==a[1]?(r.setDate(o-1),a[1]=L(r),n=!0):(l=new Date(r),l.setDate(o+1),r.setDate(o-1),s.splice(i+1,0,[L(l),a[1]]),a[1]=L(r),n=!0)):t==a&&(s.splice(i,1),n=!0)
n&&(this.normalize(),e||this.onChange(this))},normalize:function(){var t,e,n,a,s,i,r
for(this.sel=this.sel.sort(function(t,e){return t instanceof Array&&(t=t[0]),e instanceof Array&&(e=e[0]),t-e}),n=this.sel,a=n.length;--a>=0;){if(t=n[a],t instanceof Array){if(t[0]>t[1]){n.splice(a,1)
continue}t[0]==t[1]&&(t=n[a]=t[0])}e&&(s=e,i=t instanceof Array?t[1]:t,i=A(i),i.setDate(i.getDate()+1),i=L(i),s>i||(r=n[a+1],t instanceof Array&&r instanceof Array?(t[1]=r[1],n.splice(a+1,1)):t instanceof Array?(t[1]=e,n.splice(a+1,1)):r instanceof Array?(r[0]=t,n.splice(a,1)):(n[a]=[t,r],n.splice(a+1,1)))),e=t instanceof Array?t[0]:t}},findInsertPos:function(t){for(var e,n=this.sel,a=n.length;--a>=0&&(e=n[a],e instanceof Array&&(e=e[0]),t<e););return a+1},clear:function(t){this.sel=[],t||this.onChange(this)},selectRange:function(e,n){var a,s
if(e=L(e),n=L(n),e>n&&(a=e,e=n,n=a),s=this.cal.args.checkRange,!s)return this._do_selectRange(e,n)
try{q(new t.Selection([[e,n]],t.SEL_MULTIPLE,se).getDates(),O(function(t){if(this.isDisabled(t))throw s instanceof Function&&s(t,this),"OUT"},this.cal)),this._do_selectRange(e,n)}catch(i){}},_do_selectRange:function(t,e){this.sel.push([t,e]),this.normalize(),this.onChange(this)},isSelected:function(t){for(var e,n=this.sel.length;--n>=0;)if(e=this.sel[n],e instanceof Array&&t>=e[0]&&t<=e[1]||t==e)return!0
return!1},getFirstDate:function(){var t=this.sel[0]
return t&&t instanceof Array&&(t=t[0]),t},getLastDate:function(){if(this.sel.length>0){var t=this.sel[this.sel.length-1]
return t&&t instanceof Array&&(t=t[1]),t}},print:function(t,e){var n,a=[],s=0,i=this.cal.getHours(),r=this.cal.getMinutes()
for(e||(e=" -> ");s<this.sel.length;)n=this.sel[s++],n instanceof Array?a.push(S(A(n[0],i,r),t)+e+S(A(n[1],i,r),t)):a.push(S(A(n,i,r),t))
return a},getDates:function(t){for(var e,n,a=[],s=0;s<this.sel.length;){if(n=this.sel[s++],n instanceof Array)for(e=A(n[0]),n=n[1];L(e)<n;)a.push(t?S(e,t):new Date(e)),e.setDate(e.getDate()+1)
else e=A(n)
a.push(t?S(e,t):e)}return a}},t.isUnicodeLetter=function(t){return t.toUpperCase()!=t.toLowerCase()},t.parseDate=function(e,n,a){var s,i,r,o,l,c,h,u,d,f,y
if(!/\S/.test(e))return""
for(e=e.replace(/^\s+/,"").replace(/\s+$/,""),a=a||new Date,s=null,i=null,r=null,o=null,l=null,c=null,h=e.match(/([0-9]{1,2}):([0-9]{1,2})(:[0-9]{1,2})?\s*(am|pm)?/i),h&&(o=parseInt(h[1],10),l=parseInt(h[2],10),c=h[3]?parseInt(h[3].substr(1),10):0,e=e.substring(0,h.index)+e.substr(h.index+h[0].length),h[4]&&(h[4].toLowerCase()=="pm"&&12>o?o+=12:h[4].toLowerCase()!="am"||12>o||(o-=12))),u=function(){function n(){return e.charAt(l++)}function a(){return e.charAt(l)}function s(t){for(;a()&&h(a());)t+=n()
return t}function i(){for(var t="";a()&&/[0-9]/.test(a());)t+=n()
return h(a())?s(t):parseInt(t,10)}function r(t){c.push(t)}for(var o,l=0,c=[],h=t.isUnicodeLetter;l<e.length;)o=a(),h(o)?r(s("")):/[0-9]/.test(o)?r(i()):n()
return c}(),d=[],f=0;f<u.length;++f)y=u[f],/^[0-9]{4}$/.test(y)?(s=parseInt(y,10),null==i&&null==r&&null==n&&(n=!0)):/^[0-9]{1,2}$/.test(y)?(y=parseInt(y,10),60>y?0>y||y>12?1>y||y>31||(r=y):d.push(y):s=y):null==i&&(i=I(y))
return d.length<2?d.length==1&&(null==r?r=d.shift():null==i&&(i=d.shift())):n?(null==i&&(i=d.shift()),null==r&&(r=d.shift())):(null==r&&(r=d.shift()),null==i&&(i=d.shift())),null==s&&(s=d.length>0?d.shift():a.getFullYear()),30>s?s+=2e3:99>s&&(s+=1900),null==i&&(i=a.getMonth()+1),null!=s&&null!=i&&null!=r?new Date(s,i-1,r,o,l,c):null},ae={elastic_b:function(t){return 1-Math.cos(5.5*-t*Math.PI)/Math.pow(2,7*t)},magnetic:function(t){return 1-Math.cos(10.5*t*t*t*Math.PI)/Math.exp(4*t)},accel_b:function(t){return t=1-t,1-t*t*t*t},accel_a:function(t){return t*t*t},accel_ab:function(t){return t=1-t,1-Math.sin(t*t*Math.PI/2)},accel_ab2:function(t){return(t/=.5)<1?.5*t*t:-0.5*(--t*(t-2)-1)},brakes:function(t){return t=1-t,1-Math.sin(t*t*Math.PI)},shake:function(t){return.5>t?-Math.cos(11*t*Math.PI)*t*t:(t=1-t,Math.cos(11*t*Math.PI)*t*t)}},se=Function(),t}()
