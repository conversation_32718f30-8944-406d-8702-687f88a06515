// pageframe javascript
$("#open_right").hide();

function closeleft() {
	$("#leftFrameCanvas").hide();
	$("#rightFrameCanvas").css("left", 0);
	$("#open_right").show();
	$("#open_right").attr("title", "显示车辆列表");
	$("#open_left").hide();
	$("#open_right").css("marginLeft", -300);
	//resizeInfoPanel();

	initDivWidthInNoteForm();
	monitorTable.resizeTable();
}

function openleft() {
	$("#leftFrameCanvas").show();
	$("#rightFrameCanvas").css("left", 301);
	$("#open_right").hide();
	$("#open_left").show();
	$("#open_left").attr("title", "隐藏车辆列表");
	//resizeInfoPanel();

	initDivWidthInNoteForm();
	monitorTable.resizeTable();
}
/**
function showMapPanelOld() {
	var infoPanel = $("#infoOpenDiv");
	var isVisible = infoPanel.is(":visible");
	var hMap = getWinHeight();
	var hInfo = 150;
	if (isVisible) {
		hInfo = infoPanel.height();
	}
	hMap = hMap - hInfo;
	$("#mapDiv").show();// 轨迹
	$("#mapDiv").css("height", hMap);
	$("#mapDiv").css("width", "100%");
}
**/
function showMapPanel() {
	var hMap = getWinHeight();
	var hInfoPanel = 180;
	hMap = hMap - hInfoPanel;
	$("#mapDiv").show();
	$("#mapDiv").css("height", hMap);
	$("#mapDiv").css("width", "100%");
}
/**
function resizeInfoPanelOld() {
	var isVisibleFrame = $("#leftFrameCanvas").is(":visible");
	var offset = 0;
	if (isVisibleFrame) {
		offset = $("#leftFrameCanvas").width();
	} else {
		offset = 1;
	}
	var isVisiblePanel = $("#infoOpenDiv").is(":visible");
	if (isVisiblePanel) {
		// var offset = 325;
		var winWidth = getWinWidth();
		var infoWidth = winWidth - offset;
		$("#infoOpenDiv").css("width", infoWidth + "px");
	}

	showMapPanel();
}
**/
function initDivWidthInNoteForm() {
	// alert("Calling the resizeDivInNoteForm()!");
	var isVisibleFrame = $("#leftFrameCanvas").is(":visible");
	var offset = 0;
	if (isVisibleFrame) {
		offset = $("#leftFrameCanvas").width();
	} else {
		offset = 1;
	}
	var winWidth = getWinWidth();
	var infoWidth = winWidth - offset;
	$("#monitorInfoSpanDiv").css("width", infoWidth + "px");
	//$("#warningInfoSpanDiv").css("width", infoWidth + "px");
}

/**
function infoPanelOpen() {
	// alert("Calling the infoPanelOpen()!");
	//
	var offset = 1;
	var isVisible = $("#leftFrameCanvas").is(":visible");
	if (isVisible) {
		offset = $("#leftFrameCanvas").width();
	} else {
		offset = 1;
	}
	var winWidth = getWinWidth();
	var infoWidth = winWidth - offset;
	//
	$("#infoOpenDiv").show();
	$("#infoOpenDiv").css("right", "20px");
	$("#infoOpenDiv").css("height", "150px");
	$("#infoOpenDiv").css("width", infoWidth + "px");
	$("#infoOpenBtnDiv").show();
	$("#infoOpenBtnDiv").css("right", "0px");
	$("#infoOpenBtnDiv").css("height", "150px");
	$("#infoOpenBtnDiv").css("width", "15px");
	$("#infoCloseDiv").hide();

	showMapPanel();
}

function infoPanelClose() {
	// alert("Calling the infoPanelClose()!");
	$("#infoOpenDiv").hide();
	$("#infoOpenBtnDiv").hide();
	$("#infoCloseDiv").show();

	showMapPanel();
}

function infoPanelCancel() {
	// alert("Calling the infoPanelCancel()!");
	$("#infoOpenDiv").html("");
	$("#infoOpenDiv").hide();
	$("#infoOpenBtnDiv").hide();
	$("#infoCloseDiv").hide();

	showMapPanel();
}
**/
function InitInfoTable() {
	var c = "<table border=0 id='tabInfo' width='100%' class='mouseOut'>"
			+ "<tr id='trInfo' class='trStyle' >"
			+ "<td>&nbsp;序号</td>"
			+ "<td>显隐</td>"
			+ "<td>车牌号</td>"
			+ "<td>GPS时间</td>"
			+ "<td>有效定位</td>"
			+ "<td>速度(公里/时)</td>"
			+ "<td>行车方向</td>"
			+ "<td>位置</td>"
			+ "<td>状态</td>"
			+ "</tr>"
			+ "<tr>"
			+ "	<td colspan=9 style='text-align: left; padding-left: 30px; line-height: 30px;'>"
			+ "</td>" + "</tr>" + "</table>";
	$("#infoOpenDiv").html(c);
}
