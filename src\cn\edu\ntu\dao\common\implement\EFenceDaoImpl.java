package cn.edu.ntu.dao.common.implement;

import java.text.SimpleDateFormat;
import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import cn.edu.ntu.dao.common.interfaces.EFenceDao;
import cn.edu.ntu.entity.common.PolygonAreaSettingParam;
import cn.edu.ntu.entity.common.RectangleAreaSettingParam;
import cn.edu.ntu.entity.common.RoundAreaSettingParam;
import sun.tools.jar.resources.jar;

@Repository
public class EFenceDaoImpl implements EFenceDao {

	@Autowired
	private JdbcTemplate jdbcTemplate;
	@Autowired
	private TransactionTemplate transactionTemplate;
	
	@Override
	public void addRoundAreaSetting(RoundAreaSettingParam param,String userName) {

		String sql="insert into roundareasetting (areaid,areaname,areaalias,areatype,geometrytype,starttime,endtime,maxspeed,lasttimeoverspeed,radius,centerlat,centerlng,createtime,creator) values (null,?,?,?,1,?,?,?,?,?,?,?,?,?)";
		
		Object[] params=new Object[12];
		params[0]=param.getAreaName();
		params[1]=param.getAreaAlias();
		params[2]=param.getAreaType();
		params[3]=param.getStartTime();
		params[4]=param.getEndTime();
		params[5]=param.getMaxSpeed();
		params[6]=param.getLastTimeOverSpeed();
		params[7]=param.getRadius();
		params[8]=param.getCenterLat();
		params[9]=param.getCenterLng();
		Date currentTime=new Date();
		SimpleDateFormat simpleDateFormat=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		String currentTimeString=simpleDateFormat.format(currentTime);
		params[10]=currentTimeString;
		params[11]=userName;
		
		jdbcTemplate.update(sql, params);
		
	}

	@Override
	public void addRectangleAreaSetting(RectangleAreaSettingParam param, String userName) {

		String sql="insert into rectangleareasetting (areaid,areaname,areaalias,areatype,geometrytype,starttime,endtime,maxspeed,lasttimeoverspeed,latonleftandtop,lngonleftandtop,latonrightandbottom,lngonrightandbottom,width,height,createtime,creator) values (null,?,?,?,2,?,?,?,?,?,?,?,?,?,?,?,?)";
		
		Object[] params=new Object[15];
		params[0]=param.getAreaName();
		params[1]=param.getAreaAlias();
		params[2]=param.getAreaType();
		params[3]=param.getStartTime();
		params[4]=param.getEndTime();
		params[5]=param.getMaxSpeed();
		params[6]=param.getLastTimeOverSpeed();
		params[7]=param.getLatOnLeftAndTop();
		params[8]=param.getLngOnLeftAndTop();
		params[9]=param.getLatOnRightAndBottom();
		params[10]=param.getLngOnRightAndBottom();
		params[11]=param.getWidth();
		params[12]=param.getHeight();
		Date currentTime=new Date();
		SimpleDateFormat simpleDateFormat=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		String currentTimeString=simpleDateFormat.format(currentTime);
		params[13]=currentTimeString;
		params[14]=userName;
		
		jdbcTemplate.update(sql, params);
	}

	@Override
	public void addPolygonAreaSetting(PolygonAreaSettingParam param, String userName) {

		
		
		transactionTemplate.execute(new TransactionCallback<Void>() {

			@Override
			public Void doInTransaction(TransactionStatus txStatus) {

				String sql="insert into polygonareasetting (areaid,areaname,areaalias,areatype,geometrytype,starttime,endtime,maxspeed,lasttimeoverspeed,numberofvertex,creattime,creator) values (null,?,?,?,3,?,?,?,?,?,?,?)";
				Object[] params=new Object[10];
				params[0]=param.getAreaName();
				params[1]=param.getAreaAlias();
				params[2]=param.getAreaType();
				params[3]=param.getStartTime();
				params[4]=param.getEndTime();
				params[5]=param.getMaxSpeed();
				params[6]=param.getLastTimeOverSpeed();
				params[7]=param.getNumberOfVertex();
				Date currentTime=new Date();
				SimpleDateFormat simpleDateFormat=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				String currentTimeString=simpleDateFormat.format(currentTime);
				params[8]=currentTimeString;
				params[9]=userName;
				
				jdbcTemplate.update(sql,params);
				
				int count=param.getNumberOfVertex();
				sql="insert into polygonvertexsetting (id,areaname,lat,lng) values (null,?,?,?)";
				for(int i=0;i<count;i++){
					params=new Object[3];
					params[0]=param.getAreaName();
					params[1]=param.getLatArr().get(i);
					params[2]=param.getLonArr().get(i);
					jdbcTemplate.update(sql,params);
				}
				
				return null;
			}
		});
	}


}
