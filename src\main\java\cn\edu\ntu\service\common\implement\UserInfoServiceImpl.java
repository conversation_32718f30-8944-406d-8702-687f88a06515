package cn.edu.ntu.service.common.implement;

import java.util.ArrayList;
import java.util.List;

import org.apache.struts2.components.Else;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;


import cn.edu.ntu.dao.common.implement.UserInfoDaoImpl;
import cn.edu.ntu.dao.common.implement.UserRelateGroupInfoDaoImpl;
import cn.edu.ntu.dao.common.interfaces.UserInfoDao;
import cn.edu.ntu.dao.common.interfaces.UserRelateGroupInfoDao;
import cn.edu.ntu.entity.common.UserInfo;
import cn.edu.ntu.service.common.interfaces.UserInfoService;

/**
 *
 * 
 * <AUTHOR>
 * @date 2016��6��13�� ����2:59:31 
 */
@Service
public class UserInfoServiceImpl implements UserInfoService{
	
	@Autowired
	private UserInfoDao userInfoDao;
	@Autowired
	private UserRelateGroupInfoDao userRelateGroupInfoDao;
	@Autowired
	private TransactionTemplate transactionTemplate;
	
	@Override
	public  boolean validateLogin(String userName, String userPassword) {
		
		try {
			userInfoDao.validateUserInfo(userName,userPassword);
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}
	}
	
	@Override
	public  UserInfo getUserInfo(String userName) {
		
		UserInfo userInfo = null;
		try {
			userInfo = userInfoDao.getUserInfo(userName);
		} catch (Exception e) {
			e.printStackTrace();
		}
		userInfo.setConfirmedPassword(userInfo.getPassword());
		return userInfo;
	}
	
	@Override
	public UserInfo getAccountTypeInfo(String name) {
		
		UserInfo userInfo = null;
		try {
			userInfo = userInfoDao.getUserInfo(name);
		} catch (Exception e) {
			e.printStackTrace();
		}
		
		UserInfo returnedUserInfo=new UserInfo();
		returnedUserInfo.setRoleName(userInfo.getRoleName());
		return returnedUserInfo;
	}

	
	@Override
	public String validateUserName(String userName) {

	    UserInfo userInfo = null;
		try {
			userInfo = userInfoDao.getUserInfo(userName);
		} catch (Exception e) {
			e.printStackTrace();
		}
		
		if(userInfo!=null)
			return "�û����Ѵ���";
		else
			return "�û���������";
	}


	@Override
	public List<UserInfo> listUsers(String currentUserName) {
		
		UserInfo userInfo=userInfoDao.getUserInfo(currentUserName);
		List<UserInfo> userList;
		if(userInfo.getRoleName().equals("��������Ա"))
			userList=userInfoDao.listAll(currentUserName);
		else
		    userList=userInfoDao.list(currentUserName);
		return userList;
		
	}
	
	@Override
	public  List<UserInfo> queryUsers(UserInfo userInfo,String parentUserName) {

		List<UserInfo> userInfoList=userInfoDao.queryUsers(userInfo,parentUserName);
		return userInfoList;
	}

	@Override
	public  void deleteUser(String userName) {

		userInfoDao.deleteUser(userName);
	}

	@Override
	public  void addStoreUser(UserInfo userInfo, String parentName) {
		
		transactionTemplate.execute(new TransactionCallback<Void>() {

			@Override
			public Void doInTransaction(TransactionStatus txStatus) {
                userInfoDao.addStoreUser(userInfo, parentName);
				
				String userName=userInfo.getName();
				String vehicleGroupName=userInfo.getVehicleGroupName();
				String[] vehicleGroupSelections=vehicleGroupName.split(",");
				if(vehicleGroupSelections[0].length()!=0){
					for(String vehicleGroupSelection:vehicleGroupSelections)
						userRelateGroupInfoDao.addStoreUserRelateGroup(userName,vehicleGroupSelection);
				}
				
				return null;
			}
		});
				
	}

	@Override
	public void editStoreUser(UserInfo userInfo) {

		transactionTemplate.execute(new TransactionCallback<Void>() {

			@Override
			public Void doInTransaction(TransactionStatus txStatus) {
				userInfoDao.editStoreUser(userInfo);

				String userName = userInfo.getName();
				String vehicleGroupName = userInfo.getVehicleGroupName();
				String[] vehicleGroupSelections = vehicleGroupName.split(",");

				userRelateGroupInfoDao.deleteUserRelateGroup(userName);

				for (String vehicleGroupSelection : vehicleGroupSelections)
					userRelateGroupInfoDao.addStoreUserRelateGroup(userName, vehicleGroupSelection);

				return null;
			}
		});
		
	}


}
