package cn.edu.ntu.entity.common;

import java.util.List;

import org.codehaus.jackson.annotate.JsonIgnore;
import org.codehaus.jackson.annotate.JsonProperty;

import com.sun.tracing.dtrace.ProviderAttributes;

/**
 * Dynatree Node Entity
 *
 * <AUTHOR>
 * @date 2016-04-25 01:30:47
 */
public class DynatreeNode {
	
	private String title;
	private String key;
	@JsonProperty("isFolder")
	private boolean isFolder;
	private boolean expand;
	@JsonProperty("isLazy")
    private boolean isLazy;
	private List<DynatreeNode> children;
	private boolean select;
	private int onlineCount;
	private int totalCount;
	private String status;
	
	public String getTitle() {
		return title;
	}
	public void setTitle(String title) {
		this.title = title;
	}
	public String getKey() {
		return key;
	}
	public void setKey(String key) {
		this.key = key;
	}
	public boolean isExpand() {
		return expand;
	}
	public void setExpand(boolean expand) {
		this.expand = expand;
	}
	@JsonIgnore
	public boolean isFolder() {
		return isFolder;
	}
	public void setFolder(boolean isFolder) {
		this.isFolder = isFolder;
	}
	@JsonIgnore
	public boolean isLazy() {
		return isLazy;
	}
	public void setLazy(boolean isLazy) {
		this.isLazy = isLazy;
	}
	public List<DynatreeNode> getChildren() {
		return children;
	}
	public void setChildren(List<DynatreeNode> children) {
		this.children = children;
	}
	public boolean isSelect() {
		return select;
	}
	public void setSelect(boolean select) {
		this.select = select;
	}
	public int getOnlineCount() {
		return onlineCount;
	}
	public void setOnlineCount(int onlineCount) {
		this.onlineCount = onlineCount;
	}
	public int getTotalCount() {
		return totalCount;
	}
	public void setTotalCount(int totalCount) {
		this.totalCount = totalCount;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	} 

}
