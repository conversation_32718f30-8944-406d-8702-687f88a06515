package cn.edu.ntu.action;

import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap.KeySetView;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.struts2.ServletActionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.opensymphony.xwork2.ActionContext;
import com.opensymphony.xwork2.ActionSupport;

import cn.edu.ntu.dao.common.implement.UserInfoDaoImpl;
import cn.edu.ntu.entity.common.MemberInfo;
import cn.edu.ntu.entity.common.UserInfo;
import cn.edu.ntu.service.common.implement.CommonServiceImpl;
import cn.edu.ntu.service.common.implement.MemberInfoServiceImpl;
import cn.edu.ntu.service.common.implement.UserInfoServiceImpl;
import cn.edu.ntu.service.common.interfaces.CommonService;
import cn.edu.ntu.service.common.interfaces.MemberInfoService;
import cn.edu.ntu.service.common.interfaces.UserInfoService;

/**
 *
 * 
 * <AUTHOR>
 * @date 2016��3��28�� ����3:57:22 
 */
@Controller
@Scope("prototype")
public class UserAction extends ActionSupport {
	
	private String userName;
	private String userPassword;
	
	private UserInfo userInfo;
	private List<UserInfo> userInfoList;
	
	private UserInfoService userInfoService;
	private MemberInfoService memberInfoService;

	public String getUserName() {
		return userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}
	public String getUserPassword() {
		return userPassword;
	}
	public void setUserPassword(String userPassword) {
		this.userPassword = userPassword;
	}
	
	public UserInfo getUserInfo() {
		return userInfo;
	}
	public void setUserInfo(UserInfo userInfo) {
		this.userInfo = userInfo;
	}

	public List<UserInfo> getUserInfoList() {
		return userInfoList;
	}
	public void setUserInfoList(List<UserInfo> userInfoList) {
		this.userInfoList = userInfoList;
	}
	
	@Autowired
	public void setUserInfoService(UserInfoService userInfoService) {
		this.userInfoService = userInfoService;
	}
	
	@Autowired
	public void setMemberInfoService(MemberInfoService memberInfoService) {
		this.memberInfoService = memberInfoService;
	}
	
	public String list(){

		String currentUserName ="";
		HttpServletRequest request=ServletActionContext.getRequest();
		Cookie[] cookies=request.getCookies();
		for(Cookie cookie:cookies){
			if(cookie.getName().equals("accountName"))
				currentUserName=cookie.getValue();
		}
		
		userInfoList=userInfoService.listUsers(currentUserName);
		
		return SUCCESS;
	}
	
	public String add(){
		
		String name ="";
		HttpServletRequest request=ServletActionContext.getRequest();
		Cookie[] cookies=request.getCookies();
		for(Cookie cookie:cookies){
			if(cookie.getName().equals("accountName"))
				name=cookie.getValue();
		}
		
		userInfo=userInfoService.getAccountTypeInfo(name);
		return SUCCESS;
	}
	
	public String addStore(){

		String name ="";
		HttpServletRequest request=ServletActionContext.getRequest();
		Cookie[] cookies=request.getCookies();
		for(Cookie cookie:cookies){
			if(cookie.getName().equals("accountName"))
				name=cookie.getValue();
		}
		
		userInfoService.addStoreUser(userInfo,name);
		return SUCCESS;
	}
	
	public String edit(){
		
		userInfo=userInfoService.getUserInfo(userName);
		return SUCCESS;
	}
	
	public String editStore(){
		
		userInfoService.editStoreUser(userInfo);
		return SUCCESS;
	}
	

	public String delete(){
		userInfoService.deleteUser(userName);
		return SUCCESS;
	}
	
	public String query(){

		String name ="";
		HttpServletRequest request=ServletActionContext.getRequest();
		Cookie[] cookies=request.getCookies();
		for(Cookie cookie:cookies){
			if(cookie.getName().equals("accountName"))
				name=cookie.getValue();
		}
		
		userInfoList=userInfoService.queryUsers(userInfo,name);
		return SUCCESS;
	}
	

}
