<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE struts PUBLIC
	"-//Apache Software Foundation//DTD Struts Configuration 2.3//EN"
	"http://struts.apache.org/dtds/struts-2.3.dtd">

<struts>

	<constant name="struts.devMode" value="true" />
	
	<constant name="struts.objectFactory" value="org.apache.struts2.spring.StrutsSpringObjectFactory" />
	<package name="endParams" namespace="/endParams" extends="json-default">
	
	     <action name="queryEndParamsAction" class="endParamsAction" method="queryEndParams">
             <result type="json">
                 <param name="root">commandInfo</param>
             </result>
         </action>
	
         <action name="queryServerParamsAction" class="endParamsAction" method="queryServerParams">
             <result type="json">
                 <param name="root">serverParams</param>
             </result>
         </action>
         
         <action name="setServerParamsAction" class="endParamsAction" method="setServerParams">
             <result type="json">
                 <param name="root">commandInfo</param>
             </result>
         </action>
         
          <action name="queryIcCardAuthenticationParamsAction" class="endParamsAction" method="queryIcCardAuthenticationParams">
             <result type="json">
                 <param name="root">icCardAuthenticationParams</param>
             </result>
         </action>
         
         <action name="setIcCardAuthenticationParamsAction" class="endParamsAction" method="setIcCardAuthenticationParams">
             <result type="json">
                 <param name="root">commandInfo</param>
             </result>
         </action>
         
          <action name="queryPosParamsAction" class="endParamsAction" method="queryPosParams">
             <result type="json">
                 <param name="root">posParams</param>
             </result>
         </action>
         
         <action name="setPosParamsAction" class="endParamsAction" method="setPosParams">
             <result type="json">
                 <param name="root">commandInfo</param>
             </result>
         </action>
         
         <action name="queryTerminalParamsAction" class="endParamsAction" method="queryTerminalParams">
             <result type="json">
                 <param name="root">terminalParams</param>
             </result>
         </action>
         
         <action name="setTerminalParamsAction" class="endParamsAction" method="setTerminalParams">
             <result type="json">
                 <param name="root">commandInfo</param>
             </result>
         </action>
         
         <action name="queryOverSpeedParamsAction" class="endParamsAction" method="queryOverSpeedParams">
             <result type="json">
                 <param name="root">overSpeedParams</param>
             </result>
         </action>
         
         <action name="setOverSpeedParamsAction" class="endParamsAction" method="setOverSpeedParams">
             <result type="json">
                 <param name="root">commandInfo</param>
             </result>
         </action>
         
         <action name="queryFatigueDrivingParamsAction" class="endParamsAction" method="queryFatigueDrivingParams">
             <result type="json">
                 <param name="root">fatigueDrivingParams</param>
             </result>
         </action>
         
         <action name="setFatigueDrivingParamsAction" class="endParamsAction" method="setFatigueDrivingParams">
             <result type="json">
                 <param name="root">commandInfo</param>
             </result>
         </action>
         
         <action name="queryLicenseParamsAction" class="endParamsAction" method="queryLicenseParams">
             <result type="json">
                 <param name="root">licenseParams</param>
             </result>
         </action>
         
         <action name="setLicenseParamsAction" class="endParamsAction" method="setLicenseParams">
             <result type="json">
                 <param name="root">commandInfo</param>
             </result>
         </action>
    </package>

	
</struts>