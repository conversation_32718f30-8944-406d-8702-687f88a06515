<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE struts PUBLIC
	"-//Apache Software Foundation//DTD Struts Configuration 2.3//EN"
	"http://struts.apache.org/dtds/struts-2.3.dtd">

<struts>

	<constant name="struts.devMode" value="true" />
	
	<constant name="struts.objectFactory" value="org.apache.struts2.spring.StrutsSpringObjectFactory" />
	
	 <package name="vehicleGroup" namespace="/vehicleGroup" extends="struts-default">
        <action name="listAction" class="vehicleGroupAction" method="list">
            <result>/page/vehicleGroupManage.jsp</result>
        </action>
        
        <action name="addAction" class="vehicleGroupAction" method="add">
            <result>/page/vehicleGroupEdit.jsp</result>
        </action>
        
        <action name="addStoreAction" class="vehicleGroupAction" method="addStore">
            <result type="redirect">listAction.action</result>
        </action>
        
        <action name="editAction" class="vehicleGroupAction" method="edit">
            <result>/page/vehicleGroupEdit.jsp</result>
        </action>
        
        <action name="editStoreAction" class="vehicleGroupAction" method="editStore">
            <result type="redirect">listAction.action</result>
        </action>
        
        <action name="deleteAction" class="vehicleGroupAction" method="delete">
            <result type="redirect">listAction.action</result>
        </action>
        
        <action name="queryAction" class="vehicleGroupAction" method="query">
            <result>/page/vehicleGroupManage.jsp</result>
        </action>
    </package>
	
</struts>