package cn.edu.ntu.service.common.implement;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.w3c.dom.NodeList;

import cn.edu.ntu.dao.common.implement.DriverInfoDaoImpl;
import cn.edu.ntu.dao.common.implement.MemberInfoDaoImpl;
import cn.edu.ntu.dao.common.implement.VehicleGroupTreeDaoImpl;
import cn.edu.ntu.dao.common.implement.VehicleRelateGroupInfoDaoImpl;
import cn.edu.ntu.dao.common.interfaces.MemberInfoDao;
import cn.edu.ntu.dao.common.interfaces.UserRelateGroupInfoDao;
import cn.edu.ntu.dao.common.interfaces.VehicleGroupTreeDao;
import cn.edu.ntu.dao.common.interfaces.VehicleInfoDao;
import cn.edu.ntu.dao.common.interfaces.VehicleRelateGroupInfoDao;
import cn.edu.ntu.entity.common.DynatreeNode;
import cn.edu.ntu.entity.common.MemberInfo;
import cn.edu.ntu.entity.common.VehicleGroupInfo;
import cn.edu.ntu.entity.common.VehicleInfo;
import cn.edu.ntu.entity.common.VehicleInfoInGroup;
import cn.edu.ntu.dao.common.implement.UserRelateGroupInfoDaoImpl;
import cn.edu.ntu.service.common.interfaces.MemberInfoService;
import cn.edu.ntu.service.common.interfaces.PositionReportAssistRedisService;
import cn.edu.ntu.service.common.interfaces.VehicleGpsInfoService;
import cn.edu.ntu.service.common.interfaces.VehicleGroupTreeService;
import cn.edu.ntu.utils.others.PositionReportAssist;

/**
 *
 * 
 * <AUTHOR>
 * @date 2016��6��24�� ����2:44:23
 */
@Service
public class VehicleGroupTreeServiceImpl implements VehicleGroupTreeService {

	@Autowired
	private VehicleInfoDao vehicleInfoDao;
	@Autowired
	private PositionReportAssistRedisService positionReportAssistRedisService;
	@Autowired
	private MemberInfoDao memberInfoDao;
	@Autowired
	private VehicleGroupTreeDao vehicleGroupTreeDao;
	@Autowired
	private UserRelateGroupInfoDao userRelateGroupInfoDao;
	@Autowired
	private VehicleRelateGroupInfoDao vehicleRelateGroupInfoDao;

	@Override
	public List<DynatreeNode> listUserRelateGroups(String currentUserName) {
		List<VehicleGroupInfo> list = vehicleGroupTreeDao.listAncestorsByUserName(currentUserName);
		List<DynatreeNode> nodeList = new ArrayList<DynatreeNode>();
		for (VehicleGroupInfo vehicleGroupInfo : list) {
			boolean isLeaf = vehicleGroupTreeDao.isLeaf(vehicleGroupInfo);
			vehicleGroupInfo.setLeaf(isLeaf);
			DynatreeNode node = buildNode(vehicleGroupInfo);
			if (!isLeaf)
				node.setChildren(buildChildNodes(vehicleGroupInfo));
			nodeList.add(node);
		}

		return nodeList;
	}

	@Override
	public List<DynatreeNode> listAll() {
		List<VehicleGroupInfo> list = vehicleGroupTreeDao.listAncestors();
		List<DynatreeNode> nodeList = new ArrayList<DynatreeNode>();
		for (VehicleGroupInfo vehicleGroupInfo : list) {
			DynatreeNode node = buildNode(vehicleGroupInfo);
			
			boolean isLeaf = vehicleGroupTreeDao.isLeaf(vehicleGroupInfo);
			if (!isLeaf)
				node.setChildren(buildChildNodes(vehicleGroupInfo));
			nodeList.add(node);
		}

		return nodeList;
	}
	
	@Override
	public List<DynatreeNode> listByCreator(String currentUserName) {
		
		List<VehicleGroupInfo> list=vehicleGroupTreeDao.listByCreator(currentUserName);
		
		List<DynatreeNode> nodeList=new ArrayList<DynatreeNode>();
		
		for(VehicleGroupInfo info:list){
			DynatreeNode node=buildNode(info);
			
			nodeList.add(node);
		}
		
		return nodeList;
		
	}


	@Override
	public List<DynatreeNode> listAll(String currentVehicleGroupName) {
		List<VehicleGroupInfo> list = vehicleGroupTreeDao.listAncestors(currentVehicleGroupName);
		List<DynatreeNode> nodeList = new ArrayList<DynatreeNode>();
		for (VehicleGroupInfo vehicleGroupInfo : list) {
			boolean isLeaf = vehicleGroupTreeDao.isLeaf(vehicleGroupInfo);
			vehicleGroupInfo.setLeaf(isLeaf);
			DynatreeNode node = buildNode(vehicleGroupInfo);
			if (!isLeaf)
				node.setChildren(buildChildNodes(vehicleGroupInfo, currentVehicleGroupName));
			nodeList.add(node);
		}

		return nodeList;
	}

	@Override
	public DynatreeNode showAllGroup(String accountName) {

		List<DynatreeNode> nodeList = new ArrayList<DynatreeNode>();
		
		List<VehicleInfo> vehicleInfoList = vehicleInfoDao.listVehiclesByUserNameWithSingleGroupName(accountName);

		Set<String> groupNameList = new HashSet<String>();
		for (VehicleInfo vehicleInfo : vehicleInfoList) {
			groupNameList.add(vehicleInfo.getVehicleGroupName());
		}
		
		for (String groupName : groupNameList) {
			List<VehicleInfo> vehicleInfoListInGroup = new ArrayList<VehicleInfo>();

			for (VehicleInfo vehicleInfo : vehicleInfoList) {
				if (vehicleInfo.getVehicleGroupName().equals(groupName))
					vehicleInfoListInGroup.add(vehicleInfo);
			}

			DynatreeNode dynatreeNode = new DynatreeNode();
			dynatreeNode.setTitle(groupName);
			dynatreeNode.setKey("group_" + groupName);
			dynatreeNode.setFolder(true);
			dynatreeNode.setExpand(false);
			dynatreeNode.setLazy(true);
			dynatreeNode.setTotalCount(vehicleInfoListInGroup.size());

			int onlineCount = 0;
			List<DynatreeNode> subDynatreeNodeList = new ArrayList<DynatreeNode>();
			for (VehicleInfo vehicleInfo : vehicleInfoListInGroup) {

				DynatreeNode subDynatreeNode = new DynatreeNode();
				subDynatreeNode.setKey("node_" + vehicleInfo.getCarNumber());
				subDynatreeNode.setTitle(vehicleInfo.getCarNumber());
				subDynatreeNode.setFolder(false);
				subDynatreeNode.setExpand(false);
				subDynatreeNode.setLazy(false);
				//modified
				/**
				String status;
				PositionReportAssist statusReport = positionReportAssistRedisService.load(vehicleInfo.getSimCardNumber());
				if (statusReport != null && statusReport.getOnlineStatus() == 1) {
					status = "在线";
					onlineCount++;
				} else {
					status = "离线";
				}
				**/
				String status="在线";
				subDynatreeNode.setStatus(status);
				subDynatreeNodeList.add(subDynatreeNode);
			}
			dynatreeNode.setOnlineCount(onlineCount);
			dynatreeNode.setChildren(subDynatreeNodeList);
			nodeList.add(dynatreeNode);
		}

		DynatreeNode node = new DynatreeNode();
		node.setTitle("全部分组");
		node.setKey("group_-1");
		node.setFolder(true);
		node.setExpand(true);
		node.setLazy(false);
		node.setChildren(nodeList);
		
		return node;
	}

	@Override
	public DynatreeNode showSearchedGroup(String accountName, String keyWord, String keyWordType) {

		List<DynatreeNode> nodeList = new ArrayList<DynatreeNode>();

		if (keyWordType.equals("车牌")) {
			List<String> list = userRelateGroupInfoDao.showGroupsByUserName(accountName);
			for (String groupName : list) {
				DynatreeNode dynatreeNode = new DynatreeNode();
				dynatreeNode.setTitle(groupName);
				dynatreeNode.setKey("group_" + groupName);
				dynatreeNode.setFolder(true);
				dynatreeNode.setExpand(true);
				dynatreeNode.setLazy(false);
				List<DynatreeNode> subNodeList = showSearchedVehiclesByGroupAndCarNumber(groupName, keyWord);
				dynatreeNode.setChildren(subNodeList);
				if (subNodeList.size() != 0)
					nodeList.add(dynatreeNode);
			}
		} else if(keyWordType.equals("SIM卡号")){
			List<String> list = userRelateGroupInfoDao.showGroupsByUserName(accountName);
			for (String groupName : list) {
				DynatreeNode dynatreeNode = new DynatreeNode();
				dynatreeNode.setTitle(groupName);
				dynatreeNode.setKey("group_" + groupName);
				dynatreeNode.setFolder(true);
				dynatreeNode.setExpand(true);
				dynatreeNode.setLazy(false);
				List<DynatreeNode> subNodeList = showSearchedVehiclesByGroupAndSimCardNumber(groupName, keyWord);
				dynatreeNode.setChildren(subNodeList);
				if (subNodeList.size() != 0)
					nodeList.add(dynatreeNode);
			}
		}else if (keyWordType.equals("车组")) {
			List<String> list = userRelateGroupInfoDao.showGroupsByUserNameAndGroupName(accountName, keyWord);
			
			for (String groupName : list) {
				DynatreeNode dynatreeNode = new DynatreeNode();
				dynatreeNode.setTitle(groupName);
				dynatreeNode.setKey("group_" + groupName);
				dynatreeNode.setFolder(true);
				dynatreeNode.setExpand(true);
				dynatreeNode.setLazy(false);
				List<DynatreeNode> subNodeList = showSearchedVehiclesByGroupAndGroupName(groupName);
				dynatreeNode.setChildren(subNodeList);
				if (subNodeList.size() != 0)
					nodeList.add(dynatreeNode);
			}
		}

		DynatreeNode node = new DynatreeNode();
		node.setTitle("全部分组");
		node.setKey("group_-1");
		node.setFolder(true);
		node.setExpand(true);
		node.setLazy(false);
		node.setChildren(nodeList);

		return node;

	}

	private List<DynatreeNode> showAllVehiclesByGroup(String groupName, String accountName) {

		List<DynatreeNode> nodeList = new ArrayList<DynatreeNode>();

		List<String> list = vehicleRelateGroupInfoDao.showVehiclesByGroup(groupName);

		for (String carNumber : list) {
			DynatreeNode dynatreeNode = new DynatreeNode();
			dynatreeNode.setKey("node_" + carNumber);
			dynatreeNode.setTitle(carNumber);
			dynatreeNode.setFolder(false);
			dynatreeNode.setExpand(false);
			dynatreeNode.setLazy(false);
			VehicleInfo vehicleInfo = null;
			try {
				vehicleInfo = vehicleInfoDao.getVehicleInfoByCarNumber(carNumber);
			} catch (Exception e) {
			}
			PositionReportAssist statusReport = positionReportAssistRedisService.load(vehicleInfo.getSimCardNumber());
			String status;
			if (statusReport != null && statusReport.getOnlineStatus() == 1)
				status = "在线";
			else
				status = "离线";
			dynatreeNode.setStatus(status);
			nodeList.add(dynatreeNode);
		}

		return nodeList;
	}
	

	private List<DynatreeNode> showSearchedVehiclesByGroupAndGroupName(String groupName) {
		List<DynatreeNode> nodeList = new ArrayList<DynatreeNode>();
		List<String> list =  vehicleRelateGroupInfoDao.showVehiclesByGroup(groupName);
		for (String carNumber : list) {
			DynatreeNode dynatreeNode = new DynatreeNode();
			dynatreeNode.setKey("node_" + carNumber);
			dynatreeNode.setTitle(carNumber);
			dynatreeNode.setFolder(false);
			dynatreeNode.setExpand(false);
			dynatreeNode.setLazy(false);

			VehicleInfo vehicleInfo = null;
			try {
				vehicleInfo = vehicleInfoDao.getVehicleInfoByCarNumber(carNumber);
			} catch (Exception e) {
			}
			PositionReportAssist statusReport = positionReportAssistRedisService.load(vehicleInfo.getSimCardNumber());
			String status;
			if (statusReport != null && statusReport.getOnlineStatus() == 1)
				status = "在线";
			else
				status = "离线";
			dynatreeNode.setStatus(status);

			nodeList.add(dynatreeNode);
		}

		return nodeList;
		
	}

	private List<DynatreeNode> showSearchedVehiclesByGroupAndSimCardNumber(String groupName, String simCardNumber) {
		List<DynatreeNode> nodeList = new ArrayList<DynatreeNode>();
		List<String> list = vehicleRelateGroupInfoDao.showSearchedVehiclesByGroupAndSimCardNumber(groupName, simCardNumber);
		for (String carNumber : list) {
			DynatreeNode dynatreeNode = new DynatreeNode();
			dynatreeNode.setKey("node_" + carNumber);
			dynatreeNode.setTitle(carNumber);
			dynatreeNode.setFolder(false);
			dynatreeNode.setExpand(false);
			dynatreeNode.setLazy(false);

			VehicleInfo vehicleInfo = null;
			try {
				vehicleInfo = vehicleInfoDao.getVehicleInfoByCarNumber(carNumber);
			} catch (Exception e) {
			}
			PositionReportAssist statusReport = positionReportAssistRedisService.load(vehicleInfo.getSimCardNumber());
			String status;
			if (statusReport != null && statusReport.getOnlineStatus() == 1)
				status = "在线";
			else
				status = "离线";
			dynatreeNode.setStatus(status);

			nodeList.add(dynatreeNode);
		}

		return nodeList;
	}

	private List<DynatreeNode> showSearchedVehiclesByGroupAndCarNumber(String groupName, String carNumber) {
		List<DynatreeNode> nodeList = new ArrayList<DynatreeNode>();
		List<String> list = vehicleRelateGroupInfoDao.showSearchedVehiclesByGroupAndCarNumber(groupName, carNumber);
		for (String carNumberInList : list) {
			DynatreeNode dynatreeNode = new DynatreeNode();
			dynatreeNode.setKey("node_" + carNumberInList);
			dynatreeNode.setTitle(carNumberInList);
			dynatreeNode.setFolder(false);
			dynatreeNode.setExpand(false);
			dynatreeNode.setLazy(false);

			VehicleInfo vehicleInfo = null;
			try {
				vehicleInfo = vehicleInfoDao.getVehicleInfoByCarNumber(carNumberInList);
			} catch (Exception e) {
			}
			PositionReportAssist statusReport = positionReportAssistRedisService.load(vehicleInfo.getSimCardNumber());
			String status;
			if (statusReport != null && statusReport.getOnlineStatus() == 1)
				status = "在线";
			else
				status = "lixi";
			dynatreeNode.setStatus(status);

			nodeList.add(dynatreeNode);
		}

		return nodeList;
	}

	/**
	private List<DynatreeNode> showSearchedVehiclesByGroup(String groupName, String keyWord, String keyWordType) {
		List<DynatreeNode> nodeList = new ArrayList<DynatreeNode>();
		List<String> list = null;
		if (keyWordType.equals("����")) {
			list = vehicleRelateGroupInfoDao.showSearchedVehiclesByGroupAndCarNumber(groupName, keyWord);
		} else if (keyWordType.equals("SIM��")) {
			list = vehicleRelateGroupInfoDao.showSearchedVehiclesByGroupAndSimCardNumber(groupName, keyWord);
		} else if (keyWordType.equals("����")) {
			list = vehicleRelateGroupInfoDao.showVehiclesByGroup(groupName);
		}
		for (String carNumber : list) {
			DynatreeNode dynatreeNode = new DynatreeNode();
			dynatreeNode.setKey("node_" + carNumber);
			dynatreeNode.setTitle(carNumber);
			dynatreeNode.setFolder(false);
			dynatreeNode.setExpand(false);
			dynatreeNode.setLazy(false);

			VehicleInfo vehicleInfo = null;
			try {
				vehicleInfo = vehicleInfoDao.getVehicleInfoByCarNumber(carNumber);
			} catch (Exception e) {
			}
			PositionReportAssist statusReport = positionReportAssistRedisService.load(vehicleInfo.getSimCardNumber());
			String status;
			if (statusReport != null && statusReport.getOnlineStatus() == 1)
				status = "����";
			else
				status = "����";
			dynatreeNode.setStatus(status);

			nodeList.add(dynatreeNode);
		}

		return nodeList;
	}
	*/

	// ���ݳ���������Ϣ�������ĵ����ڵ�
	private DynatreeNode buildNode(VehicleGroupInfo vehicleGroupInfo) {
		DynatreeNode node = new DynatreeNode();
		node.setKey(vehicleGroupInfo.getName());
		node.setTitle(vehicleGroupInfo.getName());
		if (vehicleGroupInfo.isLeaf()) {
			node.setExpand(false);
			node.setFolder(false);
		} else {
			node.setExpand(true);
			node.setFolder(true);
		}

		return node;
	}

	// ͨ���ݹ鹹�����޲�εĳ���������Խṹ
	private List<DynatreeNode> buildChildNodes(VehicleGroupInfo vehicleGroupInfo) {

		List<DynatreeNode> nodeList = new ArrayList<DynatreeNode>();
		if (vehicleGroupInfo.isLeaf())
			return null;
		else {
			List<VehicleGroupInfo> list = vehicleGroupTreeDao.listChildren(vehicleGroupInfo);
			for (VehicleGroupInfo vehicleGroupInfoinList : list) {
				
				DynatreeNode node = buildNode(vehicleGroupInfoinList);
				boolean isLeaf = vehicleGroupTreeDao.isLeaf(vehicleGroupInfoinList);
				if (!isLeaf)
					node.setChildren(buildChildNodes(vehicleGroupInfoinList));
				nodeList.add(node);
			}
		}
		return nodeList;
	}

	//�������޲�εĳ���������Խṹ������ǰ�������ų�����
	private List<DynatreeNode> buildChildNodes(VehicleGroupInfo vehicleGroupInfo, String currentVehicleGroupName) {
		List<DynatreeNode> nodeList = new ArrayList<DynatreeNode>();
		if (vehicleGroupInfo.isLeaf())
			return null;
		else {
			List<VehicleGroupInfo> list = vehicleGroupTreeDao.listChildren(vehicleGroupInfo, currentVehicleGroupName);
			for (VehicleGroupInfo vehicleGroupInfoinList : list) {
				boolean isLeaf = vehicleGroupTreeDao.isLeaf(vehicleGroupInfoinList);
				vehicleGroupInfoinList.setLeaf(isLeaf);
				DynatreeNode node = buildNode(vehicleGroupInfoinList);
				if (!isLeaf)
					node.setChildren(buildChildNodes(vehicleGroupInfoinList, currentVehicleGroupName));
				nodeList.add(node);
			}
		}
		return nodeList;
	}

	
}
