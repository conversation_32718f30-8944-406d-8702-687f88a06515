<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@taglib prefix="s" uri="/struts-tags"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>车辆分组管理</title>

<link rel="stylesheet" type="text/css"
	href="/808FrontProject/css/user.css">

<script type="text/javascript" src="/808FrontProject/js/lib.jquery.js"></script>

<!-- date component-->
<script type="text/javascript"
	src="/808FrontProject/js/lib.jsdate/jscal2.js"></script>
<script type="text/javascript"
	src="/808FrontProject/js/lib.jsdate/en.js"></script>
<link rel="stylesheet" type="text/css"
	href="/808FrontProject/js/lib.jsdate/jscal2.css">
<link rel="stylesheet" type="text/css"
	href="/808FrontProject/js/lib.jsdate/border-radius.css">
<link rel="stylesheet" type="text/css"
	href="/808FrontProject/js/lib.jsdate/steel.css">

<!-- tree component -->
<link rel='stylesheet' type='text/css'
	href='/808FrontProject/css/ui.dynatree.css'>
<script type="text/javascript" src='/808FrontProject/js/jquery-ui.js'></script>
<script type="text/javascript"
	src='/808FrontProject/js/jquery.cookie.js'></script>
<script type="text/javascript"
	src='/808FrontProject/js/jquery.dynatree.js'></script>
<script type="text/javascript"
	src="/808FrontProject/js/jquery-migrate-1.2.1.js"></script>

<style type="text/css">
.chkbox {
	width: 20px;
}
</style>

<script type="text/javascript">
	$(function() {
		var cal = Calendar.setup({
			onSelect : function(cal) {
				cal.hide()
			},
			showTime : true
		});
		cal.manageFields("date1Btn", "certificateStartDate", "%Y-%m-%d");
		cal.manageFields("date2Btn", "certificateExpireDate", "%Y-%m-%d");
		//设置日期按钮

		var currentVehicleGroupName = $("#vehicleGroupName").val();
		$("#parentVehicleGroupTree")
				.dynatree(
						{
							checkbox : true,
							selectMode : 1,
							initAjax : {
								url : "/808FrontProject/common/showGroupTreeAction",
								data : {
									"currentVehicleGroupName" : currentVehicleGroupName
								},
								dataType : "json",
								type : "post"
							},
							onSelect : function(select, dtNode) {
								var selNodes = dtNode.tree.getSelectedNodes();
								var seltitles = $.map(selNodes, function(node) {
									return node.data.title;
								});
								var selTitleString = seltitles.join(",");
								$("#parentVehicleGroupName")
										.val(selTitleString);

							}
						});
		setTimeout("selectParentGroup()", 1000);
		//构造父车辆树
		
		var treeData = [ {
			title : "道路旅客运输",
			isFolder : true,
			expand : true,
			children : [ {
					title : "班车客运"
				}, {
					title : "包车客运"
				}, {
					title : "定线旅游"
				}, {
					title : "非定线旅游"
				} ]
		} ,{
			title : "道路货物运输",
			isFolder : true,
			expand : true,
			children : [ 
				{title : "普通货物运输"},
				{title : "专用货物运输"},
				{title : "大型货物运输"},
				{title : "危险品货物运输"}
		    ]
		},{
			title : "维修培训",
			isFolder : true,
			expand : true,
			children : [ 
				{title : "机动车维修"},
				{title : "机动车驾驶员培训"},
				{title : "道路运输从业资格证培训"},
				{title : "机动车驾驶教练员培训"}
		    ]
		},{
			title : "其他",
			isFolder : true,
			expand : true,
			children : [ 
				{title : "公交运输"},
				{title : "出租运输"},
				{title : "汽车租赁"},
				{title : "其他"}
		    ]
		}];

		$("#businessScopeTree").dynatree({
			checkbox : true,
			selectMode : 3,
			children : treeData,
			onSelect : function(select, dtNode) {
				var selNodes = dtNode.tree.getSelectedNodes();
				var seltitles = $.map(selNodes, function(node) {
					return node.data.title;
				});
				var selTitleString = seltitles.join(",");
				$("#businessScope").val(selTitleString);
			}
		});

		var businessScopeString = $("#businessScope").val();
		if (businessScopeString != "") {
			var businessScopeSelections = businessScopeString.split(",");
			for (var i = 0; i < businessScopeSelections.length; i++) {
				$("#businessScopeTree").dynatree("getTree").selectKey(
						businessScopeSelections[i]);
			}
		}
		//构建经营范围树
		
		var modifyFlag=$("#vehicleGroupModifyFlag").val();
		if(modifyFlag==1){
	        $("#vehicleGroupName").focus(function(){
		        this.blur();
	        });
		}else{
			$("#vehicleGroupName").blur(function(){
				validateGroupName(this);
			});
			
		}
		
		
			

	});

	function selectParentGroup() {
		var parentGroupName = $("#parentVehicleGroupName").val();
		if (parentGroupName != "")
			$("#parentVehicleGroupTree").dynatree("getTree").selectKey(
					parentGroupName);
	}

	function onSelectChange(obj, scope) {
		if (scope == "city") {
			$.ajax({
				url : "/808FrontProject/common/cityInfoAction.action",
				data : {
					"parentLocation" : obj.value
				},
				dataType : "json",
				success : function(data) {
					createCitySelectOption(data);
				}
			});
		} else if (scope == "area") {
			$.ajax({
				url : "/808FrontProject/common/areaInfoAction.action",
				data : {
					"parentLocation" : obj.value
				},
				dataType : "json",
				success : function(data) {
					createAreaSelectOption(data);
				}
			});
		}

	}

	function createCitySelectOption(data) {
		if (data != null && data.length > 0) {
			$("#city").html("");
			var noneOption = document.createElement("option");
			noneOption.setAttribute("value", "请选择：");
			noneOption.appendChild(document.createTextNode("请选择："));
			$("#city").append(noneOption);
			for (var i = 0; i < data.length; i++) {
				var option = document.createElement("option");
				option.setAttribute("value", data[i]);
				option.appendChild(document.createTextNode(data[i]));
				$("#city").append(option);
			}
		}
	}

	function createAreaSelectOption(data) {
		if (data != null && data.length > 0) {
			$("#area").html("");
			var noneOption = document.createElement("option");
			noneOption.setAttribute("value", "请选择：");
			noneOption.appendChild(document.createTextNode("请选择："));
			$("#area").append(noneOption);
			for (var i = 0; i < data.length; i++) {
				var option = document.createElement("option");
				option.setAttribute("value", data[i]);
				option.appendChild(document.createTextNode(data[i]));
				$("#area").append(option);
			}
		}
	}
	
	function validateGroupName(field){
		if($.trim(field.value)==""){
			$("#validationResultImage").attr("src","/808FrontProject/image/false.png");
		    $("#storeButton").attr("disabled","disabled");
		}else{
			$.ajax({
				url:"/808FrontProject/common/validateVehicleGroupNameAction",
				data:{
					vehicleGroupName:$.trim(field.value)
				},
				type:"post",
				dataType:"json",
				success:function(data){
					if(data=="车组名已存在"){
						$("#validationResultImage").attr("src","/808FrontProject/image/false.png");
						$("#storeButton").attr("disabled","disabled");
					}else{
						$("#validationResultImage").attr("src","/808FrontProject/image/true.png");
						$("#storeButton").removeAttr("disabled");
					}
				}
			});
		}
		
	}

	function store() {
/*		var vgName=$("input[name='vehicleGroupInfo.name']");
		if (vgName.val() == "") {
			alert("请输入车组名称！");
			vgName.focus();
			return false;
		}
		var vgType=$("select[name='vehicleGroupInfo.type']");
		if(vgType.find("option:selected").val()==0){
			alert("请选择车组类型！");
			vgType.focus();
			return false;
		}
		var vgOwner=$("input[name='vehicleGroupInfo.owner']");
		if (vgOwner.val() == "") {
			alert("请输入所属业户！");
			vgOwner.focus();
			return false;
		}
		var jyxkzz=$("input[name='vehicleGroupInfo.certificateCity']");
		if (jyxkzz.val() == "") {
			alert("请输入经营许可证字！");
			jyxkzz.focus();
			return false;
		}
		var jyxkzz=$("input[name='vehicleGroupInfo.certificateNumber']");
		if (jyxkzz.val() == "") {
			alert("请输入经营许可证号！");
			jyxkzz.focus();
			return false;
		}
		var vgContact=$("input[name='vehicleGroupInfo.contact']");
		var vgPhone=$("input[name='vehicleGroupInfo.contactTelephoneNumber']");
		if (vgContact.val() == "") {
			alert("请输入联系人！");
			vgContact.focus();
			return false;
		}
		if (vgPhone.val() == "") {
			alert("请输入联系电话！");
			vgPhone.focus();
			return false;
		}
*/	
		var vehicleGroupArea;
		if ($("#province").val() != "请选择：") {
			vehicleGroupArea = $("#province").val();
			if ($("#city").val() != "请选择：") {
				vehicleGroupArea += $("#city").val();
				if ($("#area").val() != "请选择：")
					vehicleGroupArea += $("#area").val();
			}
			$("#vehicleGroupArea").val(vehicleGroupArea);
		}
		
//		if ($("#vehicleGroupArea").val() == "") {
//			alert("请选择所属地区！");
//			return false;
//		}
		
		if ($("#vehicleGroupModifyFlag").val() == 0)
			$("#modifyVehilceGroupInfoForm").attr("action",
					"/808FrontProject/vehicleGroup/addStoreAction.action")
					.submit();
		else
			$("#modifyVehilceGroupInfoForm").attr("action",
					"/808FrontProject/vehicleGroup/editStoreAction.action")
					.submit();

	}
</script>
</head>

<body>
	<span class="title1">车辆分组管理</span>
	<s:form id="modifyVehilceGroupInfoForm" theme="simple">
		<s:if test='null == vehicleGroupInfo'>
			<s:hidden id="vehicleGroupModifyFlag" value="0">
			</s:hidden>
		</s:if>
		<s:else>
			<s:hidden id="vehicleGroupModifyFlag" value="1">
			</s:hidden>
		</s:else>
		<s:hidden name="vehicleGroupInfo.businessScope" id="businessScope"></s:hidden>
		<s:hidden name="vehicleGroupInfo.parentGroupName"
			id="parentVehicleGroupName"></s:hidden>
		<s:hidden name="vehicleGroupInfo.area" id="vehicleGroupArea"></s:hidden>

		<table class="tableCommon">

			<tr>
				<td width="150px">车组名称</td>
				<td id="groupNameTd"><s:textfield name="vehicleGroupInfo.name"
						id="vehicleGroupName" /> *
						<img id="validationResultImage" src="/808FrontProject/image/blank.png"></td>
				<td width="150px">车组类型</td>
				<td><s:select name="vehicleGroupInfo.type"
						list="#{1:'企业',2:'地区' }" listKey="value" listValue="value"
						headerKey="0" headerValue="请选择"></s:select> </td>
				<td width="150px">所属业户</td>
				<td><s:textfield name="vehicleGroupInfo.owner"></s:textfield> </td>
			</tr>
			<tr>
				<td>经营许可证字</td>
				<td><s:textfield name="vehicleGroupInfo.certificateCity"></s:textfield>
					</td>
				<td>经营许可证号</td>
				<td><s:textfield name="vehicleGroupInfo.certificateNumber"></s:textfield>
					</td>
				<td>服务密码</td>
				<td><s:textfield name="vehicleGroupInfo.password"></s:textfield></td>
			</tr>
			<tr>
				<td>联系人</td>
				<td><s:textfield name="vehicleGroupInfo.contact"></s:textfield>
					</td>
				<td>联系电话</td>
				<td><s:textfield name="vehicleGroupInfo.contactTelephoneNumber"></s:textfield>
					</td>
				<td>组织机构代码</td>
				<td><s:textfield name="vehicleGroupInfo.organizationCode"></s:textfield></td>
			</tr>

			<tr>
				<td>上级组织机构代码</td>
				<td><s:textfield name="vehicleGroupInfo.superOrganizationCode"></s:textfield></td>
				<td>管理组织机构代码</td>
				<td><s:textfield
						name="vehicleGroupInfo.superviseOrganizationCode"></s:textfield></td>
				<td>所属地区</td>
				<td>省份：<select name="province" id="province"
					onchange="onSelectChange(this,'city');">
						<option value="请选择：">请选择：</option>
						<option value="北京市">北京市</option>
						<option value="天津市">天津市</option>
						<option value="河北省">河北省</option>
						<option value="山西省">山西省</option>
						<option value="内蒙古自治区">内蒙古自治区</option>
						<option value="辽宁省">辽宁省</option>
						<option value="吉林省">吉林省</option>
						<option value="黑龙江省">黑龙江省</option>
						<option value="上海市">上海市</option>
						<option value="江苏省">江苏省</option>
						<option value="浙江省">浙江省</option>
						<option value="安徽省">安徽省</option>
						<option value="福建省">福建省</option>
						<option value="江西省">江西省</option>
						<option value="山东省">山东省</option>
						<option value="河南省">河南省</option>
						<option value="湖北省">湖北省</option>
						<option value="湖南省">湖南省</option>
						<option value="广东省">广东省</option>
						<option value="广西壮族自治区">广西壮族自治区</option>
						<option value="海南省">海南省</option>
						<option value="重庆市">重庆市</option>
						<option value="四川省">四川省</option>
						<option value="贵州省">贵州省</option>
						<option value="云南省">云南省</option>
						<option value="西藏自治区">西藏自治区</option>
						<option value="陕西省">陕西省</option>
						<option value="甘肃省">甘肃省</option>
						<option value="青海省">青海省</option>
						<option value="宁夏回族自治区">宁夏回族自治区</option>
						<option value="新疆维吾尔自治区">新疆维吾尔自治区</option>
				</select> 城市：<select name="city" id="city"
					onchange="onSelectChange(this,'area');"></select> 区(县)：<select
					name="area" id="area">
				</select>
				</td>

			</tr>
			<tr>
				<td>许可证有效期起</td>
				<td><s:textfield name="vehicleGroupInfo.certificateStartDate"
						id='certificateStartDate' style='width: 100px; margin-top: 2px'></s:textfield>
					<button id="date1Btn" type="button">...</button></td>
				<td>许可证有效期止</td>
				<td><s:textfield name="vehicleGroupInfo.certificateExpireDate"
						id='certificateExpireDate' style='width: 100px; margin-top: 2px'></s:textfield>
					<button id="date2Btn" type="button">...</button></td>

				<td></td>
				<td></td>
			</tr>
			<tr>
				<td>经营范围</td>
				<td colspan=5>
					<div id="businessScopeTree"
						style="width: 100%; height: 300px; overflow: auto; font-size: 12px;line-height:20px">
					</div>
				</td>
			</tr>
			<!--  
			<tr>
				<td>父车辆组</td>
				<td colspan=5>
					<div id="parentVehicleGroupTree"
						style="width: 100%; height: 300px; overflow: auto; font-size: 12px;line-height:20px">
					</div>
				</td>
			</tr>
            -->
			<tr>
				<td colspan="6" style="padding-left: 0px; text-align: center;"><input
					class="buttonStyle" id="storeButton" type="button" value="保存"
					onclick="store()" />&nbsp;&nbsp;<input
					class="buttonStyle" type="button" value="返回"
					onclick="history.go(-1);" /></td>
			</tr>
		</table>
	</s:form>

</body>
</html>