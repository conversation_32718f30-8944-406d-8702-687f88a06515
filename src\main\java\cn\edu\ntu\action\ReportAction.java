package cn.edu.ntu.action;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.opensymphony.xwork2.ActionSupport;

import cn.edu.ntu.entity.report.DriveReport;
import cn.edu.ntu.entity.report.DriverReport;
import cn.edu.ntu.entity.report.IgnitionReport;
import cn.edu.ntu.entity.report.MileageReport;
import cn.edu.ntu.entity.report.ParkReport;
import cn.edu.ntu.entity.report.VelocityReport;
import cn.edu.ntu.entity.report.WarningReport;
import cn.edu.ntu.service.report.implement.DefaultReportService;
import cn.edu.ntu.service.report.implement.DriveReportServiceImpl;
import cn.edu.ntu.service.report.implement.IgnitionReportServiceImpl;
import cn.edu.ntu.service.report.implement.MileageReportServiceImpl;
import cn.edu.ntu.service.report.implement.ParkReportServiceImpl;
import cn.edu.ntu.service.report.implement.VelocityReportServiceImpl;
import cn.edu.ntu.service.report.interfaces.DriveReportService;
import cn.edu.ntu.service.report.interfaces.DriverReportService;
import cn.edu.ntu.service.report.interfaces.IgnitionReportService;
import cn.edu.ntu.service.report.interfaces.MileageReportService;
import cn.edu.ntu.service.report.interfaces.ParkReportService;
import cn.edu.ntu.service.report.interfaces.VelocityReportService;
import cn.edu.ntu.service.report.interfaces.WarningReportService;

/**
 *
 * 
 * <AUTHOR>
 * @date 2016��7��15�� ����9:08:54 
 */
@Controller
@Scope("prototype")
public class ReportAction extends ActionSupport {

	private String carNumber;
	private String groupName;
	private String startTime;
	private String endTime;
	private String driverName;
	private List<DriveReport> driveReportList;
	private List<IgnitionReport> ignitionReportList;
	private List<ParkReport> parkReportList;
	private List<VelocityReport> velocityReportList;
	private List<MileageReport> mileageReportList;
	private List<WarningReport> warningReportList;
	private List<DriverReport> driverReportList;
	
	@Autowired
	private DriveReportService driveReportService;
	@Autowired
	private IgnitionReportService ignitionReportService;
	@Autowired
	private VelocityReportService velocityReportService;
	@Autowired
	private ParkReportService parkReportService;
	@Autowired
	private WarningReportService warningReportService;
	@Autowired
	private DriverReportService driverReportService;
	@Autowired
	private MileageReportService mileageReportService;
	

	public String getCarNumber() {
		return carNumber;
	}

	public void setCarNumber(String carNumber) {
		this.carNumber = carNumber;
	}

	public String getGroupName() {
		return groupName;
	}

	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}

	public String getStartTime() {
		return startTime;
	}

	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}

	public String getEndTime() {
		return endTime;
	}

	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}

	public List<DriveReport> getDriveReportList() {
		return driveReportList;
	}

	public void setDriveReportList(List<DriveReport> driveReportList) {
		this.driveReportList = driveReportList;
	}
	

	public List<IgnitionReport> getIgnitionReportList() {
		return ignitionReportList;
	}

	public void setIgnitionReportList(List<IgnitionReport> ignitionReportList) {
		this.ignitionReportList = ignitionReportList;
	}
	
	public List<ParkReport> getParkReportList() {
		return parkReportList;
	}

	public void setParkReportList(List<ParkReport> parkReportList) {
		this.parkReportList = parkReportList;
	}
	
	public List<VelocityReport> getVelocityReportList() {
		return velocityReportList;
	}

	public void setVelocityReportList(List<VelocityReport> velocityReportList) {
		this.velocityReportList = velocityReportList;
	}

	public List<MileageReport> getMileageReportList() {
		return mileageReportList;
	}

	public void setMileageReportList(List<MileageReport> mileageReportList) {
		this.mileageReportList = mileageReportList;
	}
	
	public List<WarningReport> getWarningReportList() {
		return warningReportList;
	}

	public void setWarningReportList(List<WarningReport> warningReportList) {
		this.warningReportList = warningReportList;
	}
	
	public String getDriverName() {
		return driverName;
	}

	public void setDriverName(String driverName) {
		this.driverName = driverName;
	}

	public List<DriverReport> getDriverReportList() {
		return driverReportList;
	}

	public void setDriverReportList(List<DriverReport> driverReportList) {
		this.driverReportList = driverReportList;
	}

	public String getDriveReportByCarNumber() throws Exception{
		driveReportList=driveReportService.getDriveReportByCarNumber(carNumber,startTime,endTime);
		return SUCCESS;
	}
	
	public String getDriveReportByGroupName() throws Exception{
		driveReportList=driveReportService.getDriveReportByGroupName(groupName,startTime,endTime);
		return SUCCESS;
	}
	
	public String getIgnitionReportByCarNumber() throws Exception{
		ignitionReportList=ignitionReportService.getIgnitionReportByCarNumber(carNumber,startTime,endTime);
		return SUCCESS;
	}
	
	public String getIgnitionReportByGroupName() throws Exception{
		ignitionReportList=ignitionReportService.getIgnitionReportByGroupName(groupName,startTime,endTime);
		return SUCCESS;
	}
	
	public String getParkReportByCarNumber() throws Exception{
		parkReportList=parkReportService.getParkReportByCarNumber(carNumber,startTime,endTime);
		return SUCCESS;
	}
	
	public String getParkReportByGroupName() throws Exception{
		parkReportList=parkReportService.getParkReportByGroupName(groupName, startTime, endTime);
		return SUCCESS;
	}
	
	public String getVelocityReportByCarNumber() throws Exception{
		
		velocityReportList=velocityReportService.getVelocityReportByCarNumber(carNumber,startTime,endTime);
		return SUCCESS;
	}
	
	public String getVelocityReportByGroupName() throws Exception{
		velocityReportList=velocityReportService.getVelocityReportByGroupName(groupName,startTime,endTime);
		return SUCCESS;
	}
	
	public String getMileageReportByCarNumber() throws Exception{
		mileageReportList=mileageReportService.getMileageReportByCarNumber(carNumber,startTime,endTime);
		return SUCCESS;
	}
	
	public String getMileageReportByGroupName() throws Exception{
		mileageReportList=mileageReportService.getMileageReportByGroupName(groupName,startTime,endTime);
		return SUCCESS;
	}
	
	public String getWarningReportByCarNumber() throws Exception{
		warningReportList=warningReportService.getWarningReportByCarNumber(carNumber,startTime,endTime);
		return SUCCESS;
	}
	
	public String getWarningReportByGroupName() throws Exception{
		warningReportList=warningReportService.getWarningReportByGroupName(groupName, startTime, endTime);
		return SUCCESS;
	}
	
	public String getDriverReportByDriverName() throws Exception{
		driverReportList=driverReportService.getDriverReportByDriverName(driverName,startTime,endTime);
		return SUCCESS;
	}
	
	public String getDriverReportByCarNumber() throws Exception{
		driverReportList=driverReportService.getDriverReportByCarNumber(carNumber,startTime,endTime);
		return SUCCESS;
	}
}
