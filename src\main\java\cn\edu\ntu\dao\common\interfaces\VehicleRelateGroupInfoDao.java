package cn.edu.ntu.dao.common.interfaces;

import java.util.List;

/**
 * Vehicle Relate Group Info DAO Interface
 *
 * <AUTHOR>
 * @date 2016-12-08 11:31:55
 */
public interface VehicleRelateGroupInfoDao {

	public void addStoreVehicleRelateGroup(String carNumber, String vehicleGroupSelection);
	public void deleteVehicleRelateGroup(String carNumber);
	public List<String> showVehiclesByGroup(String groupName);
	public List<String> showSearchedVehiclesByGroupAndCarNumber(String groupName, String keyWord);
	public List<String> showSearchedVehiclesByGroupAndSimCardNumber(String groupName, String keyWord) ;
}
