package cn.edu.ntu.entity.common;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 *
 * 
 * <AUTHOR>
 * @date 2016��10��8�� ����2:55:44 
 */
@Entity
@Table(name="fatiguedrivingparams")
public class FatigueDrivingParams {
	@Id
    @Column(name="simcardnumber",length=20)
	private String simCardNumber;
    @Column(name="continuousdrivetime")
	private int continuousDriveTime;
    @Column(name="totaldrivetimefortheday")
	private int totalDriveTimeForTheDay;
    @Column(name="minbreaktime")
	private int minBreakTime;
    @Column(name="maxparktime")
	private int maxParkTime;
    @Column(name="drivealarmdifferencevalue")
	private int driveAlarmDifferenceValue;//ƣ�ͼ�ʻԤ����ֵ
	
	public int getContinuousDriveTime() {
		return continuousDriveTime;
	}
	public void setContinuousDriveTime(int continuousDriveTime) {
		this.continuousDriveTime = continuousDriveTime;
	}
	public int getTotalDriveTimeForTheDay() {
		return totalDriveTimeForTheDay;
	}
	public void setTotalDriveTimeForTheDay(int totalDriveTimeForTheDay) {
		this.totalDriveTimeForTheDay = totalDriveTimeForTheDay;
	}
	public int getMinBreakTime() {
		return minBreakTime;
	}
	public void setMinBreakTime(int minBreakTime) {
		this.minBreakTime = minBreakTime;
	}
	public int getMaxParkTime() {
		return maxParkTime;
	}
	public void setMaxParkTime(int maxParkTime) {
		this.maxParkTime = maxParkTime;
	}
	public int getDriveAlarmDifferenceValue() {
		return driveAlarmDifferenceValue;
	}
	public void setDriveAlarmDifferenceValue(int driveAlarmDifferenceValue) {
		this.driveAlarmDifferenceValue = driveAlarmDifferenceValue;
	}
	
	
}
