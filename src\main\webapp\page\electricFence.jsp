<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@taglib prefix="s" uri="/struts-tags"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">

<title>电子围栏</title>
<meta http-equiv="pragma" content="no-cache">
<meta http-equiv="cache-control" content="no-cache">
<meta http-equiv="expires" content="0">
<meta http-equiv="keywords" content="太平洋,北斗,位置服务,云服务,车辆定位,车辆查询,车辆导航">
<meta http-equiv="description" content="太平洋北斗位置云">


<link rel="stylesheet" type="text/css"
	href="/808FrontProject/css/searchres.css">

<script type="text/javascript" src="/808FrontProject/js/lib.jquery.js"></script>
<script type="text/javascript" src="/808FrontProject/js/lib.json2.js"></script>

<!-- date component [S] -->
<script type="text/javascript"
	src="/808FrontProject/js/lib.jsdate/jscal2.js"></script>
<script type="text/javascript"
	src="/808FrontProject/js/lib.jsdate/en.js"></script>
<link rel="stylesheet" type="text/css"
	href="/808FrontProject/js/lib.jsdate/jscal2.css">
<link rel="stylesheet" type="text/css"
	href="/808FrontProject/js/lib.jsdate/border-radius.css">
<link rel="stylesheet" type="text/css"
	href="/808FrontProject/js/lib.jsdate/steel.css">
<!-- date component [E] -->

<script type="text/javascript" src="/808FrontProject/js/frame.js"></script>
<script type="text/javascript" src="/808FrontProject/js/tools.js"></script>
<script type="text/javascript" src="/808FrontProject/js/baidumap.js"></script>
<script type="text/javascript" src="/808FrontProject/js/style.js"></script>
<script type="text/javascript" src="/808FrontProject/js/efence.js"></script>
<script type="text/javascript" src="/808FrontProject/js/var.js"></script>
<link rel="stylesheet" href="/808FrontProject/css/user.css" />
<!-- interactive dialog [s] -->
<link rel="stylesheet"
	href="/808FrontProject/js/lib.artDialog/ui-dialog.css" />
<script src="/808FrontProject/js/lib.artDialog/dialog-plus-min.js"></script>
<!-- interactive dialog [e] -->

<!-- dynamic win component [s] -->
<link rel="stylesheet" type="text/css"
	href="/808FrontProject/js/lib.mewin/win.css">
<script type="text/javascript"
	src="/808FrontProject/js/lib.mewin/win.js"></script>
<!-- dynamic win component [e] -->

<script type="text/javascript"
	src="http://api.map.baidu.com/api?v=2.0&ak=6ukSP6o4ff8u9SSnlVEKmiZC"></script>
<script type="text/javascript"
	src="/808FrontProject/js/lib.baidu/BDMapDistanceTool_min.js"></script>

<style type="text/css">
body {
	font-family: 宋体;
}

#gisToolDiv {
	position: absolute;
	top: 0px;
	right: 5px;
	height: 0px;
	width: 0px;
	background-color: #fff;
	z-index: 1;
	font-size: 14px;
	line-height: 30px;
	padding-left: 5px;
}

input {
	font-size: 14px;
}

.commonFontSize {
	font-size: 14px;
}
</style>
<script type="text/javascript">
	var p;

	function showGisToolDiv() {
		$("#gisToolDiv").show();// 轨迹
		$("#gisToolDiv").css("height", "30px");
		$("#gisToolDiv").css("width", "150px");
	}

	var mapFence;
	var winFence;

	$(window).load(function() {
		/**
		p = parseParametersFromURL("p");
		if (p != null) {
			p = decodeUTF8(p);
		}
		$("#carNumber").html(p);
		 **/

		mapFence = new BaiduMap("mapDiv");
		mapFence.init();
		mapFence.locationByBrowser();
		//mapFence.locationByCity("北京市");

		showGisToolDiv();
		//InitInfoTable();
		showMapPanel();

		//loading win [s] ----------------
		winFence = new MapeyeWin("fenceInfo", "fenceName", "fenceForm");
		winFence.init();
		winFence.setImgPath("/808FrontProject/js/");
		//loading win [e] ----------------

		/**
		//date componenent
		var cal1 = Calendar.setup({
			onSelect : function(cal1) {
				cal1.hide()
			},
			showTime : true
		});
		var cal2 = Calendar.setup({
			onSelect : function(cal2) {
				cal2.hide()
			},
			showTime : true
		});
		cal1.manageFields("date1Btn", "searchDate1", "%Y-%m-%d %H:%M");
		cal2.manageFields("date2Btn", "searchDate2", "%Y-%m-%d %H:%M");
		 **/
	});

	$(window).resize(function() {
		showGisToolDiv();
		showMapPanel();
	});
</script>
</head>

<body>
	<div id="gisToolDiv">
		<!-- for drawing fence -->
		<img id="gisrule" src='../image/gisruled.png'
			style='width: 20px; height: 20px; cursor: pointer; padding: 1px; margin-top: 0px; vertical-align: middle;'
			onmouseover="styleImgMOver(this,'/808FrontProject/image/gisruleh.png','鼠标左键点击测距，双击结束')"
			onmouseout="styleImgMOut(this,'/808FrontProject/image/gisruled.png')"
			onclick="openRule();" /> <img id="giscircle"
			src='../image/giscircled.png'
			style='width: 20px; height: 20px; cursor: pointer; padding: 1px; margin-top: 0px; vertical-align: middle;'
			onmouseover="styleImgMOver(this,'/808FrontProject/image/giscircleh.png','鼠标左键点击画圆，右键结束')"
			onmouseout="styleImgMOut(this,'/808FrontProject/image/giscircled.png')"
			onclick="addCircle();" /> <img id="gisrectangle"
			src='../image/gisrectangled.png'
			style='width: 20px; height: 20px; cursor: pointer; padding: 1px; margin-top: 0px; vertical-align: middle;'
			onmouseover="styleImgMOver(this,'/808FrontProject/image/gisrectangleh.png','鼠标左键点击画矩形，右键结束')"
			onmouseout="styleImgMOut(this,'/808FrontProject/image/gisrectangled.png')"
			onclick="addRectangle();" /> <img id="gispolyline"
			src='../image/gispolylined.png'
			style='width: 20px; height: 20px; cursor: pointer; padding: 1px; margin-top: 0px; vertical-align: middle;'
			onmouseover="styleImgMOver(this,'/808FrontProject/image/gispolylineh.png','鼠标左键点击画折线，右键结束')"
			onmouseout="styleImgMOut(this,'/808FrontProject/image/gispolylined.png')"
			onclick="addPolyline();" /> <img id="gispolyline"
			src='../image/gispolygond.png'
			style='width: 20px; height: 20px; cursor: pointer; padding: 1px; margin-top: 0px; vertical-align: middle;'
			onmouseover="styleImgMOver(this,'/808FrontProject/image/gispolygonh.png','鼠标左键点击画多边形，右键结束')"
			onmouseout="styleImgMOut(this,'/808FrontProject/image/gispolygond.png')"
			onclick="addPolygon();" />
	</div>
	<table border=0
		style="width: 100%; height: 100%; border-collapse: collapse; border-spacing: 0px;">
		<tr>
			<td>
				<div id="mapDiv"></div>
			</td>
		</tr>
		<tr>
			<td style="width: 100%; height: 150px; vertical-align: top">
				<div id="fenceInfo" style="width: 100%; height: 130px">
					<div id="fenceForm">
						<div id="fenceParam"
							style="overflow: auto; height: 100%; width: 100%">&nbsp;暂无参数！</div>
						<div id="fenceCds"
							style="overflow: auto; height: 100%; width: 100%">&nbsp;暂无坐标点！</div>
						<div id="fenceManage"
							style="overflow: auto; height: 100%; width: 100%">&nbsp;暂无电子围栏数据！</div>
					</div>
					<ul id="fenceName">
						<li>参数设置</li>
						<li>坐标点</li>
						<li>围栏管理</li>
					</ul>
				</div>
			</td>
		</tr>
	</table>
</body>

</html>
