package cn.edu.ntu.utils.others;

import org.springframework.data.redis.core.RedisTemplate;


public class VehiclePhotoRedisService {

	
	private RedisTemplate<String, VehiclePhoto> redisTemplate;
	
	public void addOrUpdate(VehiclePhoto vehiclePhoto) {  

        redisTemplate.opsForValue().set("vehiclePhoto.endNumber." + vehiclePhoto.getSimCardNumber(), vehiclePhoto);  
    }  
	
	public VehiclePhoto load(String endNumberString) {
		return redisTemplate.opsForValue().get("vehiclePhoto.endNumber." + endNumberString);
	}

	public RedisTemplate<String,VehiclePhoto> getRedisTemplate() {
		return redisTemplate;
	}

	public void setRedisTemplate(RedisTemplate<String, VehiclePhoto> redisTemplate) {
		this.redisTemplate = redisTemplate;
	}
}
