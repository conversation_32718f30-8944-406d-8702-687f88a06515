package cn.edu.ntu.utils.db;

import java.util.Vector;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import net.mapeye.core.*;
/**
*
* <AUTHOR>
* @date 2016.4.18
* Parse the date from the input string
*/
public class DateParseUtil {
	private int year;
	private int mon;
	private int day;
	private int hour;
	private int min;
	private int sec;
	
	public int getYear() {
		return year;
	}


	public int getMon() {
		return mon;
	}


	public int getDay() {
		return day;
	}


	public int getHour() {
		return hour;
	}


	public int getMin() {
		return min;
	}


	public int getSec() {
		return sec;
	}


	public DateParseUtil(String dateStr,boolean mode) {
		// dateFormat: yyyy-mm-dd hh:mm:ss
		// or: yyyymmddhhmmss

		// subtract the separator character
		dateStr = dateStr.trim();
		char[] c = dateStr.toCharArray();
		Pattern pN = Pattern.compile("[0-9]");// match the number
		Matcher mN = null;
		String cstr = "";
		Vector<String> vecChr = new Vector<String>();
		Vector<Integer> vecPos = new Vector<Integer>();
		for (int i = 0; i < c.length; i++) {
			cstr = String.valueOf(c[i]);
			mN = pN.matcher(cstr);
			if (!mN.find()) {
				vecChr.add(cstr);
				vecPos.add(i);
			}
		}
		int p, q;
		String[] dateArr = new String[6];
		if (vecChr.size() > 0) {
			// date format: yyyy-mm-dd hh:mm:ss
			p = 0;
			q = 0;
			vecPos.add(dateStr.length());
			for (int v = 0; v < vecPos.size(); v++) {
				q = vecPos.get(v);
				if (q <= dateStr.length()) {
					dateArr[v] = dateStr.substring(p, q);
				}

				p = q + 1;
			}
		} else {
			// date format: yyyymmddhhmmss
			p = 0;
			q = 0;
			for (int j = 0; j < dateArr.length; j++) {
				if (j == 0) {
					q = p + 4;
				} else {
					q = p + 2;
				}
				if (q <= dateStr.length()) {
					dateArr[j] = dateStr.substring(p, q);
				}
				p = q;
			}
		}
		this.year = Integer.parseInt(dateArr[0]!=null ? dateArr[0] : "-1");
		this.mon = Integer.parseInt(dateArr[1]!=null ? dateArr[1] : "-1");
		this.day = Integer.parseInt(dateArr[2]!=null ? dateArr[2] : "-1");
		this.hour = Integer.parseInt(dateArr[3]!=null ? dateArr[3] : "-1");
		this.min = Integer.parseInt(dateArr[4]!=null ? dateArr[4] : "-1");
		this.sec = Integer.parseInt(dateArr[5]!=null ? dateArr[5] : "-1");
		
		
		if(mode&&((this.hour==0&&this.min==0&&this.sec==0)||(this.hour==0&&this.min==0&&this.sec==-1))){
			this.day--;
			this.hour=23;
			this.min=59;
			this.sec=59;
		}
	}

	
	public String getDate() {
		return getDate("-");
	}
	
	public String getAfterDate(){
		return getAfterDate("-");
	}

	private String getDate(String separator) {
		String date = CommonTools.getDateNormal(year, mon, day, separator);
		return date;
	}
	
	private String getAfterDate(String separator){
		String date=CommonTools.getDateNormal(year, mon, day+1, separator);
		return date;
	}


	
//	public void printDate() {
//		System.out.println(year + "-" + mon + "-" + day + " " + hour + ":" + min + ":" + sec);
//	}

//	private String[] newArray(int n) {
//		String arr[] = new String[n];
//		for (int i = 0; i < arr.length; i++) {
//			arr[i] = "";
//		}
//		return arr;
//	}

//	public static void main(String[] args) {
//		// test the method
//		String str1 = "2016-01-12 12:14:13";
//		String str2 = "2016-01-12 12:14:";
//		String str3 = "2016-01-12 12";
//		String str4 = "2016-01-12";
//		String str5 = "2016-01";
//		String str6 = "20160112212002";
//		String str7 = "201601122120";
//		String str8 = "20160112";
//		DateParseUtil dpu = new DateParseUtil(str1);
//		System.out.println(dpu.getDate());
//		
//	}

}
