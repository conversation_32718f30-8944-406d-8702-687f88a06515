package cn.edu.ntu.entity.common;

import java.util.Date;

import org.apache.struts2.json.annotations.JSON;

public class AreaSetting {

	private String areaName;
	private int geometryType;
	private Date createTime;
	private String creator;
	
	public String getAreaName() {
		return areaName;
	}
	public void setAreaName(String areaName) {
		this.areaName = areaName;
	}
	public int getGeometryType() {
		return geometryType;
	}
	public void setGeometryType(int geometryType) {
		this.geometryType = geometryType;
	}
	@JSON(format="yyyy-MM-dd")
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public String getCreator() {
		return creator;
	}
	public void setCreator(String creator) {
		this.creator = creator;
	}
	
	
}
