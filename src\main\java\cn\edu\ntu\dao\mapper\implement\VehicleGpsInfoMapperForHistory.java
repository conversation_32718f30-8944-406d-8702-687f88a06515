package cn.edu.ntu.dao.mapper.implement;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.Date;

import cn.edu.ntu.dao.mapper.interfaces.ObjectMapper;
import cn.edu.ntu.entity.common.VehicleGpsInfoForHistory;

/**
 * Vehicle GPS Info Mapper for History
 *
 * <AUTHOR>
 * @date 2016-07-28 11:34:44
 */
public class VehicleGpsInfoMapperForHistory implements ObjectMapper {

	@Override
	public Object rowMapper(ResultSet rs) throws SQLException {
        VehicleGpsInfoForHistory vehicleGpsInfo=new VehicleGpsInfoForHistory();
        
        vehicleGpsInfo.setSimCardNumber(rs.getString("simcardnumber"));;
        Timestamp gpsTimestamp=rs.getTimestamp("gpstime");
        vehicleGpsInfo.setGpsTime(new Date(gpsTimestamp.getTime()));
        Timestamp lastRunTimestamp=rs.getTimestamp("lastruntime");
        if(lastRunTimestamp!=null)
            vehicleGpsInfo.setLastRunTime(new Date(lastRunTimestamp.getTime()));
        Timestamp parkTimestamp=rs.getTimestamp("stoptime");
        if(parkTimestamp!=null)
            vehicleGpsInfo.setParkTime(new Date(parkTimestamp.getTime()));
		float lon=rs.getFloat("lon");
		vehicleGpsInfo.setLon(lon);
		float lat=rs.getFloat("lat");
		vehicleGpsInfo.setLat(lat);
		vehicleGpsInfo.setAlt(rs.getFloat("alt"));
		vehicleGpsInfo.setSpeed(rs.getFloat("speed"));
		vehicleGpsInfo.setDirection(rs.getInt("direction"));
		vehicleGpsInfo.setState(rs.getInt("state"));
		vehicleGpsInfo.setWarningSign(rs.getInt("warningsign"));
		vehicleGpsInfo.setTotalDistance(rs.getInt("totaldistance"));
		
		return vehicleGpsInfo;
	}

}
