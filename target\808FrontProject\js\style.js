//the method 'styleSearchImgMOver()' is applied to the track.jsp page
//function styleSearchImgMOver(obj) {
//	var jqObj = $(obj);
//	jqObj.attr("src", "/808FrontProject/image/search-sh.png");
//	jqObj.attr("title", "点击进行查找！");
//}
//// the method 'styleSearchImgMOut()' is applied to the track.jsp page
//function styleSearchImgMOut(obj) {
//	var jqObj = $(obj);
//	jqObj.attr("src", "/808FrontProject/image/search-s.png");
//}

var cellHoverColor="#c4ddf6";
var cellDefaultColor="#ffffff";

var textHoverColor="#ff0000";
var textDefaultColor="#000000";

function styleImgMOver(obj, img, title) {
	var jqObj = $(obj);
	jqObj.attr("src", img);
	jqObj.attr("title", title);
}

function styleImgMOut(obj, img) {
	var jqObj = $(obj);
	jqObj.attr("src", img);
}

function styleDateImgMOver(imgId) {
	$("#" + imgId).attr("src", "../image/dateh.png");
	$("#" + imgId).attr("title", "点击选择时间！");
}
function styleDateImgMOut(imgId) {
	$("#" + imgId).attr("src", "../image/date.png");
}

/**
// the method 'styleResMOver()' is applied to the track.jsp page
function styleResMOver(trObj) {
	trObj.className = "mouseOver";
	// retain the style of the previous selected row
	var ior = vehicleIdxGV;
	if (ior > 0) {
		var tabObj = trObj.parentNode;
		var trs = tabObj.childNodes;
		trs[ior].className = "mouseSelect";
	}
}
// the method 'styleResMOut()' is applied to the track.jsp page
function styleResMOut(trObj) {
	trObj.className = "mouseOut";
	// retain the style of the previous selected row
	var ior = vehicleIdxGV;
	if (ior > 0) {
		var tabObj = trObj.parentNode;
		var trs = tabObj.childNodes;
		trs[ior].className = "mouseSelect";
	}
}

function styleResDefault(trObj) {
	trObj.className = "mouseOut";
	vehicleIdxGV = 0;
}
**/

//主页右下角信息栏按钮样式
function styleInfoMOver(obj) {
	// alert("Calling the styleInfoMOver()!");
	var jqObj = $(obj);
	jqObj.css("cursor", "pointer");
	jqObj.css("background-color", "#ccc");
}

function styleInfoMOut(obj) {
	var jqObj = $(obj);
	jqObj.css("background-color", "#eee");
}



function styleCellOnMouseOver(obj, color) {
	//alert("Calling the styleCellMOver()!");
	var jqObj = $(obj);
	//jqObj.css("cursor", "pointer");
	jqObj.css("background-color", color);
}

function styleCellOnMouseOut(obj, color) {
	//alert("Calling the styleCellMOut()!");
	var jqObj = $(obj);
	jqObj.css("background-color", color);
}

function styleTextOnMouseOver(obj, color) {
	//alert("Calling the styleCellMOver()!");
	var jqObj = $(obj);
	//jqObj.css("cursor", "pointer");
	jqObj.css("color", color);
}

function styleTextOnMouseOut(obj, color) {
	//alert("Calling the styleCellMOut()!");
	var jqObj = $(obj);
	jqObj.css("color", color);
}

function styleCursorOnMouseOver(obj){
	var jqObj = $(obj);
	jqObj.css("cursor", "pointer");
}