function MapeyeChart(t){this.canvasId=t;var a={left:30,right:30,top:30,bottom:60};var r=document.getElementById(t);r.innerHTML="";var e=r.offsetWidth;var n=r.offsetHeight;var l=d3.select("body").select("#"+t).append("svg");l.attr("width",e);l.attr("height",n);this.addBorder=function(){r.className="border"};var s=50;var o=10;this.bar=function(t,r,s,o){var i=t.length;var c=d3.scale.ordinal().domain(d3.range(i)).rangeRoundBands([0,e-a.left-a.right]);var d=d3.scale.linear().domain([0,d3.max(t)]).range([n-a.top-a.bottom,0]);var f=d3.scale.ordinal().domain(d3.range(i)).range(r);var u=2;var p=l.selectAll("rect").data(t).enter().append("rect");p.attr("transform","translate("+a.left+","+a.top+")");p.attr("y",function(t){return d(t)});p.attr("x",function(t,a){return c(a)+u/2});p.attr("height",function(t){return n-a.top-a.bottom-d(t)});p.attr("width",c.rangeBand()-u);p.attr("fill","steelblue");var x=l.selectAll(".chart").data(t).enter().append("text");x.attr("class","chart");x.attr("transform","translate("+a.left+","+a.top+")");x.attr("x",function(t,a){return c(a)+u/2});x.attr("y",function(t){return d(t)});x.attr("dx",function(){return(c.rangeBand()-u)/2});x.attr("dy",function(t){return 20});x.text(function(t){return t});var v=l.selectAll(".dataLabel").data(r).enter().append("text");v.attr("class","dataLabel");v.attr("transform","translate("+a.left+","+a.top+")");v.attr("x",function(t,a){return c(a)+u/2});v.attr("y",n-a.bottom);v.attr("dx",function(){return(c.rangeBand()-u)/2});v.attr("dy",function(t){return-10});v.text(function(t){return f(t)});var m=d3.svg.axis().scale(c).orient("bottom").ticks(i);m.tickPadding(n);var g=d3.svg.axis().scale(d).orient("left").tickFormat(d3.format(".0f")).ticks(5);var h=l.append("g");h.attr("class","axis");h.attr("transform","translate("+a.left+","+(n-a.bottom)+")");h.attr("fill","white");h.call(m);var y=l.append("g");y.attr("class","axis");y.attr("transform","translate("+a.left+","+a.top+")");y.call(g);var b=l.selectAll(".axisyUnit").data(t).enter().append("text");b.attr("class","axisyUnit");b.attr("text-anchor","start");b.attr("transform","translate("+a.left/3+","+a.top*2/3+")");b.text(s);var A=l.selectAll(".axisxUnit").data(t).enter().append("text");A.attr("class","axisxUnit");A.attr("text-anchor","middle");A.attr("transform","translate("+e/2+","+(n-a.bottom/4)+")");A.text(o)};this.line=function(t,r,s,o){var i=t.length;var c=d3.scale.ordinal().domain(d3.range(i)).rangeRoundBands([0,e-a.left-a.right]);var d=d3.scale.linear().domain([0,d3.max(t)]).range([n-a.top-a.bottom,0]);var f=d3.scale.ordinal().domain(d3.range(i)).range(r);var u=d3.svg.line();u.x(function(t,a){return c(a)});u.y(function(t){return d(t)});var p=l.selectAll("path").data(t).enter().append("path");p.attr("d",u(t));p.attr("transform","translate("+(a.left+c.rangeBand()/2)+","+a.top+")");p.style("fill","steelblue");p.style("fill","none");p.style("stroke-width",2);p.style("stroke","steelblue");p.style("stroke-opacity",1);var x=l.selectAll("circle").data(t).enter().append("circle");x.attr("cx",function(t,a){return c(a)});x.attr("cy",function(t){return d(t)});x.attr("transform","translate("+(a.left+c.rangeBand()/2)+","+a.top+")");x.attr("r",5);x.attr("fill","steelblue");var v=2;var m=l.selectAll(".chart").data(t).enter().append("text");m.attr("class","chart");m.attr("transform","translate("+a.left+","+a.top+")");m.attr("x",function(t,a){return c(a)});m.attr("y",function(t){return d(t)});m.attr("dx",function(){return c.rangeBand()/2});m.attr("dy",function(t){return 20});m.text(function(t){return t});var g=l.selectAll(".dataLabel").data(r).enter().append("text");g.attr("class","dataLabel");g.attr("transform","translate("+a.left+","+a.top+")");g.attr("x",function(t,a){return c(a)});g.attr("y",n-a.bottom);g.attr("dx",function(){return(c.rangeBand()-v)/2});g.attr("dy",function(t){return-10});g.text(function(t){return f(t)});var h=d3.svg.axis().scale(c).orient("bottom").ticks(i);h.tickPadding(n);var y=d3.svg.axis().scale(d).orient("left").tickFormat(d3.format(".0f")).ticks(5);var b=l.append("g");b.attr("class","axis");b.attr("transform","translate("+a.left+","+(n-a.bottom)+")");b.attr("fill","white");b.call(h);var A=l.append("g");A.attr("class","axis");A.attr("transform","translate("+a.left+","+a.top+")");A.call(y);var k=l.selectAll(".axisyUnit").data(t).enter().append("text");k.attr("class","axisyUnit");k.attr("text-anchor","start");k.attr("transform","translate("+a.left/3+","+a.top*2/3+")");k.text(s);var B=l.selectAll(".axisxUnit").data(t).enter().append("text");B.attr("class","axisxUnit");B.attr("text-anchor","middle");B.attr("transform","translate("+e/2+","+(n-a.bottom/4)+")");B.text(o)};this.lineDyn=function(t,r,s,o){var i=t.length;var c=d3.scale.ordinal().domain(d3.range(i)).rangeRoundBands([0,e-a.left-a.right]);var d=d3.scale.linear().domain([0,d3.max(t)]).range([n-a.top-a.bottom,0]);var f=d3.scale.ordinal().domain(d3.range(i)).range(r);var u=1;var p=l.selectAll("rect").data(t).enter().append("rect");p.attr("y",function(t){return d(t)});p.attr("x",function(t,a){return c(a)});p.attr("id",function(t,a){return"rect"+a});p.attr("transform","translate("+(a.left+c.rangeBand()/2)+","+a.top+")");p.attr("height",function(t){return n-a.top-a.bottom-d(t)});p.attr("width",u);p.attr("fill","none");p.on("mouseover",function(t,a){l.select("#circle"+a).attr("fill","red");l.select("#text"+a).attr("fill","red");d3.select(this).attr("fill","red")});p.on("mouseout",function(t,a){l.select("#circle"+a).attr("fill","steelblue");l.select("#text"+a).attr("fill","none");d3.select(this).attr("fill","none")});var x=d3.svg.line();x.x(function(t,a){return c(a)});x.y(function(t){return d(t)});var v=l.selectAll("path").data(t).enter().append("path");v.attr("d",x(t));v.attr("transform","translate("+(a.left+c.rangeBand()/2)+","+a.top+")");v.style("fill","steelblue");v.style("fill","none");v.style("stroke-width",1);v.style("stroke","steelblue");v.style("stroke-opacity",1);var m=l.selectAll("circle").data(t).enter().append("circle");m.attr("cx",function(t,a){return c(a)});m.attr("cy",function(t){return d(t)});m.attr("id",function(t,a){return"circle"+a});m.attr("transform","translate("+(a.left+c.rangeBand()/2)+","+a.top+")");m.attr("r",4);m.attr("fill","steelblue");m.on("mouseover",function(t,a){l.select("#rect"+a).attr("fill","red");l.select("#text"+a).attr("fill","red");d3.select(this).attr("fill","red")});m.on("mouseout",function(t,a){l.select("#rect"+a).attr("fill","none");l.select("#text"+a).attr("fill","none");d3.select(this).attr("fill","steelblue")});var g=l.selectAll(".chart").data(t).enter().append("text");g.attr("class","chart");g.attr("transform","translate("+a.left+","+a.top+")");g.attr("id",function(t,a){return"text"+a});g.attr("x",function(t,a){return c(a)});g.attr("y",function(t){return d(t)});g.attr("dx",function(){return c.rangeBand()/2-10});g.attr("dy",function(t){return 20});g.text(function(t){return t});g.attr("fill","none");var h=l.selectAll(".dataLabel").data(r).enter().append("text");h.attr("class","dataLabel");h.attr("transform","translate("+a.left+","+a.top+")");h.attr("x",function(t,a){return c(a)});h.attr("y",n-a.bottom);h.attr("dx",function(){return c.rangeBand()/2});h.attr("dy",function(t){return-10});h.text(function(t){return f(t)});var y=d3.svg.axis().scale(c).orient("bottom").ticks(i);y.tickPadding(n);var b=d3.svg.axis().scale(d).orient("left").tickFormat(d3.format(".0f")).ticks(5);var A=l.append("g");A.attr("class","axis");A.attr("transform","translate("+a.left+","+(n-a.bottom)+")");A.attr("fill","white");A.call(y);var k=l.append("g");k.attr("class","axis");k.attr("transform","translate("+a.left+","+a.top+")");k.call(b);var B=l.selectAll(".axisyUnit").data(t).enter().append("text");B.attr("class","axisyUnit");B.attr("text-anchor","start");B.attr("transform","translate("+a.left/3+","+a.top*2/3+")");B.text(s);var U=l.selectAll(".axisxUnit").data(t).enter().append("text");U.attr("class","axisxUnit");U.attr("text-anchor","middle");U.attr("transform","translate("+e/2+","+(n-a.bottom/4)+")");U.text(o)};this.dualAxisLineDyn=function(t,r,i,c,d,f,u,p){var x=t.length;var v=d3.scale.ordinal().domain(d3.range(x)).rangeRoundBands([0,e-a.left-a.right]);var m=d3.scale.linear().domain([0,d3.max(t)]).range([n-a.top-a.bottom,0]);var g=d3.scale.linear().domain([0,d3.max(r)]).range([n-a.top-a.bottom,0]);var h=d3.scale.ordinal().domain(d3.range(x)).range(i);var y=1;var b=l.selectAll().data(t).enter().append("rect");b.attr("transform","translate("+(a.left+v.rangeBand()/2)+","+a.top+")");b.attr("y",function(t){return m(t)});b.attr("x",function(t,a){return v(a)});b.attr("id",function(t,a){return"rectp"+a});b.attr("height",function(t){return n-a.top-a.bottom-m(t)});b.attr("width",y);b.attr("fill","none");b.on("mouseover",function(t,a){l.select("#circlep"+a).attr("fill","red");l.select("#textp"+a).attr("fill","red");d3.select(this).attr("fill","red")});b.on("mouseout",function(t,a){l.select("#circlep"+a).attr("fill","steelblue");l.select("#textp"+a).attr("fill","none");d3.select(this).attr("fill","none")});var A=d3.svg.line();A.x(function(t,a){return v(a)});A.y(function(t){return m(t)});var k=l.selectAll().data(t).enter().append("path");k.attr("d",A(t));k.attr("transform","translate("+(a.left+v.rangeBand()/2)+","+a.top+")");k.style("fill","steelblue");k.style("fill","none");k.style("stroke-width",1);k.style("stroke","steelblue");k.style("stroke-opacity",1);var B=l.selectAll().data(t).enter().append("circle");B.attr("cx",function(t,a){return v(a)});B.attr("cy",function(t){return m(t)});B.attr("id",function(t,a){return"circlep"+a});B.attr("transform","translate("+(a.left+v.rangeBand()/2)+","+a.top+")");B.attr("r",4);B.attr("fill","steelblue");B.on("mouseover",function(t,a){l.select("#rectp"+a).attr("fill","red");l.select("#textp"+a).attr("fill","red");d3.select(this).attr("fill","red")});B.on("mouseout",function(t,a){l.select("#rectp"+a).attr("fill","none");l.select("#textp"+a).attr("fill","none");d3.select(this).attr("fill","steelblue")});var U=l.selectAll().data(t).enter().append("text");U.attr("class","chart");U.attr("transform","translate("+a.left+","+a.top+")");U.attr("id",function(t,a){return"textp"+a});U.attr("x",function(t,a){return v(a)});U.attr("y",function(t){return m(t)});U.attr("dx",function(){return v.rangeBand()/2-8});U.attr("dy",function(t){return 15});U.text(function(t){return t});U.attr("fill","none");var w=l.selectAll().data(r).enter().append("rect");w.attr("transform","translate("+(a.left+v.rangeBand()/2)+","+a.top+")");w.attr("y",function(t){return g(t)});w.attr("x",function(t,a){return v(a)});w.attr("id",function(t,a){return"rects"+a});w.attr("height",function(t){return n-a.top-a.bottom-g(t)});w.attr("width",y);w.attr("fill","none");w.on("mouseover",function(t,a){l.select("#circles"+a).attr("fill","#D200D2");l.select("#texts"+a).attr("fill","#D200D2");d3.select(this).attr("fill","#D200D2")});w.on("mouseout",function(t,a){l.select("#circles"+a).attr("fill","green");l.select("#texts"+a).attr("fill","none");d3.select(this).attr("fill","none")});var F=d3.svg.line();F.x(function(t,a){return v(a)});F.y(function(t){return g(t)});var D=l.selectAll().data(r).enter().append("path");D.attr("d",F(r));D.attr("transform","translate("+(a.left+v.rangeBand()/2)+","+a.top+")");D.style("fill","green");D.style("fill","none");D.style("stroke-width",1);D.style("stroke","green");D.style("stroke-opacity",1);var L=l.selectAll().data(r).enter().append("circle");L.attr("cx",function(t,a){return v(a)});L.attr("cy",function(t){return g(t)});L.attr("id",function(t,a){return"circles"+a});L.attr("transform","translate("+(a.left+v.rangeBand()/2)+","+a.top+")");L.attr("r",4);L.attr("fill","green");L.on("mouseover",function(t,a){l.select("#rects"+a).attr("fill","#D200D2");l.select("#texts"+a).attr("fill","#D200D2");d3.select(this).attr("fill","#D200D2")});L.on("mouseout",function(t,a){l.select("#rects"+a).attr("fill","none");l.select("#texts"+a).attr("fill","none");d3.select(this).attr("fill","green")});var E=l.selectAll().data(r).enter().append("text");E.attr("class","chart");E.attr("transform","translate("+a.left+","+a.top+")");E.attr("id",function(t,a){return"texts"+a});E.attr("x",function(t,a){return v(a)});E.attr("y",function(t){return g(t)});E.attr("dx",function(){return v.rangeBand()/2-8});E.attr("dy",function(t){return 15});E.text(function(t){return t});E.attr("fill","none");var P=l.selectAll(".dataLabel").data(i).enter().append("text");P.attr("class","dataLabel");P.attr("transform","translate("+a.left+","+a.top+")");P.attr("x",function(t,a){return v(a)});P.attr("y",n-a.bottom);P.attr("dx",function(){return v.rangeBand()/2});P.attr("dy",function(t){return-10});P.text(function(t){return h(t)});var R=d3.svg.axis().scale(v).orient("bottom").ticks(x);R.tickPadding(n);var _=d3.svg.axis().scale(m).orient("left").tickFormat(d3.format(".0f")).ticks(5);var C=d3.svg.axis().scale(g).orient("right").tickFormat(d3.format(".0f")).ticks(5);var H=l.append("g");H.attr("class","axis");H.attr("transform","translate("+a.left+","+(n-a.bottom)+")");H.attr("fill","white");H.call(R);var I=l.append("g");I.attr("class","axis");I.attr("transform","translate("+a.left+","+a.top+")");I.call(_);var M=l.append("g");M.attr("class","axis");M.attr("transform","translate("+(e-a.right)+","+a.top+")");M.call(C);var N=l.selectAll().data(t).enter().append("text");N.attr("class","axisyUnit");N.attr("text-anchor","start");N.attr("transform","translate("+a.left/3+","+a.top*2/3+")");N.text(f);var T=l.selectAll().data(r).enter().append("text");T.attr("class","axisyUnit");T.attr("text-anchor","end");T.attr("x",e-u.length*2);T.attr("y",a.top*2/3);T.text(u);var W=l.selectAll().data(t).enter().append("text");W.attr("class","axisxUnit");W.attr("text-anchor","middle");W.attr("transform","translate("+e/2+","+(n-a.bottom/4)+")");W.text(p);var j=l.selectAll().data(t).enter().append("rect");var q=a.left+v.rangeBand()/2;var z=n-a.bottom/3;var G=n-a.bottom/5;j.attr("transform","translate("+q+","+z+")");j.attr("height",o);j.attr("width",s);j.attr("fill","steelblue");var J=l.selectAll().data(t).enter().append("text");J.attr("class","axisxUnit");J.attr("text-anchor","start");J.attr("transform","translate("+(q+s)+","+G+")");J.text(c);var K=l.selectAll().data(t).enter().append("rect");var O=q+s+c.length*20;K.attr("transform","translate("+O+","+z+")");K.attr("height",o);K.attr("width",s);K.attr("fill","green");var Q=l.selectAll().data(t).enter().append("text");Q.attr("class","axisxUnit");Q.attr("text-anchor","start");Q.attr("transform","translate("+(O+s)+","+G+")");Q.text(d)};this.multiLineDyn=function(t,r,i,c,d){var f=t.length;var u=t[0].length;var p=[];for(var x=0;x<f;x++){p[x]=d3.max(t[x])}var v=d3.max(p);var m=d3.scale.ordinal().domain(d3.range(u)).rangeRoundBands([0,e-a.left-a.right]);var g=d3.scale.linear().domain([0,v]).range([n-a.top-a.bottom,0]);var h=d3.scale.ordinal().domain(d3.range(u)).range(r);var y=["#4169E1","#87CEFA","#1E90FF","#6495ED","#0072E3","#0080FF","#2894FF","#46A3FF","#66B3FF","#84C1FF"];if(f>y.length){alert("折线图仅支持"+y.length+"列数据！");return false}var b=0;var A=0;var k=0;for(var x=0;x<f;x++){var B="r"+x+"_";var U="c"+x+"_";var w="t"+x+"_";var F=t[x];var D=1;var L=l.selectAll().data(F).enter().append("rect");L.attr("y",function(t){return g(t)});L.attr("x",function(t,a){return m(a)});L.attr("id",function(t,a){return B+a});L.attr("transform","translate("+(a.left+m.rangeBand()/2)+","+a.top+")");L.attr("height",function(t){return n-a.top-a.bottom-g(t)});L.attr("width",D);L.attr("fill","none");var E=d3.svg.line();E.x(function(t,a){return m(a)});E.y(function(t){return g(t)});var P=l.selectAll().data(F).enter().append("path");P.attr("d",E(F));P.attr("transform","translate("+(a.left+m.rangeBand()/2)+","+a.top+")");P.style("fill",y[x]);P.style("fill","none");P.style("stroke-width",1);P.style("stroke",y[x]);P.style("stroke-opacity",1);var R=l.selectAll().data(F).enter().append("circle");R.attr("cx",function(t,a){return m(a)});R.attr("cy",function(t){return g(t)});R.attr("id",function(t,a){return U+a});R.attr("transform","translate("+(a.left+m.rangeBand()/2)+","+a.top+")");R.attr("r",4);R.attr("fill",y[x]);R.on("mouseover",function(t,a){var r=d3.select(this).attr("id");var e=r.substring(1,r.length);l.select("#r"+e).attr("fill","red");l.select("#t"+e).attr("fill","red");d3.select(this).attr("fill","red")});R.on("mouseout",function(t,a){var r=d3.select(this).attr("id");var e=r.substring(1,r.length);var n=e.split("_")[0];l.select("#r"+e).attr("fill","none");l.select("#t"+e).attr("fill","none");d3.select(this).attr("fill",y[n])});var _=l.selectAll().data(F).enter().append("text");_.attr("class","chart");_.attr("transform","translate("+a.left+","+a.top+")");_.attr("id",function(t,a){return w+a});_.attr("x",function(t,a){return m(a)});_.attr("y",function(t){return g(t)});_.attr("dx",function(){return m.rangeBand()/2-10});_.attr("dy",function(t){return 20});_.text(function(t){return t});_.attr("fill","none");var C=l.selectAll().data(F).enter().append("rect");if(x==0){b=a.left+m.rangeBand()/2}else{b=b+s+i[x].length*20}A=n-a.bottom/3;k=n-a.bottom/5;C.attr("transform","translate("+b+","+A+")");C.attr("height",o);C.attr("width",s);C.attr("fill",y[x]);var H=l.selectAll().data(F).enter().append("text");H.attr("class","axisxUnit");H.attr("text-anchor","start");H.attr("transform","translate("+(b+s)+","+k+")");H.text(i[x])}var I=l.selectAll(".dataLabel").data(r).enter().append("text");I.attr("class","dataLabel");I.attr("transform","translate("+a.left+","+a.top+")");I.attr("x",function(t,a){return m(a)});I.attr("y",n-a.bottom);I.attr("dx",function(){return m.rangeBand()/2});I.attr("dy",function(t){return-10});I.text(function(t){return h(t)});var M=d3.svg.axis().scale(m).orient("bottom").ticks(u);M.tickPadding(n);var N=d3.svg.axis().scale(g).orient("left").tickFormat(d3.format(".0f")).ticks(5);var T=l.append("g");T.attr("class","axis");T.attr("transform","translate("+a.left+","+(n-a.bottom)+")");T.attr("fill","white");T.call(M);var W=l.append("g");W.attr("class","axis");W.attr("transform","translate("+a.left+","+a.top+")");W.call(N);var j=l.selectAll(".axisyUnit").data(F).enter().append("text");j.attr("class","axisyUnit");j.attr("text-anchor","start");j.attr("transform","translate("+a.left/3+","+a.top*2/3+")");j.text(c);var q=l.selectAll().data(F).enter().append("text");q.attr("class","axisxUnit");q.attr("text-anchor","middle");q.attr("transform","translate("+(e-d.length*20)+","+(n-a.bottom/4)+")");q.text(d)}}