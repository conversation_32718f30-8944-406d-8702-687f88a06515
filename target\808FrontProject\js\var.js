﻿var latArray;//重点监控时用来记录各个时刻纬度的数组，下同
var lonArray;
var labelArray;
var infoArray;

var dataArray;// 车辆跟踪时用来显示信息栏的json数据数组

var monitoredArray=[];//被监控车辆的车牌号组成的数组
var selectedArray=[];//被选择车辆的车牌号组成的数组

var refreshCount=0;

var positionEnquireInterval;//点名定时器
var textMessageDeliveryInterval;//下发文本信息定时器
//var immediatelyPhotoInterval;//拍照定时器
var submitIntervalInterval;//临时跟踪定时器
//var getMultiMediaEventInfoInterval;//获取多媒体事件信息定时器
var terminalResetCommandInterval;//终端复位定时器
var nPositionEnquireInterval;//群发点名定时器
var nImmediatelyPhotoInterval;//群发拍照定时器

var serverParamsCommandInterval;//查询/设置服务器参数定时器
var icCardAuthenticationParamsCommandInterval;//查询/设置id卡认证参数定时器
var posParamsCommandInterval;//查询/设置位置汇报参数定时器
var terminalParamsCommandInterval;//查询/设置终端电话参数定时器
var overSpeedParamsCommandInterval;//查询/设置超速参数定时器
var fatigueDrivingParamsCommandInterval;//查询/设置疲劳驾驶参数定时器
var licenseParamsCommandInterval;//查询/设置车牌参数定时器

var simCardString=""; //sim卡号
var commandSerialNumber=0;//命令流水号

var commandResult=0;//命令执行结果，0为正在执行，1为执行成功
var commandResults=0;//多命令执行结果，0为正在执行，1为执行成功

var shotNumberMap;//=new hashMap();
var immediatelyPhotoIntervalMap;//=new hashMap();
var multiMediaInfoIntervalMap;//=new hashMap();


var jsonTree;

var groupStatusRefreshIndicator;

var queryMessageRefreshIndicator=1;
var indexMap;

var timerIdGV;// 倒计时定时器返回值

var warningTableTitleGV=[ "车牌号", "紧急报警", "超速报警", "疲劳驾驶", "危险预警", "GNSS模块故障",
              			"GNSS天线未接或被剪短", "GNSS天线短路", "主电源欠压", "主电源掉电", "显示屏故障", "TTS模块故障",
            			"摄像头故障", "道路运输证IC卡模块故障", "超速预警", "疲劳驾驶预警", "当天累计驾驶超时", "超时停车",
            			"进出区域", "进出路线", "路段驾驶时间不足/过长", "路线偏离报警", "车辆VSS故障", "车辆油量异常",
            			"车辆被盗", "车辆非法点火", "车辆非法位移", "碰撞预警", "侧翻预警", "非法开门报警" ,"行驶中未插卡报警"];//报警信息栏报警项过滤
var warningTableTitleSelectGV= warningTableTitleGV;//报警信息栏选中的报警项
var soundOpenGV=1;
var addressCursorGV;
