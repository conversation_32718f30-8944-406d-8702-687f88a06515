function showAllTree() {
	groupStatusRefreshIndicator = 1;

	$.ajax({
		url : "/808FrontProject/showVehicle/showAllGroupAction",
		type : "post",
		dataType : "json",
		success : function(data) {
			console.log("show all group action success!");
			jsonTree = data;
			//console.log(jsonTree);
			parsePart(jsonTree);
			multiAjaxAll();
		}
	});

}

function parsePart(data) {
	var htmlContent = '<ul id="browser" class="filetree">';
	var txtTmp = "";
	var codeTmp = "";
	indexMap = new HashMap();

	for (var i = 0; i < data.children.length; i++) {
		htmlContent += '<li id="group'
				+ i
				+ '"><input type=\"checkbox\" name=\"groups\" onclick=\"clickActionOnSelectAllGroups(this)\">'
				+ '<img src="/808FrontProject/image/xtracking-close.gif" title="点击显示车辆" onmouseover="styleCursorOnMouseOver(this)"  onclick="clickActionAllOnEyes(this)"/>'
				+ '<input type="hidden" class="eyes" value=0 />'
				+ ' <span class="folder" onmouseover="styleCursorOnMouseOver(this); styleTextOnMouseOver(this,textHoverColor)" onmouseout="styleTextOnMouseOut(this,textDefaultColor)" onclick="clickActionOnOpenGroup(this)">'
				+ data.children[i].title
				+ '</span><input type="hidden" class="openGroup" value="0"/>'
				+ "<ul>";
		for (var j = 0; j < data.children[i].children.length; j++) {
			var carNumber = data.children[i].children[j].title;
			var index = i + "-" + j;
			indexMap.put(carNumber, index);
		}

		htmlContent += '</ul></li>';
	}
	htmlContent += "</ul>";
	$("#vehicleList").html(htmlContent);
	$("#browser").treeview();
}

function showSearchedTree() {
	groupStatusRefreshIndicator = 0;
	monitoredArray = [];

	var keyWord = $("#searchText").val();
	var keyWordType = $("#keyWordType").val();

	$.ajax({
		url : "/808FrontProject/showVehicle/showSearchedGroupAction",
		data : {
			"keyWord" : keyWord,
			"keyWordType" : keyWordType
		},
		type : "post",
		dataType : "json",
		success : function(data) {
			jsonTree = data;
			parsePart(jsonTree);
		}
	});
}

function clickActionAllOnEyes(obj) {
	// 点击车组名
	if ($(obj).attr("src") == "/808FrontProject/image/xtracking-close.gif") {
		$(obj).parents("li").find(".eyes").val(1);
		$(obj).attr("src", "/808FrontProject/image/xtracking-open.gif");
		$(obj).attr("title", "点击关闭车辆");
		$(obj).parents("li").find("img").each(function() {
			$(this).attr("src", "/808FrontProject/image/xtracking-open.gif");
			$(this).attr("title", "点击关闭车辆");
		});

		$(obj).parents("li").find(".eye").each(function() {
			$(this).val(1);
		});

		monitoredArray = [];

		$(obj).parents("ul").find(".eye").each(
				function() {
					if ($(this).val() == 1) {
						var carNumberStringArray = $(this).next("span").html()
								.split(";");
						monitoredArray.push(carNumberStringArray[0]);
					}
				});

		multiAjax(monitoredArray, "");

	} else if ($(obj).attr("src") == "/808FrontProject/image/xtracking-open.gif") {
		$(obj).parents("li").find(".eyes").val(0);
		$(obj).attr("src", "/808FrontProject/image/xtracking-close.gif");
		$(obj).attr("title", "点击显示车辆");
		$(obj).parents("li").find("img").each(function() {
			$(this).attr("src", "/808FrontProject/image/xtracking-close.gif");
			$(this).attr("title", "点击显示车辆");
		});

		$(obj).parents("li").find(".eye").each(function() {
			$(this).val(0);
		});

		monitoredArray = [];

		$(obj).parents("ul").find(".eye").each(
				function() {
					if ($(this).val() == 1) {
						var carNumberStringArray = $(this).next("span").html()
								.split(";");
						monitoredArray.push(carNumberStringArray[0]);
					}
				});

		multiAjax(monitoredArray, "");
	}
}

function clickActionOnEye(obj) {
	// 点击车牌号前的眼睛图标
	if ($(obj).attr("src") == "/808FrontProject/image/xtracking-open.gif") {
		$(obj).next(".eye").val(0);
		$(obj).attr("src", "/808FrontProject/image/xtracking-close.gif");
		$(obj).attr("title", "点击显示车辆");
	} else if ($(obj).attr("src") == "/808FrontProject/image/xtracking-close.gif") {
		$(obj).next(".eye").val(1);
		$(obj).attr("src", "/808FrontProject/image/xtracking-open.gif");
		$(obj).attr("title", "点击关闭车辆");
	}

	monitoredArray = [];

	$(obj).parents("ul").parents("ul").find(".eye").each(function() {
		if ($(this).val() == 1) {
			var carNumberStringArray = $(this).next("span").html().split(";");
			monitoredArray.push(carNumberStringArray[0]);
		}
	});

	multiAjax(monitoredArray, "");
}

function clickActionOnSelectAllGroups(obj) {
	if ($(obj).prop("checked") == true) {

		$(obj).parent("li").find("input[name='group']").each(function() {
			$(this).prop("checked", true);
		});

		selectedArray = [];

		$(obj).parents("ul").find("input[name='group']").each(
				function() {
					if ($(this).prop("checked") == true) {
						selectedArray.push($(this).parent("li").find("span")
								.html().substring(0, 7));
					}
				});

	} else {

		$(obj).parent("li").find("input[name='group']").each(function() {
			$(this).prop("checked", false);
		});

		selectedArray = [];

		$(obj).parents("ul").find("input[name='group']").each(
				function() {
					if ($(this).prop("checked") == true) {
						selectedArray.push($(this).parent("li").find("span")
								.html().substring(0, 7));
					}
				});

	}

}

function clickActionOnSelectGroup(obj) {

	selectedArray = [];

	$(obj).parents("ul").parents("ul").find("input[name='group']").each(
			function() {
				if ($(this).prop("checked") == true) {
					selectedArray.push($(this).parent("li").find("span").html()
							.substring(0, 7));
				}
			});

}

function clickActionOnOpenGroup(obj) {
	parseAll(jsonTree, obj);
}

function parseAll(data, obj) {
	$("#browser").find(".folder").each(function() {
		var groupNameString = $(this).html();
		var selectGroupNameString = $(obj).html();
		if (groupNameString == selectGroupNameString) {

			var openGroup = $(this).parents("li").find(".openGroup").val();
			if (openGroup == 1) {
				$(this).parents("li").find(".openGroup").val(0);
			} else {
				$(this).parents("li").find(".openGroup").val(1);
			}
		}
	});

	var htmlContent = '<ul id="browser" class="filetree">';
	var txtTmp = "";
	var codeTmp = "";

	for (var i = 0; i < data.children.length; i++) {
		$("#browser")
				.find(".folder")
				.each(
						function() {
							var groupNameString = $(this).html().split("(");
							var groupName = groupNameString[0];
							var openGroup = $(this).parents("li").find(
									".openGroup").val();
							// 车辆分组中的车辆列表是否为展开
							var openEyes = $(this).parents("li").find(".eyes")
									.val();
							// 车辆分组中的所有车辆是否为监控

							if (openEyes == 0 && openGroup == 1
									&& groupName == data.children[i].title) {
								htmlContent += '<li id="group'
										+ i
										+ '" class="open"><input type=\"checkbox\" name=\"groups\" onclick=\"clickActionOnSelectAllGroups(this)\"></input>'
										+ '<img src="/808FrontProject/image/xtracking-close.gif" title="点击显示车辆" onmouseover="styleCursorOnMouseOver(this)"  onclick="clickActionAllOnEyes(this)"/>'
										+ '<input type="hidden" class="eyes" value=0 />'
										+ ' <span class="folder" onmouseover="styleCursorOnMouseOver(this); styleTextOnMouseOver(this,textHoverColor)" onmouseout="styleTextOnMouseOut(this,textDefaultColor)" onclick="clickActionOnOpenGroup(this)">'
										+ data.children[i].title
										+ '</span>'
										+ '<input type="hidden" class="openGroup" value=1 />'
										+ "<ul>";

								for (var j = 0; j < data.children[i].children.length; j++) {
									txtTmp = data.children[i].children[j].title
											+ ";"
											+ data.children[i].children[j].status;

									if (txtTmp.indexOf("在线") >= 0) {
										codeTmp = "<span style=\"color:#009900\" class=\"file\" onmousedown=\"openShortcut(this,event)\" onmouseover=\"styleTextOnMouseOver(this,textHoverColor)\" onmouseout=\"styleTextOnMouseOut(this,'#009900')\">"
												+ txtTmp + "</span>";
									} else {
										codeTmp = '<span class="file" onmousedown="openShortcut(this,event)" onmouseover="styleTextOnMouseOver(this,textHoverColor)" onmouseout="styleTextOnMouseOut(this,textDefaultColor)">'
												+ txtTmp + '</span>';
									}

									htmlContent += '<li id="car'
											+ (i + '-' + j)
											+ '"><input type="checkbox" name="group" onclick="clickActionOnSelectGroup(this);" value=\"'
											+ data.children[i].children[j].title
											+ '\"><img src="/808FrontProject/image/xtracking-close.gif" title="点击显示车辆" onclick="clickActionOnEye(this)" onmouseover="styleCursorOnMouseOver(this)"/><input type="hidden" class="eye" />'
											+ codeTmp + '</li>';
								}
								htmlContent += '</ul></li>';

							} else if (openEyes == 1 && openGroup == 1
									&& groupName == data.children[i].title) {
								htmlContent += '<li id="group'
										+ i
										+ '" class="open"><input type=\"checkbox\" name=\"groups\" onclick=\"clickActionOnSelectAllGroups(this)\"></input>'
										+ '<img src="/808FrontProject/image/xtracking-open.gif" title="点击显示车辆" onmouseover="styleCursorOnMouseOver(this)"  onclick="clickActionAllOnEyes(this)"/>'
										+ '<input type="hidden" class="eyes" value=1 />'
										+ ' <span class="folder" onmouseover="styleCursorOnMouseOver(this); styleTextOnMouseOver(this,textHoverColor)" onmouseout="styleTextOnMouseOut(this,textDefaultColor)" onclick="clickActionOnOpenGroup(this)">'
										+ data.children[i].title
										+ '</span>'
										+ '<input type="hidden" class="openGroup" value=1 />'
										+ "<ul>";

								for (var j = 0; j < data.children[i].children.length; j++) {
									txtTmp = data.children[i].children[j].title
											+ ";"
											+ data.children[i].children[j].status;

									if (txtTmp.indexOf("在线") >= 0) {
										codeTmp = "<span style=\"color:#009900\" class=\"file\" onmousedown=\"openShortcut(this,event)\" onmouseover=\"styleTextOnMouseOver(this,textHoverColor)\" onmouseout=\"styleTextOnMouseOut(this,'#009900')\">"
												+ txtTmp + "</span>";
									} else {
										codeTmp = '<span class="file" onmousedown="openShortcut(this,event)" onmouseover="styleTextOnMouseOver(this,textHoverColor)" onmouseout="styleTextOnMouseOut(this,textDefaultColor)">'
												+ txtTmp + '</span>';
									}

									htmlContent += '<li id="car'
											+ (i + '-' + j)
											+ '"><input type="checkbox" name="group" onclick="clickActionOnSelectGroup(this);" value=\"'
											+ data.children[i].children[j].title
											+ '\"><img src="/808FrontProject/image/xtracking-open.gif" title="点击显示车辆" onclick="clickActionOnEye(this)" onmouseover="styleCursorOnMouseOver(this)"/><input type="hidden" class="eye" value=1  />'
											+ codeTmp + '</li>';
								}
								htmlContent += '</ul></li>';
							} else if (openEyes == 0
									&& groupName == data.children[i].title) {
								htmlContent += '<li id="group'
										+ i
										+ '" class="closed"><input type=\"checkbox\" name=\"groups\" onclick=\"clickActionOnSelectAllGroups(this)\"></input>'
										+ '<img src="/808FrontProject/image/xtracking-close.gif" title="点击显示车辆" onmouseover="styleCursorOnMouseOver(this)"  onclick="clickActionAllOnEyes(this)"/>'
										+ '<input type="hidden" class="eyes" value=0 />'
										+ ' <span class="folder" onmouseover="styleCursorOnMouseOver(this); styleTextOnMouseOver(this,textHoverColor)" onmouseout="styleTextOnMouseOut(this,textDefaultColor)" onclick="clickActionOnOpenGroup(this)">'
										+ data.children[i].title
										+ '</span>'
										+ '<input type="hidden" class="openGroup" value=0></input>'
										+ "</li>";
							} else if (openEyes == 1
									&& groupName == data.children[i].title) {
								htmlContent += '<li id="group'
										+ i
										+ '" class="closed"><input type=\"checkbox\" name=\"groups\" onclick=\"clickActionOnSelectAllGroups(this)\"></input>'
										+ '<img src="/808FrontProject/image/xtracking-open.gif" title="点击显示车辆" onmouseover="styleCursorOnMouseOver(this)"  onclick="clickActionAllOnEyes(this)"/>'
										+ '<input type="hidden" class="eyes" value=1 />'
										+ ' <span class="folder" onmouseover="styleCursorOnMouseOver(this); styleTextOnMouseOver(this,textHoverColor)" onmouseout="styleTextOnMouseOut(this,textDefaultColor)" onclick="clickActionOnOpenGroup(this)">'
										+ data.children[i].title
										+ '</span>'
										+ '<input type="hidden" class="openGroup" value=0></input>'
										+ "</li>";
							}
						});
	}

	htmlContent += "</ul>";
	$("#vehicleList").html(htmlContent);
	$("#browser").treeview();

	$("#browser").find(".eye").each(function() {
		if ($(this).val() == 1) {
			var carNumberStringArray = $(this).next("span").html().split(";");
			monitoredArray.push(carNumberStringArray[0]);
		}
	});

	multiAjax(monitoredArray, "");
}

function setNormalMonitorInterval() {
	if (intervalVar != null)
		clearInterval(intervalVar);
	intervalVar = setInterval(showMonitorTrack, 30000);
}

function showMonitorTrack(carNumber) {
	$.ajax({
		url : "/808FrontProject/showVehicle/showSingleVehicleAction",
		data : {
			carNumber : carNumber
		},
		dataType : "json",
		type : "post",
		success : function(data) {

			latArray.push(data.lat);
			lonArray.push(data.lon);
			labelArray.push(data.carNumber);
			var info = "车辆id:" + data.carNumber + "<br>" + "时间:" + data.gpsTime
					+ "<br>" + "经度:" + data.lon + "<br>" + "纬度:" + data.lat
					+ "<br>" + "方向:" + data.direction;
			infoArray.push(info);

			addMarkersWithLabel(latArray, lonArray, labelArray, infoArray,
					"grid");
			addPolyLine(latArray, lonArray, "red", 2);

			dataArray.push(data);
			parseResultsOnInfoSpan(dataArray);
		}
	});
}


function multiAjax(carNumberArray, opt) {
	var carNumberString = "";
	for (var i = 0; i < carNumberArray.length; i++) {
		carNumberString += carNumberArray[i] + ",";// 连接符其实可以换的
	}
	carNumberString = carNumberString.substring(0, carNumberString.length - 1);

	var actionUrl = "/808FrontProject/showVehicle/showAllVehiclesAction";
	$.ajax({
		url : actionUrl,
		data : {
			"carNumberString" : carNumberString
		},
		type : "post",
		dataType : "json",
		success : function(data) {
			var now1 = new Date();
			
			
			mapMain.delOverlays();
			monitorTable.removeData();

			
			parseResultsOnTab(data);
			
			parseResultsOnMonitorInfoSpan(data);
			
			var now3=new Date();
			
			parseResultsOnWarnInfoSpan(data);
			
			var now4=new Date();
			
			mapMain.parseAllResultsOnMap(data, opt);
			
			if (queryMessageRefreshIndicator == 1)
				parseResultsOnDialogWindow(data);
			
			
			var now2 = new Date();
			var ts = now1.getTime();
			var te = now2.getTime();
			
//			alert("报警列表加载时间为:"+(now4.getTime()-now3.getTime())/1000+"s");
//			alert("总加载时间：" + (te - ts) / 1000 + "s");
			
		}
	});
	
	
}

function multiAjaxAll() {
	$("#browser").find(".eye").each(function() {
		if ($(this).val() == 1) {
			var carNumberStringArray = $(this).next("span").html().split(";");
			monitoredArray.push(carNumberStringArray[0]);
		}
	});

	multiAjax(monitoredArray, "");
}


function parseResultsOnTab(data) {

	
	// 解析车辆列表
	for (var i = 0; i < data.statusInfo.length; i++) {
		
		var carNumber = data.statusInfo[i].carNumber;
		var index = indexMap.get(carNumber);
		var stateString = data.statusInfo[i].carNumber;
		
		if (data.statusInfo[i].onlineStatus == 1) {
			stateString += ";在线";
			$("#car" + index).find("span").html(stateString);
			$("#car" + index).find("span").css("color", "green");
		} else {
			stateString += ";离线";
			$("#car" + index).find("span").html(stateString);
			$("#car" + index).find("span").css("color", "black");
		}

	}

	for (var i = 0; i < data.gpsInfo.length; i++) {
		$("#browser").find(".eye")
				.each(
						function() {
							if (data.gpsInfo[i] != null
									&& $(this).next("span").html().substring(0,
											7) == data.gpsInfo[i].carNumber) {
								var stateString = $(this).next("span").html()
										.substring(0, 10);
								if (stateString.indexOf("在线") != -1)
									stateString += ";" + data.gpsInfo[i].speed
											+ "km/h";
								$(this).next("span").html(stateString);
							}
						});
	}
	

	if (groupStatusRefreshIndicator == 1) {
		for (var i = 0; i < data.groupStatusInfo.length; i++) {
			$("#browser")
					.find(".eyes")
					.each(
							function() {
								var groupNameString = $(this).next("span").html();
								
								var groupNameSplitString;
								if(groupNameString.indexOf("(")!=-1){
									var groupNameSplitStrings = groupNameString.split("(");
									groupNameSplitString=groupNameSplitStrings[0];
								}else{
									groupNameSplitString=groupNameString;
								}
								
								if ($.trim(groupNameSplitString) == data.groupStatusInfo[i].vehicleGroupName) {
									
							
									var newGroupNameString = data.groupStatusInfo[i].vehicleGroupName
											+ "("
											+ data.groupStatusInfo[i].onlineVehicleNumber
											+ "/"
											+ data.groupStatusInfo[i].totalVehicleNumber
											+ ")";
									$(this).next("span").html(newGroupNameString);
								}
							});
		}
	}

}

function parseResultsOnMonitorInfoSpan(data) {
	// 解析信息栏监控信息标签页
	monitorTable.removeData();
	for (var i = 0; i < data.gpsInfo.length; i++) {
		if (data.gpsInfo[i] != null) {
			var trObj = document.createElement("tr");
			var htmlContent = "<td>" + data.gpsInfo[i].carNumber + "</td>";
			htmlContent += "<td>" + data.gpsInfo[i].simCardNumber + "</td>";
			htmlContent += "<td>" + data.gpsInfo[i].vehicleGroupName + "</td>";
			htmlContent += "<td>" + data.gpsInfo[i].revTime + "</td>";
			htmlContent += "<td>" + data.gpsInfo[i].gpsTime + "</td>";
			htmlContent += "<td>" + "报警标志" + "</td>";
			if ((data.gpsInfo[i].state & 0x01) == 1)
				htmlContent += "<td>ACC开</td>";
			else
				htmlContent += "<td>ACC关</td>";
			if (((data.gpsInfo[i].state & 0x02) >> 1) == 1)
				htmlContent += "<td>已定位</td>";
			else
				htmlContent += "<td>未定位</td>";
			htmlContent += "<td>" + data.gpsInfo[i].speed + "</td>";
			htmlContent += "<td>" + getAzimuth(data.gpsInfo[i].direction)
					+ "</td>";
			htmlContent += "<td>"
					+ "<div style='cursor:pointer' onclick=\"new BaiduMap().parseAddress("
					+ data.gpsInfo[i].latWithOffset + ","
					+ data.gpsInfo[i].lonWithOffset + ",'showAddress',this)\">"
					+ "点击显示位置..." + "</div>" + "</td>";
			htmlContent += "<td>" + data.gpsInfo[i].lon + "</td>";
			htmlContent += "<td>" + data.gpsInfo[i].lat + "</td>";
			if (data.gpsInfo[i].totalDistance != -1)
				htmlContent += "<td>" + data.gpsInfo[i].totalDistance + "</td>";
			else
				htmlContent += "<td></td>";
			htmlContent += "<td>油量</td>";
			if (data.gpsInfo[i].driverName != null) {
				htmlContent += "<td>" + data.gpsInfo[i].driverName + "</td>";
				htmlContent += "<td>" + data.gpsInfo[i].driveDistance + "</td>";
				var driveTime = data.gpsInfo[i].driveTime;
				if (driveTime >= 60)
					htmlContent += "<td>" + parseInt(driveTime / 60) + "时"
							+ driveTime % 60 + "分</td>";
				else
					htmlContent += "<td>" + driveTime + "分</td>";
			} else {
				htmlContent += "<td></td>";
				htmlContent += "<td></td>";
				htmlContent += "<td></td>";
			}
			trObj.innerHTML = htmlContent;
			// alert(data.length+", "+trObj.cells.length);
			monitorTable.appendData(trObj);
		}
	}

}

//报警提示函数
function alertLight(s) {
	// s=0,1,2
	var imgTitle = "";
	var imgPath = "";
	var codes = "";
	if (s == 0) {
		imgTitle = "正常";
		imgPath = "/808FrontProject/image/alert-green.gif";
		soundPath = "";
	} else if (s == 1) {
		imgTitle = "警告";
		imgPath = "/808FrontProject/image/alert-yellow.gif";
		soundPath = "";
	} else if (soundOpenGV == 1) {
		imgTitle = "报警";
		imgPath = "/808FrontProject/image/alert-red.gif";
		soundPath = "/808FrontProject/sound/ALARM8.WAV";
	} else {
		imgTitle = "报警";
		imgPath = "/808FrontProject/image/alert-red.gif";
		soundPath = "";
	}
	codes = "状态提示 <img style='float:right' width='20' height='20' title='"
			+ imgTitle + "' src='" + imgPath
			+ "' onclick='alertLight(0)' style='vertical-align:middle' />"
			+ "<embed src='" + soundPath
			+ "' hidden='true' autostart='true' loop='true'/>"
	$("#alertDiv").html(codes);
}

function controlSound(obj) {
	if (soundOpenGV) {
		// open
		obj.src = "/808FrontProject/image/soundf.png";
		obj.title = "点击开启报警声音";
		soundOpenGV = 0;
	} else {
		obj.src = "/808FrontProject/image/soundd.png";
		obj.title = "点击关闭报警声音";
		soundOpenGV = 1;
	}
}

function filterWarningOpt(tag) {
	var cb = document.getElementsByName("filterWarning");
	for (var k = 0; k < cb.length; k++) {
		if (tag == 1) {
			cb[k].checked = true;
		} else {
			cb[k].checked = false;
		}

	}
}

function filterWarningType() {
	var codes = "<span style='cursor:pointer; text-decoration: underline;' onclick='filterWarningOpt(1)' title='全部选择过滤类型'>全选</span>"
			+ "&nbsp;&nbsp;&nbsp;&nbsp;"
			+ "<span style='cursor:pointer; text-decoration: underline;' onclick='filterWarningOpt(0)' title='全部取消过滤类型'>全部取消</span>";

	codes += "<table style='font-size:13px'>";
	var len = warningTableTitleGV.length - 1;// 去掉第一列车牌号
	var cols = 3;
	var rows = Math.ceil(len / cols);
	var cnt = 1;// 从第一个元素开始遍历
	var warningTitle = "";
	for (var i = 0; i < rows; i++) {
		codes += "<tr>";
		for (var j = 0; j < cols; j++) {
			if (cnt < warningTableTitleGV.length) {
				warningTitle = warningTableTitleGV[cnt];
				if (warningTableTitleSelectGV.length == 0) {
					codes += "<td>"
							+ "<input type='checkbox' name='filterWarning' title='选择报警类型' style='height: 15px; width: 15px;' checked />&nbsp;"
							+ warningTitle + "</td>";
				} else {
					var recLog = 0;
					for (var k = 0; k < warningTableTitleSelectGV.length; k++) {
						if (warningTitle == warningTableTitleSelectGV[k]) {
							recLog++;
						}
					}
					if (recLog == 1) {
						codes += "<td>"
								+ "<input type='checkbox' name='filterWarning' title='选择报警类型' style='height: 15px; width: 15px;' checked />&nbsp;"
								+ warningTitle + "</td>";
					} else {
						codes += "<td>"
								+ "<input type='checkbox' name='filterWarning' title='选择报警类型' style='height: 15px; width: 15px;' />&nbsp;"
								+ warningTitle + "</td>";
					}
				}
			} else {
				codes += "<td></td>";
			}
			cnt++;
		}
		codes += "</tr>";
	}
	codes += "</table>";

	var d = dialog({
		title : '过滤报警类型',
		content : codes,
		okValue : '确定',
		ok : function() {
			var cb = document.getElementsByName("filterWarning");
			var titleSel = "";
			var t = 0;
			warningTableTitleSelectGV = [];
			for (var k = 0; k < cb.length; k++) {
				if (cb[k].checked) {
					warningTableTitleSelectGV[t] = warningTableTitleGV[k + 1];
					t++;
				}
			}
			multiAjax(monitoredArray, "refresh");
		},
	});

	d.showModal();

}

function parseResultsOnWarnInfoSpan(data) {
	// 解析信息栏报警标签页
	// warningTableTitleGV
	// warningTableTitleSelectGV
//	var arrData = [];
	var htmlContent = "<table width='100%' border=1 class='tableStyle'>"
			+ "<tr class='thStyle'><td width='80px'>车牌号</td><td>报警时间</td><td>报警内容</td><td>报警次数</td><td>车主信息</td><td>操作</td></tr>"
    var totalWarningDetail="";
	
	for (var i = 0; i < data.warningInfo.length; i++) {
		if (data.warningInfo[i] != null) {
////			// arrData[0] = data.warningInfo[i].carNumber;
////
////			// if (data.warningInfo[i].warningSign != 0)
////			// alertLight(2);
////
//			if ((data.warningInfo[i].warningSign & 0x00000001) == 0)
//				arrData[1] = 0;
//			else{
//				arrData[1] = 1;
//			}
//				
//			
//			if ((data.warningInfo[i].warningSign & 0x00000002) == 0)
//				arrData[2] = 0;
//			else{
//				arrData[2] = 1;
//			}
//			
//				
//
//			if ((data.warningInfo[i].warningSign & 0x00000004) == 0)
//				arrData[3] = 0;
//			else
//				arrData[3] = 1;
//
//			if ((data.warningInfo[i].warningSign & 0x00000008) == 0)
//				arrData[4] = 0;
//			else
//				arrData[4] = 1;
//
//			if ((data.warningInfo[i].warningSign & 0x00000010) == 0)
//				arrData[5] = 0;
//			else
//				arrData[5] = 1;
//
//			if ((data.warningInfo[i].warningSign & 0x00000020) == 0)
//				arrData[6] = 0;
//			else
//				arrData[6] = 1;
//
//			if ((data.warningInfo[i].warningSign & 0x00000040) == 0)
//				arrData[7] = 0;
//			else
//				arrData[7] = 1;
//
//			if ((data.warningInfo[i].warningSign & 0x00000080) == 0)
//				arrData[8] = 0;
//			else
//				arrData[8] = 1;
//
//			if ((data.warningInfo[i].warningSign & 0x00000100) == 0)
//				arrData[9] = 0;
//			else
//				arrData[9] = 1;
//
//			if ((data.warningInfo[i].warningSign & 0x00000200) == 0)
//				arrData[10] = 0;
//			else
//				arrData[10] = 1;
//
//			if ((data.warningInfo[i].warningSign & 0x00000400) == 0)
//				arrData[11] = 0;
//			else
//				arrData[11] = 1;
//
//			if ((data.warningInfo[i].warningSign & 0x00000800) == 0)
//				arrData[12] = 0;
//			else
//				arrData[12] = 1;
//
//			if ((data.warningInfo[i].warningSign & 0x00001000) == 0)
//				arrData[13] = 0;
//			else
//				arrData[13] = 1;
//
//			if ((data.warningInfo[i].warningSign & 0x00002000) == 0)
//				arrData[14] = 0;
//			else
//				arrData[14] = 1;
//
//			if ((data.warningInfo[i].warningSign & 0x00004000) == 0)
//				arrData[15] = 0;
//			else
//				arrData[15] = 1;
//			// if(data[i].warningSign&0x00008000==0)
//			// htmlContent+="<td>×</td>";
//			// else
//			// htmlContent+="<td>√</td>";
//			// if(data[i].warningSign&0x00010000==0)
//			// htmlContent+="<td>×</td>";
//			// else
//			// htmlContent+="<td>√</td>";
//			// if(data[i].warningSign&0x00020000==0)
//			// htmlContent+="<td>×</td>";
//			// else
//			// htmlContent+="<td>√</td>";
//
//			if ((data.warningInfo[i].warningSign & 0x00040000) == 0)
//				arrData[16] = 0;
//			else
//				arrData[16] = 1;
//
//			if ((data.warningInfo[i].warningSign & 0x00080000) == 0)
//				arrData[17] = 0;
//			else
//				arrData[17] = 1;
//
//			if ((data.warningInfo[i].warningSign & 0x00100000) == 0)
//				arrData[18] = 0;
//			else
//				arrData[18] = 1;
//
//			if ((data.warningInfo[i].warningSign & 0x00200000) == 0)
//				arrData[19] = 0;
//			else
//				arrData[19] = 1;
//
//			if ((data.warningInfo[i].warningSign & 0x00400000) == 0)
//				arrData[20] = 0;
//			else
//				arrData[20] = 1;
//
//			if ((data.warningInfo[i].warningSign & 0x00800000) == 0)
//				arrData[21] = 0;
//			else
//				arrData[21] = 1;
//
//			if ((data.warningInfo[i].warningSign & 0x01000000) == 0)
//				arrData[22] = 0;
//			else
//				arrData[22] = 1;
//
//			if ((data.warningInfo[i].warningSign & 0x02000000) == 0)
//				arrData[23] = 0;
//			else
//				arrData[23] = 1;
//
//			if ((data.warningInfo[i].warningSign & 0x04000000) == 0)
//				arrData[24] = 0;
//			else
//				arrData[24] = 1;
//
//			if ((data.warningInfo[i].warningSign & 0x08000000) == 0)
//				arrData[25] = 0;
//			else
//				arrData[25] = 1;
//
//			if ((data.warningInfo[i].warningSign & 0x10000000) == 0)
//				arrData[26] = 0;
//			else
//				arrData[26] = 1;
//
//			if ((data.warningInfo[i].warningSign & 0x20000000) == 0)
//				arrData[27] = 0;
//			else
//				arrData[27] = 1;
//
//			if ((data.warningInfo[i].warningSign & 0x40000000) == 0)
//				arrData[28] = 0;
//			else
//				arrData[28] = 1;
//
//			if ((data.warningInfo[i].warningSign & 0x80000000) == 0)
//				arrData[29] = 0;
//			else
//				arrData[29] = 1;
//
//			if (data.warningInfo[i].icCardWarning == 0)
//				arrData[30] = 0;
//			else {
//				arrData[30] = 1;
//				// var textMessageContent="您在驾驶过程中未插卡。请及时插入司机卡";
//				// deliveryTextMessage(data.warningInfo[i].carNumber,
//				// textMessageContent);
//			}
////			// 此处设置行驶未插卡报警反馈信息
////
////			// warningTableTitleGV
////			// warningTableTitleSelectGV
////
//////            var warningContent="";
//////			for (var p = 0; p < warningTableTitleSelectGV.length; p++) {
//////				for (var q = 0; q < warningTableTitleGV.length; q++) {
//////					if (warningTableTitleSelectGV[p] == warningTableTitleGV[q]
//////							&& arrData[q] == 1) {
//////						warningContent +=  warningTableTitleGV[q] +", ";
//////					}
//////				}
//////			}
////			
//			var warningContent="";
//		    for (var p = 0; p < warningTableTitleGV.length; p++) {
//				if (warningTableTitleGV[p] && arrData[p] == 1) {
//			        warningContent += warningTableTitleGV[p]+",";
//				}
//			}
////			
////			
//////			var warningContent;
//////			var count=-1;
//////		    for (var p = 0; p < warningTableTitleGV.length; p++) {
//////				if (warningTableTitleGV[p] && arrData[p] == 1) {
//////					count++;
//////					if(count==0)
//////						warningContent =warningTableTitleGV[p];
//////					else
//////					    warningContent += warningTableTitleGV[p];
//////				}
//////			}
////		    
//////		    var warningContentArray=[];
//////		    for (var p = 0; p < warningTableTitleGV.length; p++) {
//////				if (warningTableTitleGV[p] && arrData[p] == 1) {
//////					warningContentArray.push(warningTableTitleGV[p] + ", ");
//////				}
//////			}
//////		    var warningContent=warningContentArray.join("");
////
////			// 截断最后的逗号
//////			var warningDetail = "";
////
//////			var comma = warningContent.split(",");
//////			if (comma.length > 0) {
//////				for (var k = 0; k < comma.length; k++) {
//////					if (k < comma.length - 2)
//////						warningDetail += comma[k] + ", ";
//////					else
//////						warningDetail += comma[k];
//////				}
//////			}
////		    
//		    var warningDetail=warningContent.substring(0,warningContent.length-2);
	
			
			var warningDetail=data.warningInfo[i].warningDetail;

    // 		if (warningDetail != "") {
	//			alertLight(2);
				if (warningDetail.indexOf("行驶中未插卡报警") >= 0) {
					htmlContent += "<tr style='background-color:#F4A460'>";
				} else {
					htmlContent += "<tr>";
			    }
				
				htmlContent += "<td>" + data.warningInfo[i].carNumber + "</td>";
				htmlContent += "<td>" + data.warningInfo[i].gpsTime + "</td>";
				htmlContent += "<td>" + warningDetail + "</td>";
				htmlContent += "<td></td><td></td><td><a href='#' onclick=''>处理</a></td></tr>";
				
				totalWarningDetail+=warningDetail;
	//		}
		}

	}
	htmlContent += "</table>";
	document.getElementById("warningInfoSpanDiv").innerHTML = htmlContent;
	
	if(totalWarningDetail!="")
		alertLight(2);
}


function parseResultsOnInfoDiv(data) {
	var htmlContent = "<table  width='100%'  id='tabInfo' class='tabStyle'>"
			+ "<tr id='trInfo' class='trStyle'>" + "<td>&nbsp;序号</td>"
			+ "<td>车牌号</td>" + "<td>GPS时间</td>" + "<td>有效定位</td>"
			+ "<td>速度(公里/时)</td>" + "<td>行车方向</td>" + "<td>位置</td>"
			+ "<td>状态</td></tr>";
	var state = [];
	for (var i = 0; i < data.length; i++) {
		htmlContent += "<tr align='center'><td>" + (i + 1) + "</td>";
		htmlContent += "<td>" + data[i].carNumber + "</td>";
		htmlContent += "<td>" + data[i].gpsTime + "</td>";

		if (((data[i].state & 0x0002) >> 1) == 1)
			htmlContent += "<td>" + "是" + "</td>";
		else
			htmlContent += "<td>" + "否" + "</td>";
		htmlContent += "<td>" + data[i].speed + "</td>";
		htmlContent += "<td>" + getAzimuth(data[i].direction) + "</td>";
		htmlContent += "<td>"
				+ "<div style='cursor:pointer' onclick=\"parseAddress("
				+ data[i].latWithOffset + "," + data[i].lonWithOffset
				+ ",'showAddressInMonitorTable',this)\">" + "点击显示位置..."
				+ "</div>" + "</td>";
		state[i] = data[i].state;
		var stateString = "";
		if ((state[i] & 0x0001) == 1)
			stateString += "ACC开;";
		else
			stateString += "ACC关;";
		var runState = (state[i] & 0x0010) >> 4;
		if (runState == 1)
			stateString += "停运状态";
		else
			stateString += "运营状态";
		htmlContent += "<td>" + stateString + "</td></tr>";
	}
	htmlContent += "</table>";
	$("#infoOpenDiv").html(htmlContent);
	// infoPanelOpen();
}

function parseResultsOnDialogWindow(data) {

	if (data.message != null && data.message.infoContent != "") {
		// data.message.infoContent弹出查岗对话框
		queryMessageRefreshIndicator = 0;
		var codes = "<div>"
				+ "问题："
				+ "<textArea style='width: 200px' readonly='readonly'>"
				+ data.message.infoContent
				+ " </textArea>"
				+ "<input type='hidden' id='objectType' value="
				+ data.message.objectType
				+ " />"
				+ "<input type='hidden' id='objectId' value="
				+ data.message.objectId
				+ " />"
				+ "<input type='hidden' id='infoId' value="
				+ data.message.infoId
				+ " />"
				+ "<br/>回答："
				+ "<input type='text' id='answer' style='width: 200px;margin-top:10px' />"
				+ "</div>";
		var d = dialog({
			title : '查岗信息',
			content : codes,
			okValue : '提交',
			ok : function() {
				var objectType = $("#objectType").val();
				var objectId = $("#objectId").val();
				var infoId = $("#infoId").val();
				var answerContent = $("#answer").val();
				if ($.trim(answerContent) == "") {
					alert("回复内容不为空！");
					return false;
				}
				$.ajax({
					url : "/808FrontProject/common/answerMessageQueryAction",
					data : {
						"objectType" : objectType,
						"objectId" : objectId,
						"infoId" : infoId,
						"answerContent" : answerContent
					},
					type : "post"
				});

				queryMessageRefreshIndicator = 1;

				return true;
			},
			cancelValue : '取消',
			cancel : function() {

				queryMessageRefreshIndicator = 1;
			}
		});
		d.showModal();
	}
}

function runRefreshPhoto(){
	setInterval(function(){
		photoAjax();
	},60*1000);
}

function photoAjax(){
	$.ajax({
		url : "/808FrontProject/common/getCurrentMultiMediaEventInfoAction",
		type : "post",
		dataType : "json",
		success : function(data) {
			var htmlContent="";
			
			for(var i=0;i<data.length;i++){
				
			     htmlContent+= "<tr onmouseover='styleCellOnMouseOver(this, cellHoverColor)' onmouseout='styleCellOnMouseOut(this,cellDefaultColor)'>"
					+ "<td title='点击显示图像' style='cursor:pointer' onclick='showMultiMedia(this)'>"
					+ data[i].carNumber
					+ "</td><td>"
					+ data[i].eventType
					+ "</td><td id='mediaType'>"
					+ data[i].mediaType
					+ "</td><td>"
					+ data[i].fileType
					+ "</td><td id='createTime'>"
					+ data[i].createTime
					+ "</td></tr>";
			}
			
			$("#multiMediaTable").append(htmlContent);

		}
	});
}

function showMultiMedia(obj) {
	var carNumber = $(obj).html();
	var mediaType = $(obj).parent("tr").find("td#mediaType").html();
	var createTimeString = $(obj).parent("tr").find("td#createTime").html();
	if (mediaType == "图像") {
		$.ajax({
					url : "/808FrontProject/common/queryCurrentPhotoAction",
					data : {
						carNumber : carNumber,
						createTimeString : createTimeString
					},
					type : "post",
					dataType : "json",
					success : function(data) {
						var w = 480;
						var h = 320;
						var codes = "<a target='_blank' href='data'><img width="
								+ w
								+ " hight="
								+ h
								+ " src='"
								+ data + "' /></a>";
						var d = dialog({
							title : "实时图像_" + carNumber + "_"+ createTimeString,
							content : codes,
						});
						d.show();
					}
				});
	}

}

