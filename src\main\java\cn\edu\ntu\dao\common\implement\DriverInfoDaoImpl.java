package cn.edu.ntu.dao.common.implement;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;
import org.springframework.stereotype.Service;

import cn.edu.ntu.dao.common.interfaces.DriverInfoDao;
import cn.edu.ntu.dao.mapper.implement.DriverInfoMapper;
import cn.edu.ntu.entity.common.DriverInfo;
import cn.edu.ntu.entity.report.DriverReport;
import cn.edu.ntu.utils.db.JDBCTemplateForGpsHistory;

/**
 *
 * 
 * <AUTHOR>
 * @date 2016��6��20�� ����10:24:07 
 */
@Repository
public class DriverInfoDaoImpl implements DriverInfoDao{

	@Autowired
	private JdbcTemplate jdbcTemplate;
	
	@Override
	public DriverInfo getDriverInfo(String driverId) {
		String sql="select * from driverinfo where driverid="+driverId;
		DriverInfo driverInfo=jdbcTemplate.queryForObject(sql, new DriverInfoMapper());
		return driverInfo;
	}
	
	@Override
	public List<DriverInfo> listDrivers() {
		String sql="select * from driverinfo";
		return jdbcTemplate.query(sql, new DriverInfoMapper());
	}
	
	@Override
	public void addStoreDriver(String driverId) {
		
		String sql="insert into driverinfo (driverid) values ('"+driverId+"')";
		jdbcTemplate.update(sql);
		
	}
	
	@Override
	public void addStoreDriver(DriverInfo driverInfo) {
		String sql = "insert into driverinfo (driverid,drivertype,pwd,guid,drivername,driversex,identificationcardnumber,borndate,contactnumber,contactphonenumber,nativeplace,driveraddress,licenseexpiredate,receivelicensedate,qualificationcertificateid,certificatedeliverydate,certificateexpiredate,regulatoryagencyname,regulatorytelephonenumber,firstcertificatedate,certificatetype,certificateorganization,driverlicenseclass,certificatephotolocation) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
		Object[] params = new Object[24];
		params[0]=driverInfo.getDriverId();
		params[1]=driverInfo.getDriverType();
		params[2]=driverInfo.getDriverPassword();
		params[3]=driverInfo.getGuid();
		params[4]=driverInfo.getDriverName();
		params[5]=driverInfo.getDriverSex();
		params[6]=driverInfo.getIdentificationCardNumber();
		params[7]=driverInfo.getBornDate();
		params[8]=driverInfo.getContactNumber();
		params[9]=driverInfo.getContactPhoneNumber();
		params[10]=driverInfo.getNativePlace();
		params[11]=driverInfo.getDriverAddress();
/**
		Date annualVerificationDate=driverInfo.getAnnualVerificationDate();
		java.sql.Date annualVerificationSqlDate;
		if(annualVerificationDate!=null)
			annualVerificationSqlDate=new java.sql.Date(annualVerificationDate.getTime());
		else
			annualVerificationSqlDate=new java.sql.Date(0);
		params[12]=annualVerificationSqlDate;
*/
		params[12]=driverInfo.getLicenseExpireDate();
		params[13]=driverInfo.getReceiveLicenseDate();
		params[14]=driverInfo.getQualificationCertificateId();
		params[15]=driverInfo.getCertificateDeliveryDate();
		params[16]=driverInfo.getCertificateExpireDate();;
		params[17]=driverInfo.getRegulatoryAgencyName();
		params[18]=driverInfo.getRegulatoryTelephoneNumber();
		params[19]=driverInfo.getFirstCertificateDate();
		params[20]=driverInfo.getCertificateType();
		params[21]=driverInfo.getCertificateOrganization();
		params[22]=driverInfo.getDriverLicenseClass();
		jdbcTemplate.update(sql, params);
		
	}

	@Override
	public void editStoreDriver(DriverInfo driverInfo) {
		String sql="update driverinfo set drivertype=?,pwd=?,guid=?,drivername=?,driversex=?,identificationcardnumber=?,"+
	    "borndate=?,contactnumber=?,contactphonenumber=?,nativeplace=?,driveraddress=?,licenseexpiredate=?,receivelicensedate=?,"+
		"qualificationcertificateid=?,certificatedeliverydate=?,certificateexpiredate=?,regulatoryagencyname=?,regulatorytelephonenumber=?,"+
	    "firstcertificatedate=?,certificatetype=?,certificateorganization=?,driverlicenseclass=? where driverid=?";
		Object[] params=new Object[23];
		params[0]=driverInfo.getDriverType();
		params[1]=driverInfo.getDriverPassword();
		params[2]=driverInfo.getGuid();
		params[3]=driverInfo.getDriverName();
		params[4]=driverInfo.getDriverSex();
		params[5]=driverInfo.getIdentificationCardNumber();
		params[6]=driverInfo.getBornDate();
		params[7]=driverInfo.getContactNumber();
		params[8]=driverInfo.getContactPhoneNumber();
		params[9]=driverInfo.getNativePlace();
		params[10]=driverInfo.getDriverAddress();
		params[11]=driverInfo.getLicenseExpireDate();
		params[12]=driverInfo.getReceiveLicenseDate();
		params[13]=driverInfo.getQualificationCertificateId();
		params[14]=driverInfo.getCertificateDeliveryDate();
		params[15]=driverInfo.getCertificateExpireDate();
		params[16]=driverInfo.getRegulatoryAgencyName();
		params[17]=driverInfo.getRegulatoryTelephoneNumber();
		params[18]=driverInfo.getFirstCertificateDate();
		params[19]=driverInfo.getCertificateType();
		params[20]=driverInfo.getCertificateOrganization();
		params[21]=driverInfo.getDriverLicenseClass();
		params[22]=driverInfo.getDriverId();
		jdbcTemplate.update(sql, params);
	}

	@Override
	public void deleteDriver(String driverId) {

		String sql="delete from driverinfo where driverid=?";
		jdbcTemplate.update(sql, driverId);
	}

	@Override
	public List<DriverInfo> queryDrivers(DriverInfo driverInfo) {
		String sql="select * from driverinfo where 1=1 ";
//		if(!driverInfo.getDriverType().equals("0"))
//			sql+="and drivertype='"+driverInfo.getDriverType()+"' ";
		if(!driverInfo.getDriverName().equals(""))
			sql+="and drivername like '%"+driverInfo.getDriverName()+"%' ";
		if(!driverInfo.getQualificationCertificateId().equals(""))
			sql+="and qualificationcertificateid like '%"+driverInfo.getQualificationCertificateId()+"%'";
		return jdbcTemplate.query(sql, new DriverInfoMapper());
	}

	
}
