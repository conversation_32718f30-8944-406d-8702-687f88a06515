/*
 * Generated by the Jasper component of Apache Tomcat
 * Version: Apache Tomcat/7.0.47
 * Generated at: 2025-07-03 08:02:42 UTC
 * Note: The last modified time of this file was set to
 *       the last modified time of the source file after
 *       generation to assist with modification tracking.
 */
package org.apache.jsp.page;

import javax.servlet.*;
import javax.servlet.http.*;
import javax.servlet.jsp.*;

public final class index_jsp extends org.apache.jasper.runtime.HttpJspBase
    implements org.apache.jasper.runtime.JspSourceDependent {

  private static final javax.servlet.jsp.JspFactory _jspxFactory =
          javax.servlet.jsp.JspFactory.getDefaultFactory();

  private static java.util.Map<java.lang.String,java.lang.Long> _jspx_dependants;

  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fs_005fif_0026_005ftest;

  private javax.el.ExpressionFactory _el_expressionfactory;
  private org.apache.tomcat.InstanceManager _jsp_instancemanager;

  public java.util.Map<java.lang.String,java.lang.Long> getDependants() {
    return _jspx_dependants;
  }

  public void _jspInit() {
    _005fjspx_005ftagPool_005fs_005fif_0026_005ftest = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _el_expressionfactory = _jspxFactory.getJspApplicationContext(getServletConfig().getServletContext()).getExpressionFactory();
    _jsp_instancemanager = org.apache.jasper.runtime.InstanceManagerFactory.getInstanceManager(getServletConfig());
  }

  public void _jspDestroy() {
    _005fjspx_005ftagPool_005fs_005fif_0026_005ftest.release();
  }

  public void _jspService(final javax.servlet.http.HttpServletRequest request, final javax.servlet.http.HttpServletResponse response)
        throws java.io.IOException, javax.servlet.ServletException {

    final javax.servlet.jsp.PageContext pageContext;
    javax.servlet.http.HttpSession session = null;
    final javax.servlet.ServletContext application;
    final javax.servlet.ServletConfig config;
    javax.servlet.jsp.JspWriter out = null;
    final java.lang.Object page = this;
    javax.servlet.jsp.JspWriter _jspx_out = null;
    javax.servlet.jsp.PageContext _jspx_page_context = null;


    try {
      response.setContentType("text/html; charset=utf-8");
      pageContext = _jspxFactory.getPageContext(this, request, response,
      			null, true, 8192, true);
      _jspx_page_context = pageContext;
      application = pageContext.getServletContext();
      config = pageContext.getServletConfig();
      session = pageContext.getSession();
      out = pageContext.getOut();
      _jspx_out = out;

      out.write("\r\n");
      out.write("\r\n");
      out.write("<!DOCTYPE html PUBLIC \"-//W3C//DTD HTML 4.01 Transitional//EN\" \"http://www.w3.org/TR/html4/loose.dtd\">\r\n");
      out.write("<html>\r\n");
      out.write("<head>\r\n");
      out.write("<meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">\r\n");
      out.write("<meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge,chrome=1\">\r\n");
      out.write("\r\n");
      out.write("<title>太平洋北斗位置云</title>\r\n");
      out.write("<meta http-equiv=\"pragma\" content=\"no-cache\">\r\n");
      out.write("<meta http-equiv=\"cache-control\" content=\"no-cache\">\r\n");
      out.write("<meta http-equiv=\"expires\" content=\"0\">\r\n");
      out.write("<meta http-equiv=\"keywords\" content=\"太平洋,北斗,位置服务,云服务,车辆定位,车辆查询,车辆导航\">\r\n");
      out.write("<meta http-equiv=\"description\" content=\"太平洋北斗位置云\">\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("<link rel=\"stylesheet\" href=\"/808FrontProject/css/frame.css\"\r\n");
      out.write("\ttype=\"text/css\">\r\n");
      out.write("<link rel=\"stylesheet\" href=\"/808FrontProject/css/submenu.css\"\r\n");
      out.write("\ttype=text/css>\r\n");
      out.write("<link rel=\"stylesheet\" type=\"text/css\"\r\n");
      out.write("\thref=\"/808FrontProject/css/searchres.css\">\r\n");
      out.write("\r\n");
      out.write("<script type=\"text/javascript\" src=\"/808FrontProject/js/lib.jquery.js\"></script>\r\n");
      out.write("<script type=\"text/javascript\" src=\"/808FrontProject/js/lib.json2.js\"></script>\r\n");
      out.write("\r\n");
      out.write("<script type=\"text/javascript\"\r\n");
      out.write("\tsrc=\"http://api.map.baidu.com/api?v=2.0&ak=6ukSP6o4ff8u9SSnlVEKmiZC\"></script>\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("<script type=\"text/javascript\" src=\"/808FrontProject/js/frame.js\"></script>\r\n");
      out.write("<script type=\"text/javascript\" src=\"/808FrontProject/js/style.js\"></script>\r\n");
      out.write("<script type=\"text/javascript\" src=\"/808FrontProject/js/var.js\"></script>\r\n");
      out.write("\r\n");
      out.write("<script type=\"text/javascript\" src=\"/808FrontProject/js/tools.js\"></script>\r\n");
      out.write("<script type=\"text/javascript\" src=\"/808FrontProject/js/baidumap.js\"></script>\r\n");
      out.write("\r\n");
      out.write("<script type=\"text/javascript\" src=\"/808FrontProject/js/nav.js\"></script>\r\n");
      out.write("<script type=\"text/javascript\" src=\"/808FrontProject/js/hashMap.js\"></script>\r\n");
      out.write("<script type=\"text/javascript\" src=\"/808FrontProject/js/cookie.js\"></script>\r\n");
      out.write("<script type=\"text/javascript\" src=\"/808FrontProject/js/track.js\"></script>\r\n");
      out.write("<script type=\"text/javascript\" src=\"/808FrontProject/js/endParams.js\"></script>\r\n");
      out.write("<script type=\"text/javascript\" src=\"/808FrontProject/js/endControl.js\"></script>\r\n");
      out.write("<script type=\"text/javascript\"\r\n");
      out.write("\tsrc=\"/808FrontProject/js/commandResult.js\"></script>\r\n");
      out.write("\r\n");
      out.write("<!-- date component [S] -->\r\n");
      out.write("<script type=\"text/javascript\"\r\n");
      out.write("\tsrc=\"/808FrontProject/js/lib.jsdate/jscal2.js\"></script>\r\n");
      out.write("<script type=\"text/javascript\"\r\n");
      out.write("\tsrc=\"/808FrontProject/js/lib.jsdate/en.js\"></script>\r\n");
      out.write("<link rel=\"stylesheet\" type=\"text/css\"\r\n");
      out.write("\thref=\"/808FrontProject/js/lib.jsdate/jscal2.css\">\r\n");
      out.write("<link rel=\"stylesheet\" type=\"text/css\"\r\n");
      out.write("\thref=\"/808FrontProject/js/lib.jsdate/border-radius.css\">\r\n");
      out.write("<link rel=\"stylesheet\" type=\"text/css\"\r\n");
      out.write("\thref=\"/808FrontProject/js/lib.jsdate/steel.css\">\r\n");
      out.write("<!-- date component [E] -->\r\n");
      out.write("\r\n");
      out.write("<!-- treeview component [S] -->\r\n");
      out.write("<link rel=\"stylesheet\"\r\n");
      out.write("\thref=\"/808FrontProject/js/lib.jtree/jquery.treeview.css\" />\r\n");
      out.write("<link rel=\"stylesheet\" href=\"/808FrontProject/js/lib.jtree/screen.css\" />\r\n");
      out.write("<script src=\"/808FrontProject/js/lib.jtree/jquery.cookie.js\"></script>\r\n");
      out.write("<script src=\"/808FrontProject/js/lib.jtree/jquery.treeview.js\"></script>\r\n");
      out.write("<!-- treeview component [E] -->\r\n");
      out.write("\r\n");
      out.write("<!-- context menu [s] -->\r\n");
      out.write("<script type=\"text/javascript\" src=\"/808FrontProject/js/shortcut.js\"></script>\r\n");
      out.write("<link rel=\"stylesheet\" href=\"/808FrontProject/css/shortcut.css\"\r\n");
      out.write("\ttype=\"text/css\">\r\n");
      out.write("<!-- context menu [e] -->\r\n");
      out.write("\r\n");
      out.write("<!-- dynamic win component [s] -->\r\n");
      out.write("<link rel=\"stylesheet\" type=\"text/css\"\r\n");
      out.write("\thref=\"/808FrontProject/js/lib.mewin/win.css\">\r\n");
      out.write("<script type=\"text/javascript\"\r\n");
      out.write("\tsrc=\"/808FrontProject/js/lib.mewin/winfull.js\"></script>\r\n");
      out.write("<!-- dynamic win component [e] -->\r\n");
      out.write("\r\n");
      out.write("<!-- dynamic img component [s] -->\r\n");
      out.write("<link rel=\"stylesheet\" type=\"text/css\"\r\n");
      out.write("\thref=\"/808FrontProject/js/lib.meimg/img.css\">\r\n");
      out.write("<script type=\"text/javascript\"\r\n");
      out.write("\tsrc=\"/808FrontProject/js/lib.meimg/img.js\"></script>\r\n");
      out.write("<!-- dynamic win component [e] -->\r\n");
      out.write("\r\n");
      out.write("<!-- interactive dialog [s] -->\r\n");
      out.write("<link rel=\"stylesheet\"\r\n");
      out.write("\thref=\"/808FrontProject/js/lib.artDialog/ui-dialog.css\" />\r\n");
      out.write("<script src=\"/808FrontProject/js/lib.artDialog/dialog-plus-min.js\"></script>\r\n");
      out.write("<!-- interactive dialog [e] -->\r\n");
      out.write("\r\n");
      out.write("<!-- dynamic table component [s] -->\r\n");
      out.write("<link rel=\"stylesheet\" type=\"text/css\"\r\n");
      out.write("\thref=\"/808FrontProject/js/lib.metable/table.css\">\r\n");
      out.write("<script type=\"text/javascript\"\r\n");
      out.write("\tsrc=\"/808FrontProject/js/lib.metable/table.js\" charset=\"utf-8\"></script>\r\n");
      out.write("<!-- dynamic table component [e] -->\r\n");
      out.write("\r\n");
      out.write("<style type=\"text/css\">\r\n");
      out.write("body {\r\n");
      out.write("\tfont-family: 宋体;\r\n");
      out.write("}\r\n");
      out.write("\r\n");
      out.write("input {\r\n");
      out.write("\tfont-size: 14px;\r\n");
      out.write("}\r\n");
      out.write("\r\n");
      out.write(".thColor {\r\n");
      out.write("\tbackground-color: #ACD6FF;\r\n");
      out.write("}\r\n");
      out.write("\r\n");
      out.write(".commonFontSize {\r\n");
      out.write("\tfont-size: 14px;\r\n");
      out.write("}\r\n");
      out.write("\r\n");
      out.write("#rightFrameCanvas {\r\n");
      out.write("\tdisplay: none;\r\n");
      out.write("}\r\n");
      out.write("\r\n");
      out.write(".thStyle {\r\n");
      out.write("\tfont-weight: bold;\r\n");
      out.write("\tbackground-color: #ACD6FF;\r\n");
      out.write("\theight: 25px;\r\n");
      out.write("\tfont-size: 13px;\r\n");
      out.write("\ttext-align: center;\r\n");
      out.write("}\r\n");
      out.write("\r\n");
      out.write("#posDiv {\r\n");
      out.write("\tposition: absolute;\r\n");
      out.write("\ttop: 0px;\r\n");
      out.write("\tright: 0px;\r\n");
      out.write("\theight: 0px;\r\n");
      out.write("\twidth: 0px;\r\n");
      out.write("\tbackground-color: #fff;\r\n");
      out.write("\tz-index: 1;\r\n");
      out.write("\tfont-size: 14px;\r\n");
      out.write("\tline-height: 30px;\r\n");
      out.write("\tpadding-left: 5px;\r\n");
      out.write("}\r\n");
      out.write("\r\n");
      out.write("#cmdTable td {\r\n");
      out.write("\ttext-align: center\r\n");
      out.write("}\r\n");
      out.write("\r\n");
      out.write("#multiMediaTable td {\r\n");
      out.write("\ttext-align: center\r\n");
      out.write("}\r\n");
      out.write("\r\n");
      out.write("#alertDiv {\r\n");
      out.write("\tposition: absolute;\r\n");
      out.write("\tbottom: 0px;\r\n");
      out.write("\tright: 0px;\r\n");
      out.write("\theight: 0px;\r\n");
      out.write("\twidth: 0px;\r\n");
      out.write("\tz-index: 1;\r\n");
      out.write("\tfont-size: 14px;\r\n");
      out.write("\tline-height: 25px;\r\n");
      out.write("\tpadding-left: 5px;\r\n");
      out.write("}\r\n");
      out.write("\r\n");
      out.write(".tableStyle {\r\n");
      out.write("\tborder-collapse: collapse;\r\n");
      out.write("\tborder-spacing: 0px;\r\n");
      out.write("\tfont-size: 13px;\r\n");
      out.write("\ttext-align: center;\r\n");
      out.write("\tborder-color: #666;\r\n");
      out.write("}\r\n");
      out.write("\r\n");
      out.write(".warningInfoSpanDivStyle {\r\n");
      out.write("\theight: 112px;\r\n");
      out.write("\twidth: 100%;\r\n");
      out.write("\toverflow: auto;\r\n");
      out.write("}\r\n");
      out.write("\r\n");
      out.write("#warningInfoSpanDiv a:hover {\r\n");
      out.write("\tcolor: #666666;\r\n");
      out.write("\ttext-decoration: underline;\r\n");
      out.write("}\r\n");
      out.write("\r\n");
      out.write("#warningInfoSpanDiv a:link {\r\n");
      out.write("\tcolor: #000;\r\n");
      out.write("\ttext-decoration: none;\r\n");
      out.write("}\r\n");
      out.write("\r\n");
      out.write("#warningInfoSpanDiv a:visited {\r\n");
      out.write("\tcolor: #000;\r\n");
      out.write("\ttext-decoration: none;\r\n");
      out.write("}\r\n");
      out.write("</style>\r\n");
      out.write("<script type=\"text/javascript\">\r\n");
      out.write("\tvar mewin;\r\n");
      out.write("\tvar mapnotewin;\r\n");
      out.write("\tvar mapMain;\r\n");
      out.write("\tvar monitorTable;\r\n");
      out.write("\t//var warnTable;\r\n");
      out.write("\r\n");
      out.write("\t//counter for time \r\n");
      out.write("\tfunction runTimer() {\r\n");
      out.write("\t\tvar maxTime = 10;//sec\r\n");
      out.write("\t\ttimerIdGV = window.setInterval(function() {\r\n");
      out.write("\t\t\tif (maxTime > 0) {\r\n");
      out.write("\t\t\t\t$(\"#posDiv\").text(maxTime + \"秒后刷新\");\r\n");
      out.write("\t\t\t\t--maxTime;\r\n");
      out.write("\t\t\t} else {\r\n");
      out.write("\t\t\t\tmaxTime = 10;\r\n");
      out.write("\t\t\t\trefresh();\r\n");
      out.write("\t\t\t}\r\n");
      out.write("\t\t}, 1000);\r\n");
      out.write("\t}\r\n");
      out.write("\r\n");
      out.write("\tfunction killTimer() {\r\n");
      out.write("\t\tif (\"undefined\" != typeof timerIdGV) {\r\n");
      out.write("\t\t\twindow.clearInterval(timerIdGV);\r\n");
      out.write("\t\t}\r\n");
      out.write("\t\t$(\"#posDiv\").text(\"秒后刷新\");\r\n");
      out.write("\t}\r\n");
      out.write("\t\r\n");
      out.write("\tfunction refresh() {\r\n");
      out.write("\r\n");
      out.write("\t\tmultiAjax(monitoredArray, \"refresh\");\r\n");
      out.write("\t\t\r\n");
      out.write("\t}\r\n");
      out.write("\r\n");
      out.write("\tfunction showPosDiv() {\r\n");
      out.write("\t\t$(\"#posDiv\").show();// 轨迹\r\n");
      out.write("\t\t$(\"#posDiv\").css(\"height\", \"30px\");\r\n");
      out.write("\t\t$(\"#posDiv\").css(\"width\", \"90px\");\r\n");
      out.write("\t}\r\n");
      out.write("\r\n");
      out.write("\tfunction showAlertDiv() {\r\n");
      out.write("\t\t$(\"#alertDiv\").show();// 轨迹\r\n");
      out.write("\t\t$(\"#alertDiv\").css(\"height\", \"25px\");\r\n");
      out.write("\t\t$(\"#alertDiv\").css(\"width\", \"100px\");\r\n");
      out.write("\t}\r\n");
      out.write("\r\n");
      out.write("\t$(window).load(function() {\r\n");
      out.write("\t\tdocument.getElementById(\"rightFrameCanvas\").style.display = \"block\";\r\n");
      out.write("\t\tinitShortcut();\r\n");
      out.write("\r\n");
      out.write("\t\t//loading mywin [s] ----------------\r\n");
      out.write("\t\tmewin = new MapeyeWin(\"myWin\", \"winName\", \"winForm\");\r\n");
      out.write("\t\tmewin.init();\r\n");
      out.write("\t\tmewin.setImgPath(\"/808FrontProject/js/\");\r\n");
      out.write("\t\t//loading mywin [e] ----------------\r\n");
      out.write("\r\n");
      out.write("\t\tmapnotewin = new MapeyeWin(\"mapNote\", \"noteName\", \"noteForm\");\r\n");
      out.write("\t\tmapnotewin.init();\r\n");
      out.write("\t\tmapnotewin.setImgPath(\"/808FrontProject/js/\");\r\n");
      out.write("\r\n");
      out.write("\t\tmapMain = new BaiduMap(\"mapMain\");\r\n");
      out.write("\t\tmapMain.init();\r\n");
      out.write("\t\tmapMain.locationByBrowser();\r\n");
      out.write("\r\n");
      out.write("\t\t//屏蔽鼠标右键\r\n");
      out.write("\t\t$(document).bind(\"contextmenu\", function(e) {\r\n");
      out.write("\t\t\treturn false;\r\n");
      out.write("\t\t});\r\n");
      out.write("\r\n");
      out.write("\t\trunTimer();\r\n");
      out.write("\t\trunRefreshPhoto();\r\n");
      out.write("\t\tshowPosDiv();\r\n");
      out.write("\t\tshowAlertDiv();\r\n");
      out.write("\r\n");
      out.write("\t\tshowAllTree();\r\n");
      out.write("\r\n");
      out.write("\t\tinitDivWidthInNoteForm();\r\n");
      out.write("\r\n");
      out.write("\t\tmonitorTable = new MapeyeTable(\"monitorInfoSpanDiv\", \"monitorTable\");\r\n");
      out.write("\t\tmonitorTable.setInitColWidth(\"original\");\r\n");
      out.write("\t\tmonitorTable.setWordCompress(false);\r\n");
      out.write("\t\t//moinotrTable.setWidthOffset(7);\r\n");
      out.write("\t\tmonitorTable.init();\r\n");
      out.write("\t\tmonitorTable.setImgPath(\"/808FrontProject/js/\");\r\n");
      out.write("\r\n");
      out.write("\t\t/**\r\n");
      out.write("\t\twarnTable = new MapeyeTable(\"warningInfoSpanDiv\", \"warnTable\");\r\n");
      out.write("\t\twarnTable.setInitColWidth(\"original\");\r\n");
      out.write("\t\twarnTable.setWordCompress(false);\r\n");
      out.write("\t\twarnTable.init();\r\n");
      out.write("\t\twarnTable.setImgPath(\"/808FrontProject/js/\");\r\n");
      out.write("\t\t **/\r\n");
      out.write("\r\n");
      out.write("\t});\r\n");
      out.write("\r\n");
      out.write("\t$(window).resize(function() {\r\n");
      out.write("\t\t//adjust the layout of mewin\r\n");
      out.write("\t\tmewin.resizeWinLayout();\r\n");
      out.write("\t\tmapnotewin.resizeWinLayout();\r\n");
      out.write("\r\n");
      out.write("\t\tinitDivWidthInNoteForm();\r\n");
      out.write("\t\tmonitorTable.resizeTable();\r\n");
      out.write("\t\t//warnTable.resizeTable();\r\n");
      out.write("\t});\r\n");
      out.write("\r\n");
      out.write("\tfunction pageAdd(name, url) {\r\n");
      out.write("\t\tvar p = $(\"#menuParameterL1\").val();\r\n");
      out.write("\t\t//参数p为打开新页面要传入的参数，传入前必须进行UTF8编码，传入后必须进行UTF8解码\r\n");
      out.write("\t\tvar code = \"<iframe src='\"\r\n");
      out.write("\t\t\t\t+ url\r\n");
      out.write("\t\t\t\t+ \"?p=\"\r\n");
      out.write("\t\t\t\t+ encodeUTF8(p)\r\n");
      out.write("\t\t\t\t+ \"' width=100% height=100%  frameborder=0  border=2><\\/iframe>\";\r\n");
      out.write("\t\tmewin.winAdd(name + \"_\" + p, code);\r\n");
      out.write("\t}\r\n");
      out.write("\r\n");
      out.write("\t//当显示群发命令结果至页面\r\n");
      out.write("\tfunction pagesAdd(name, url) {\r\n");
      out.write("\t\tvar p = $(\"#menuParameterL2\").val();\r\n");
      out.write("\t\tvar code = \"<iframe src='\"\r\n");
      out.write("\t\t\t\t+ url\r\n");
      out.write("\t\t\t\t+ \"?p=\"\r\n");
      out.write("\t\t\t\t+ encodeUTF8(p)\r\n");
      out.write("\t\t\t\t+ \"' width=100% height=100%  frameborder=0  border=2><\\/iframe>\";\r\n");
      out.write("\t\tmewin.winAdd(name + \"_\" + p, code);\r\n");
      out.write("\t}\r\n");
      out.write("\r\n");
      out.write("\tfunction efencePageAdd(name, url) {\r\n");
      out.write("\t\tvar code = \"<iframe src='\"\r\n");
      out.write("\t\t\t\t+ url\r\n");
      out.write("\t\t\t\t+ \"' width=100% height=100%  frameborder=0  border=2><\\/iframe>\";\r\n");
      out.write("\t\tmewin.winAdd(name, code);\r\n");
      out.write("\t}\r\n");
      out.write("\r\n");
      out.write("\t//submenu function [s] ---------------------------------------\r\n");
      out.write("\tfunction displaySubMenu(li) {\r\n");
      out.write("\t\tvar subMenu = li.getElementsByTagName(\"ul\")[0];\r\n");
      out.write("\t\tif (typeof (subMenu) != \"undefined\") {\r\n");
      out.write("\t\t\tsubMenu.style.display = \"block\";\r\n");
      out.write("\t\t\tsubMenu.style.zIndex = 2;\r\n");
      out.write("\t\t}\r\n");
      out.write("\t}\r\n");
      out.write("\r\n");
      out.write("\tfunction hideSubMenu(li) {\r\n");
      out.write("\t\tvar subMenu = li.getElementsByTagName(\"ul\")[0];\r\n");
      out.write("\t\tif (typeof (subMenu) != \"undefined\") {\r\n");
      out.write("\t\t\tsubMenu.style.display = \"none\";\r\n");
      out.write("\t\t}\r\n");
      out.write("\t}\r\n");
      out.write("\t//submenu function [e] ---------------------------------------\r\n");
      out.write("\r\n");
      out.write("\t//unload event\r\n");
      out.write("\t$(window).unload(function() {\r\n");
      out.write("\t\tdocument.getElementById(\"rightFrameCanvas\").style.display = \"none\";\r\n");
      out.write("\t});\r\n");
      out.write("</script>\r\n");
      out.write("\r\n");
      out.write("</head>\r\n");
      out.write("\r\n");
      out.write("<body>\r\n");
      out.write("\t<div class=\"topFrame\" id=\"topFrame\">\r\n");
      out.write("\t\t<!-- Top Frame [S] -->\r\n");
      out.write("\t\t<table width=100% height=\"55px\" style=\"border-collapse: collapse;\"\r\n");
      out.write("\t\t\tborder=0 class=\"commonFontSize\">\r\n");
      out.write("\t\t\t<tr>\r\n");
      out.write("\t\t\t\t<td style=\"text-align: left; width: 350px\"><img height=\"50px\"\r\n");
      out.write("\t\t\t\t\twidth=\"350px\" src=\"/808FrontProject/image/tpy.jpg\"></td>\r\n");
      out.write("\t\t\t\t<td style=\"text-align: right\">\r\n");
      out.write("\t\t\t\t\t<ul id=\"navigation\" style=\"right: 0px\">\r\n");
      out.write("\t\t\t\t\t\t<li onmouseover=\"displaySubMenu(this)\"\r\n");
      out.write("\t\t\t\t\t\t\tonmouseout=\"hideSubMenu(this)\"><a href=\"#\"\r\n");
      out.write("\t\t\t\t\t\t\tonClick=\"efencePageAdd('电子围栏','/808FrontProject/page/electricFence.jsp')\">电子围栏</a></li>\r\n");
      out.write("\t\t\t\t\t\t");
      if (_jspx_meth_s_005fif_005f0(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\t\t\t\t\t\t<li onmouseover=\"displaySubMenu(this)\"\r\n");
      out.write("\t\t\t\t\t\t\tonmouseout=\"hideSubMenu(this)\"><a href=\"#\">统计报表</a>\r\n");
      out.write("\t\t\t\t\t\t\t<ul>\r\n");
      out.write("\t\t\t\t\t\t\t\t<li><a href=\"/808FrontProject/page/reportDrive.jsp\"\r\n");
      out.write("\t\t\t\t\t\t\t\t\ttarget=\"blank\">行驶报表</a></li>\r\n");
      out.write("\t\t\t\t\t\t\t\t<li><a href=\"/808FrontProject/page/reportMileage.jsp\"\r\n");
      out.write("\t\t\t\t\t\t\t\t\ttarget=\"blank\">里程报表</a></li>\r\n");
      out.write("\t\t\t\t\t\t\t\t<li><a href=\"/808FrontProject/page/reportVelocity.jsp\"\r\n");
      out.write("\t\t\t\t\t\t\t\t\ttarget=\"blank\">速度报表</a></li>\r\n");
      out.write("\t\t\t\t\t\t\t\t<!--  \t<li><a href=\"/808FrontProject/page/reportIgnition.jsp\"\r\n");
      out.write("\t\t\t\t\t\t\t\t\ttarget=\"blank\">点火报表</a></li>\r\n");
      out.write("\t\t\t\t\t\t-->\r\n");
      out.write("\t\t\t\t\t\t\t\t<li><a href=\"/808FrontProject/page/reportPark.jsp\"\r\n");
      out.write("\t\t\t\t\t\t\t\t\ttarget=\"blank\">停车报表</a></li>\r\n");
      out.write("\t\t\t\t\t\t\t\t<li><a href=\"/808FrontProject/page/reportWarning.jsp\"\r\n");
      out.write("\t\t\t\t\t\t\t\t\ttarget=\"blank\">报警报表</a></li>\r\n");
      out.write("\t\t\t\t\t\t\t\t<li><a href=\"/808FrontProject/page/reportDriver.jsp\"\r\n");
      out.write("\t\t\t\t\t\t\t\t\ttarget=\"blank\">司机报表</a></li>\r\n");
      out.write("\t\t\t\t\t\t\t</ul></li>\r\n");
      out.write("\t\t\t\t\t\t<!-- \t\t\t\t\t<li onmouseover=\"displaySubMenu(this)\"\r\n");
      out.write("\t\t\t\t\t\t\tonmouseout=\"hideSubMenu(this)\"><a\r\n");
      out.write("\t\t\t\t\t\t\thref=\"/808FrontProject/page/userInfo.jsp\" target=\"blank\">个人资料</a></li>\r\n");
      out.write("\t -->\r\n");
      out.write("\t\t\t\t\t\t<li onmouseover=\"displaySubMenu(this)\"\r\n");
      out.write("\t\t\t\t\t\t\tonmouseout=\"hideSubMenu(this)\"><a\r\n");
      out.write("\t\t\t\t\t\t\thref=\"/808FrontProject/user/loginOutAction.action\">安全退出</a></li>\r\n");
      out.write("\t\t\t\t\t</ul>\r\n");
      out.write("\t\t\t\t</td>\r\n");
      out.write("\t\t\t</tr>\r\n");
      out.write("\t\t</table>\r\n");
      out.write("\t\t<!-- Top Frame [E] -->\r\n");
      out.write("\t</div>\r\n");
      out.write("\r\n");
      out.write("\t<!-- Shortcut Menu [s] -->\r\n");
      out.write("\t<div id=\"shortcutMenuL1\" class=\"shortcutMenu\">\r\n");
      out.write("\t\t<div>群发操作 >></div>\r\n");
      out.write("\t\t<div\r\n");
      out.write("\t\t\tonclick=\"pageAdd('车辆信息','/808FrontProject/page/showVehicleInfo.jsp')\">车辆信息</div>\r\n");
      out.write("\t\t<div onClick=\"tempMonitor();\">临时跟踪</div>\r\n");
      out.write("\t\t<div\r\n");
      out.write("\t\t\tonClick=\"pageAdd('重点监控','/808FrontProject/page/focusMonitor.jsp')\">重点监控</div>\r\n");
      out.write("\t\t<div\r\n");
      out.write("\t\t\tonClick=\"pageAdd('历史轨迹','/808FrontProject/page/trackHistory.jsp')\">历史轨迹</div>\r\n");
      out.write("\t\t<div onclick=\"positionEnquire()\">车辆点名</div>\r\n");
      out.write("\t\t<div onclick=\"photoCapture()\">拍照监控</div>\r\n");
      out.write("\t\t<div onclick=\"photoQuery()\">拍照查询</div>\r\n");
      out.write("\t\t<div onclick=\"photoSetting()\">拍照设置</div>\r\n");
      out.write("\t\t<div onclick=\"videoMonitor()\">视频监控</div>\r\n");
      out.write("\t\t<div onclick=\"videoHuifang()\">视频回放</div>\r\n");
      out.write("\t\t<div onclick=\"fenceSetting()\">围栏绑定</div>\r\n");
      out.write("\t\t<div>配置终端 >></div>\r\n");
      out.write("\t\t<div>终端控制 >></div>\r\n");
      out.write("\t\t<div onclick=\"textDelivery()\">发送调度</div>\r\n");
      out.write("\t\t<input type=\"hidden\" id=\"menuParameterL1\" /> <input type=\"hidden\"\r\n");
      out.write("\t\t\tid=\"menuParameterL2\" />\r\n");
      out.write("\t</div>\r\n");
      out.write("\t<div id=\"shortcutMenuL2_QF\" class=\"shortcutMenu\">\r\n");
      out.write("\t\t<!-- 群发命令 -->\r\n");
      out.write("\t\t<div onclick=\"nTempMonitor();\">临时跟踪</div>\r\n");
      out.write("\t\t<div onclick=\"nPositionEnquire();\">车辆点名</div>\r\n");
      out.write("\t\t<div onClick=\"nPhotoCapture();\">拍照监控</div>\r\n");
      out.write("\t</div>\r\n");
      out.write("\t<div id=\"shortcutMenuL2_ZD\" class=\"shortcutMenu\">\r\n");
      out.write("\t\t<!-- 配置终端 -->\r\n");
      out.write("\t\t<div onclick=\"serverParam()\">服务器参数设置</div>\r\n");
      out.write("\t\t<div onclick=\"icCardAuthenParam()\">道路运输证IC卡认证</div>\r\n");
      out.write("\t\t<div onClick=\"posParam()\">位置汇报参数设置</div>\r\n");
      out.write("\t\t<div onClick=\"terminalParam()\">终端电话参数设置</div>\r\n");
      out.write("\t\t<div onClick=\"overspeedParam()\">超速参数设置</div>\r\n");
      out.write("\t\t<div onClick=\"fatigueDrivingParam()\">疲劳驾驶参数设置</div>\r\n");
      out.write("\t\t<div onClick=\"licenseParam()\">车牌号码参数设置</div>\r\n");
      out.write("\t</div>\r\n");
      out.write("\t<div id=\"shortcutMenuL2_ZK\" class=\"shortcutMenu\">\r\n");
      out.write("\t\t<!-- 终端控制 -->\r\n");
      out.write("\t\t<div onclick=\"terminalReset()\">终端复位</div>\r\n");
      out.write("\t</div>\r\n");
      out.write("\r\n");
      out.write("\t<!-- Shortcut Menu [e] -->\r\n");
      out.write("\r\n");
      out.write("\t<div class=\"pageShowCanvas\" id=\"index_main\">\r\n");
      out.write("\t\t<!-- Main Frame [S] -->\r\n");
      out.write("\t\t<div id=\"leftFrameCanvas\">\r\n");
      out.write("\t\t\t<!-- Left Frame [S]-->\r\n");
      out.write("\t\t\t<table style=\"padding: 2px; width: 100%; height: 100%\">\r\n");
      out.write("\t\t\t\t");
      if (_jspx_meth_s_005fif_005f1(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\t\t\t\t<tr>\r\n");
      out.write("\t\t\t\t\t<td colspan=\"2\">\r\n");
      out.write("\t\t\t\t\t\t<div\r\n");
      out.write("\t\t\t\t\t\t\tstyle=\"width: 100%; height: 100%; overflow: auto; margin-bottom: 20px; border: 0px solid #F00\">\r\n");
      out.write("\t\t\t\t\t\t\t<div id=\"vehicleList\" style=\"vertical-align: top\"></div>\r\n");
      out.write("\t\t\t\t\t\t</div>\r\n");
      out.write("\t\t\t\t\t</td>\r\n");
      out.write("\t\t\t\t</tr>\r\n");
      out.write("\t\t\t</table>\r\n");
      out.write("\t\t\t<!-- Left Frame [S]-->\r\n");
      out.write("\t\t\t\r\n");
      out.write("\t\t</div>\r\n");
      out.write("\r\n");
      out.write("\t\t<div class=\"middle_shousuo\">\r\n");
      out.write("\t\t\t<!--Middle Frame [S]-->\r\n");
      out.write("\t\t\t<a href=\"#\" class=\"open_right\" id=\"open_right\" style=\"display: none;\"\r\n");
      out.write("\t\t\t\tonClick=\"openleft();\" title=\"显示控制面板\"></a> <a href=\"javascript:;\"\r\n");
      out.write("\t\t\t\tonClick=\"closeleft();\" class=\"open_left\" id=\"open_left\"\r\n");
      out.write("\t\t\t\ttitle=\"隐藏控制面板\"></a>\r\n");
      out.write("\t\t\t<!--Middle Frame [E]-->\r\n");
      out.write("\t\t</div>\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\t\t<div id=\"rightFrameCanvas\">\r\n");
      out.write("\t\t\t<!--Right Frame [S]-->\r\n");
      out.write("\r\n");
      out.write("\t\t\t<!-- dynamic windows [s]-->\r\n");
      out.write("\t\t\t<div id=\"myWin\" style=\"height: 95%; width: 100%\">\r\n");
      out.write("\t\t\t\t<ul id=\"winName\">\r\n");
      out.write("\t\t\t\t\t<li>地图</li>\r\n");
      out.write("\t\t\t\t</ul>\r\n");
      out.write("\t\t\t\t<div id=\"winForm\">\r\n");
      out.write("\t\t\t\t\t<div style=\"overflow-y: none\">\r\n");
      out.write("\t\t\t\t\t\t<table width=\"100%\" height=\"100%\" border=0>\r\n");
      out.write("\t\t\t\t\t\t\t<tr>\r\n");
      out.write("\t\t\t\t\t\t\t\t<td>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t<div id=\"mapMain\" style=\"width: 100%; height: 100%\"></div>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t<div id=\"posDiv\">\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t<!-- for vehicles refresh -->\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t秒后刷新\r\n");
      out.write("\t\t\t\t\t\t\t\t\t</div>\r\n");
      out.write("\t\t\t\t\t\t\t\t</td>\r\n");
      out.write("\t\t\t\t\t\t\t</tr>\r\n");
      out.write("\t\t\t\t\t\t\t<tr>\r\n");
      out.write("\t\t\t\t\t\t\t\t<td height=\"150px\">\r\n");
      out.write("\t\t\t\t\t\t\t\t\t<div id=\"mapNote\" style=\"height: 100%; width: 100%;\">\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t<div id=\"noteForm\" style=\"height: 120px;\">\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t<div style=\"height: 120px; overflow: auto\"\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\tid=\"monitorInfoSpanDiv\">\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t<table width=\"1500px\" id=\"monitorTable\" border=1>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t<tr>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t\t<td width=\"80px\">车牌号</td>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t\t<td width=\"100px\">手机号</td>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t\t<td width=\"100px\">分组</td>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t\t<td width=\"150px\">接收时间</td>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t\t<td width=\"150px\">定位时间</td>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t\t<td>报警标志</td>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t\t<td>ACC状态</td>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t\t<td>定位状态</td>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t\t<td>速度</td>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t\t<td>方向</td>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t\t<td width=\"255px\">地址</td>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t\t<td>经度</td>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t\t<td>纬度</td>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t\t<td>里程</td>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t\t<td>油量</td>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t\t<td>当班司机</td>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t\t<td>当班里程</td>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t\t<td>行驶时间</td>\r\n");
      out.write("\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t</tr>\r\n");
      out.write("\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t</table>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t</div>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t<div>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t<table width=\"100%\">\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t<tr>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t\t<td class=\"thColor\" width=\"20px\"><img\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tid=\"settingTable\" src='../image/settingd.png'\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle='width: 20px; height: 20px; cursor: pointer; padding: 1px; margin-top: 0px; vertical-align: middle;'\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tonmouseover=\"styleImgMOver(this,'/808FrontProject/image/settingh.png','设置过滤报警类型')\"\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tonmouseout=\"styleImgMOut(this,'/808FrontProject/image/settingd.png')\"\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tonclick=\"filterWarningType();\" /></td>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t\t<td rowspan=2>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div id=\"warningInfoSpanDiv\"\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tclass=\"warningInfoSpanDivStyle\">\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<table width=\"100%\" id=\"warnTable\" border=1\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tclass=\"tableStyle\">\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<tr class=\"thStyle\">\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<td width=\"80px\">车牌号</td>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<td>报警时间</td>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<td>报警内容</td>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<td>报警次数</td>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<td>车主信息</td>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<td>操作</td>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</tr>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</table>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t\t</td>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t</tr>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t<tr>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t\t<td class=\"thColor\"><img id=\"settingTable\"\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tsrc='../image/soundd.png'\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle='width: 20px; height: 20px; cursor: pointer; padding: 1px; margin-top: 0px; vertical-align: middle;'\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttitle=\"点击关闭报警声音\" onclick=\"controlSound(this);\" /></td>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t</tr>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t</table>\r\n");
      out.write("\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t</div>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t<div style=\"overflow: auto\">\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t<table width=\"100%\" id=\"cmdTable\" class=\"tableStyle\"\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\tborder=1>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t<tr class=\"thStyle\">\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t\t<td>车牌</td>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t\t<td>命令</td>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t\t<td>上传时间</td>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t\t<td>执行结果</td>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t</tr>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t</table>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t</div>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t<div style=\"overflow: auto\">\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t<table width=\"100%\" id=\"multiMediaTable\" class=\"tableStyle\"\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\tborder=1>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t<tr class=\"thStyle\">\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t\t<td>车牌</td>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t\t<td>事件类型</td>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t\t<td>多媒体类型</td>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t\t<td>文件格式</td>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t\t<td>多媒体生成时间</td>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t</tr>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t</table>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t</div>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t<div style=\"overflow: auto\">\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t<table width=\"100%\" id=\"infoTable\" class=\"tableStyle\"\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\tborder=1>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t<tr class=\"thStyle\">\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t\t<td>信息类型</td>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t\t<td>上传时间</td>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t\t<td>信息内容</td>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t\t</tr>\r\n");
      out.write("\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t\t</table>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t</div>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t</div>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t<ul id=\"noteName\">\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t<li>监控信息</li>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t<li>报警</li>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t<li>命令结果</li>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t<li>多媒体</li>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\t<li>综合信息</li>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t</ul>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t</div>\r\n");
      out.write("\t\t\t\t\t\t\t\t\t<div id=\"alertDiv\">\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t状态提示 <img style=\"float: right\" width=\"20\" height=\"20\"\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\ttitle=\"正常\" src=\"/808FrontProject/image/alert-green.gif\"\r\n");
      out.write("\t\t\t\t\t\t\t\t\t\t\tstyle=\"vertical-align:middle\" />\r\n");
      out.write("\t\t\t\t\t\t\t\t\t</div>\r\n");
      out.write("\t\t\t\t\t\t\t\t</td>\r\n");
      out.write("\t\t\t\t\t\t\t</tr>\r\n");
      out.write("\r\n");
      out.write("\t\t\t\t\t\t</table>\r\n");
      out.write("\t\t\t\t\t</div>\r\n");
      out.write("\t\t\t\t</div>\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\t\t\t</div>\r\n");
      out.write("\t\t\t<!-- dynamic windows [e]-->\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\t\t\t<!--Right Frame [E]-->\r\n");
      out.write("\t\t</div>\r\n");
      out.write("\t\t<!-- Main Frame [E] -->\r\n");
      out.write("\t</div>\r\n");
      out.write("</body>\r\n");
      out.write("\r\n");
      out.write("</html>\r\n");
    } catch (java.lang.Throwable t) {
      if (!(t instanceof javax.servlet.jsp.SkipPageException)){
        out = _jspx_out;
        if (out != null && out.getBufferSize() != 0)
          try { out.clearBuffer(); } catch (java.io.IOException e) {}
        if (_jspx_page_context != null) _jspx_page_context.handlePageException(t);
        else throw new ServletException(t);
      }
    } finally {
      _jspxFactory.releasePageContext(_jspx_page_context);
    }
  }

  private boolean _jspx_meth_s_005fif_005f0(javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  s:if
    org.apache.struts2.views.jsp.IfTag _jspx_th_s_005fif_005f0 = (org.apache.struts2.views.jsp.IfTag) _005fjspx_005ftagPool_005fs_005fif_0026_005ftest.get(org.apache.struts2.views.jsp.IfTag.class);
    _jspx_th_s_005fif_005f0.setPageContext(_jspx_page_context);
    _jspx_th_s_005fif_005f0.setParent(null);
    // /page/index.jsp(365,6) name = test type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
    _jspx_th_s_005fif_005f0.setTest("userInfo!=null&userInfo.roleName!=\"操作员\"");
    int _jspx_eval_s_005fif_005f0 = _jspx_th_s_005fif_005f0.doStartTag();
    if (_jspx_eval_s_005fif_005f0 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
      if (_jspx_eval_s_005fif_005f0 != javax.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
        out = _jspx_page_context.pushBody();
        _jspx_th_s_005fif_005f0.setBodyContent((javax.servlet.jsp.tagext.BodyContent) out);
        _jspx_th_s_005fif_005f0.doInitBody();
      }
      do {
        out.write("\r\n");
        out.write("\t\t\t\t\t\t\t<li onmouseover=\"displaySubMenu(this)\"\r\n");
        out.write("\t\t\t\t\t\t\t\tonmouseout=\"hideSubMenu(this)\"><a href=\"#\">信息管理</a>\r\n");
        out.write("\t\t\t\t\t\t\t\t<ul>\r\n");
        out.write("\t\t\t\t\t\t\t\t\t<li><a href=\"/808FrontProject/user/listAction.action\"\r\n");
        out.write("\t\t\t\t\t\t\t\t\t\ttarget=\"blank\">用户管理</a></li>\r\n");
        out.write("\t\t\t\t\t\t\t\t\t<li><a href=\"/808FrontProject/vehicle/listAction.action\"\r\n");
        out.write("\t\t\t\t\t\t\t\t\t\ttarget=\"blank\">车辆信息</a></li>\r\n");
        out.write("\t\t\t\t\t\t\t\t\t<li><a\r\n");
        out.write("\t\t\t\t\t\t\t\t\t\thref=\"/808FrontProject/vehicleGroup/listAction.action\"\r\n");
        out.write("\t\t\t\t\t\t\t\t\t\ttarget=\"blank\">车辆分组</a></li>\r\n");
        out.write("\t\t\t\t\t\t\t\t\t<li><a href=\"/808FrontProject/driver/listAction.action\"\r\n");
        out.write("\t\t\t\t\t\t\t\t\t\ttarget=\"blank\">司机信息管理</a></li>\r\n");
        out.write("\r\n");
        out.write("\t\t\t\t\t\t\t\t</ul></li>\r\n");
        out.write("\t\t\t\t\t\t");
        int evalDoAfterBody = _jspx_th_s_005fif_005f0.doAfterBody();
        if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
          break;
      } while (true);
      if (_jspx_eval_s_005fif_005f0 != javax.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
        out = _jspx_page_context.popBody();
      }
    }
    if (_jspx_th_s_005fif_005f0.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
      _005fjspx_005ftagPool_005fs_005fif_0026_005ftest.reuse(_jspx_th_s_005fif_005f0);
      return true;
    }
    _005fjspx_005ftagPool_005fs_005fif_0026_005ftest.reuse(_jspx_th_s_005fif_005f0);
    return false;
  }

  private boolean _jspx_meth_s_005fif_005f1(javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  s:if
    org.apache.struts2.views.jsp.IfTag _jspx_th_s_005fif_005f1 = (org.apache.struts2.views.jsp.IfTag) _005fjspx_005ftagPool_005fs_005fif_0026_005ftest.get(org.apache.struts2.views.jsp.IfTag.class);
    _jspx_th_s_005fif_005f1.setPageContext(_jspx_page_context);
    _jspx_th_s_005fif_005f1.setParent(null);
    // /page/index.jsp(465,4) name = test type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
    _jspx_th_s_005fif_005f1.setTest("userInfo!=null");
    int _jspx_eval_s_005fif_005f1 = _jspx_th_s_005fif_005f1.doStartTag();
    if (_jspx_eval_s_005fif_005f1 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
      if (_jspx_eval_s_005fif_005f1 != javax.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
        out = _jspx_page_context.pushBody();
        _jspx_th_s_005fif_005f1.setBodyContent((javax.servlet.jsp.tagext.BodyContent) out);
        _jspx_th_s_005fif_005f1.doInitBody();
      }
      do {
        out.write("\r\n");
        out.write("\t\t\t\t\t<tr>\r\n");
        out.write("\t\t\t\t\t\t<td height=\"30px\">\r\n");
        out.write("\t\t\t\t\t\t\t<form style=\"margin-top: 2px; width: 100%; min-height: 30px;\">\r\n");
        out.write("\t\t\t\t\t\t\t\t<select id=\"keyWordType\" style=\"width: 60px; font-size: 13px\">\r\n");
        out.write("\t\t\t\t\t\t\t\t\t<option>车牌</option>\r\n");
        out.write("\t\t\t\t\t\t\t\t\t<option>SIM卡</option>\r\n");
        out.write("\t\t\t\t\t\t\t\t\t<!-- \r\n");
        out.write("\t\t\t\t\t\t\t\t<option>驾驶员</option>\r\n");
        out.write("\t\t\t\t\t\t\t\t -->\r\n");
        out.write("\t\t\t\t\t\t\t\t\t<option>车组</option>\r\n");
        out.write("\t\t\t\t\t\t\t\t</select> <input type='text' id='searchText'\r\n");
        out.write("\t\t\t\t\t\t\t\t\tonFocus='initFormText(this,\"\",\"请输入...\")'\r\n");
        out.write("\t\t\t\t\t\t\t\t\tonBlur='initFormText(this,\"请输入...\",\"请输入...\")'\r\n");
        out.write("\t\t\t\t\t\t\t\t\tstyle='width: 140px; font-size: 13px' value='请输入...' />\r\n");
        out.write("\t\t\t\t\t\t\t</form>\r\n");
        out.write("\t\t\t\t\t\t</td>\r\n");
        out.write("\t\t\t\t\t\t<td><img id=\"searchImg\" src='../image/search-s.png'\r\n");
        out.write("\t\t\t\t\t\t\tstyle='width: 20px; height: 20px; cursor: pointer; padding: 1px; margin-top: 0px; vertical-align: middle;'\r\n");
        out.write("\t\t\t\t\t\t\tonmouseover=\"styleImgMOver(this,'/808FrontProject/image/search-sh.png','点击查找')\"\r\n");
        out.write("\t\t\t\t\t\t\tonmouseout=\"styleImgMOut(this,'/808FrontProject/image/search-s.png')\"\r\n");
        out.write("\t\t\t\t\t\t\tonclick=\"showSearchedTree();\" /> <img\r\n");
        out.write("\t\t\t\t\t\t\tsrc=\"/808FrontProject/image/refreshd.png\"\r\n");
        out.write("\t\t\t\t\t\t\tstyle=\"width: 18px; height: 20px; vertical-align: middle; cursor: pointer\"\r\n");
        out.write("\t\t\t\t\t\t\tonmouseover=\"styleImgMOver(this,'/808FrontProject/image/refreshh.png','手动刷新')\"\r\n");
        out.write("\t\t\t\t\t\t\tonmouseout=\"styleImgMOut(this,'/808FrontProject/image/refreshd.png')\"\r\n");
        out.write("\t\t\t\t\t\t\tonclick=\"showAllTree();\" /></td>\r\n");
        out.write("\t\t\t\t\t</tr>\r\n");
        out.write("\t\t\t\t");
        int evalDoAfterBody = _jspx_th_s_005fif_005f1.doAfterBody();
        if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
          break;
      } while (true);
      if (_jspx_eval_s_005fif_005f1 != javax.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
        out = _jspx_page_context.popBody();
      }
    }
    if (_jspx_th_s_005fif_005f1.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
      _005fjspx_005ftagPool_005fs_005fif_0026_005ftest.reuse(_jspx_th_s_005fif_005f1);
      return true;
    }
    _005fjspx_005ftagPool_005fs_005fif_0026_005ftest.reuse(_jspx_th_s_005fif_005f1);
    return false;
  }
}
