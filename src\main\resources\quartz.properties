#============================================================================  
# Configure Main Scheduler Properties    
#============================================================================  
org.quartz.scheduler.instanceName = QuartzScheduler  
org.quartz.scheduler.instanceId = AUTO  
#============================================================================  
# Configure ThreadPool    
#============================================================================  
org.quartz.threadPool.class = org.quartz.simpl.SimpleThreadPool  
org.quartz.threadPool.threadCount = 5  
org.quartz.threadPool.threadPriority = 5  

#============================================================================  
# Configure Plugins   
#============================================================================  
org.quartz.plugin.jobInitializer.class =org.quartz.plugins.xml.XMLSchedulingDataProcessorPlugin
org.quartz.plugin.jobInitializer.fileNames = jobs.xml  
org.quartz.plugin.jobInitializer.failOnFileNotFound = true  
