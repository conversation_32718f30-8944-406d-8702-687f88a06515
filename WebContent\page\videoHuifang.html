<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频回放</title>
    <style>
        * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    height: 100vh;
    background-color: #f0f5ff;
    color: #333;
    overflow: hidden;
}

.container {
    display: flex;
    height: 100vh;
    width: 100vw;
}

.control-panel {
    width: 250px;
    padding: 20px;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    gap: 20px;
    overflow-y: auto;
}

.video-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 5px;
    overflow: hidden;
    background-color: #fff;
}

.video-container-wrapper {
    height: 70%;
    display: flex;
    flex-direction: column;
}

.video-container {
    position: relative;
    width: 100%;
    height: 100%;
    background-color: #000;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.table-container {
    height: 30%;
    margin-top: 0px;
    margin-bottom:0px;
    display: flex;
    flex-direction: column;
}

#videoStream {
    width: 100%;
    height: 100%;
    object-fit: fill;
    display: block;
}

.loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 18px;
    background-color: rgba(0, 0, 0, 0.7);
    padding: 10px 20px;
    border-radius: 5px;
}

.no-video {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 18px;
    text-align: center;
    width: 80%;
    background-color: rgba(0, 0, 0, 0.7);
    padding: 15px;
    border-radius: 5px;
}

.timestamp {
    position: absolute;
    bottom: 15px;
    left: 15px;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 8px 15px;
    border-radius: 5px;
    font-size: 14px;
    font-family: monospace;
}

.video-info-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    background-color: #FFF;
    border-radius: 8px;
    margin: 10px 0;
    font-size: 14px;
}

.control-buttons {
    display: flex;
    gap: 12px;
}

#carID{
   font-size:15px;
   font-weight:bolder;
   color:#00F;
}

/* 通用按钮样式 */
button {
    border: none;
    border-radius: 6px;
    padding: 10px 20px;
    font-size: 15px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 30px;
    letter-spacing: 1.2px;
}

/* 查看录像按钮 */
#viewBtn {
    background-color: #0d5dcc;
    color: white;
    min-width: 100%;
    padding: 12px;
    font-size: 15px;
    border-radius: 6px;
}

#viewBtn:hover {
    text-decoration: none;
    background-color: #1a73e8;
}

/* 播放/暂停按钮 */
#playPauseBtn {
    background-color: #0d5dcc;
    color: white;
    min-width: 100px;
}

#playPauseBtn:hover {
    text-decoration: none;
    background-color: #1a73e8;
}

#playPauseBtn.playing {
    background-color: #ff7043;
}

#playPauseBtn.playing:hover {
    text-decoration: none;
    background-color: #f0a046;
}

#playPauseBtn.continue {
    background-color: #0d5dcc;
}

#playPauseBtn.continue:hover {
    text-decoration: none;
    background-color: #1a73e8;
}

/* 截图按钮 */
#snapshotBtn {
    background-color: #0d5dcc;
    color: white;
    min-width: 100px;
}

#snapshotBtn:hover {
    text-decoration: none;
    background-color: #1a73e8;
}

/* 控制组样式 */
.control-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.control-group label {
    font-size: 14px;
    color: #000;
    font-weight: bolder;
}

.control-group input,
.control-group select { /*表单样式*/
    padding: 10px 12px;
    border: 1px solid #000;
    border-radius: 6px;
    font-size: 14px;
    background-color: white;
    transition: all 0.3s ease;
}

.video-info {
    text-align: right;
    color: #000;
    line-height: 1.5;
    flex: 1;
    margin-left: 20px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 15px;
}

.stream-table-container {
    flex: 1;
    overflow: auto;
    border: 1px solid #d1d9e6;
    border-radius: 8px;
    background-color: white;
}

table {
    width: 100%;
    border-collapse: collapse;
}

th, td {
    border: 1px solid #e0e6ed;
    padding: 10px 12px;
    text-align: left;
}

td {
    font-size:13px;
    height:16px;
}

th {
    background-color: #eee;
    position: sticky;
    top: 0;
    color: #000;
    font-weight: 600;
}

tr:nth-child(even) {
    background-color: #f8fbff;
}

tr:hover {
    background-color: #f0f7ff;
}

@media (max-width: 768px) {
    .container {
        flex-direction: column;
    }

    .control-panel {
        width: 100%;
        height: auto;
    }

    .video-container-wrapper {
        height: 60%;
    }

    .table-container {
        height: 40%;
    }

    .video-info-container {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .video-info {
        text-align: left;
        width: 100%;
        margin-left: 0;
        white-space: normal;
    }
    .play-me:hover{
      cursor: pointer;
      color:#000;
    }
    .play-me:visited{
      color:#0d5dcc;
    }

}
    </style>
</head>

<body>
    <div class="container">
        <div class="control-panel">
            <div class="control-group">
                <label for="startTime">开始时间</label>
                <input type="datetime-local" id="startTime">
            </div>

            <div class="control-group">
                <label for="endTime">结束时间</label>
                <input type="datetime-local" id="endTime">
            </div>

            <div class="control-group">
                <label for="channel">通道选择</label>
                <select id="channel">
                    <option value="1">通道 1</option>
                    <option value="2">通道 2</option>
                    <option value="3">通道 3</option>
                    <option value="4">通道 4</option>
                    <option value="5">通道 5</option>
                    <option value="6">通道 6</option>
                </select>
            </div>

            <button id="viewBtn">查看录像</button>
        </div>

        <div class="video-panel">
            <div class="video-container-wrapper">
                <div class="video-container">
                    <img id="videoStream" src="" alt="视频流">
                    <div id="loading" class="loading" style="display: none;">正在加载视频...</div>
                    <div id="noVideo" class="no-video">请选择时间范围和通道后点击"查看录像"</div>
                    <div id="timestamp" class="timestamp"></div>
                </div>

                <div class="video-info-container">
                    <div class="control-buttons">
                        <div id="carID">车牌号：</div>
                        <!--  
                        <button id="playPauseBtn">播放</button>
                        -->
                        <!--
                        <button id="snapshotBtn">截图</button>
                      --> 
                    </div>
                    <div class="video-info" id="videoInfo">
                        准备就绪
                    </div>
                </div>
            </div>

            <div class="table-container">
                <div class="stream-table-container" id="streamTableContainer">
                    <table id="streamTable">
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>通道</th>
                                <th>开始时间</th>
                                <th>结束时间</th>
                                <th>文件大小</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script>
	    // 获取当前页面的URL参数
	    const urlParams = new URLSearchParams(window.location.search);
	    const carID = urlParams.get('carID'); // 返回 "qqq"
        //var carID="%E8%8B%8FF25689";//"苏F25689";
        var videoUrlDefault="/808FrontProject/image/default.png";
        document.getElementById('videoStream').src=videoUrlDefault;
        function ajax(url, func){ //AJAX函数
                // 创建 XMLHttpRequest 对象
                const xhr = new XMLHttpRequest();
                // 配置请求
                xhr.open('GET', url, true);
                // 设置响应类型
                xhr.responseType = 'json';
                // 请求完成时的回调
                xhr.onload = function() {
                    if (xhr.status === 200) {// 请求成功
                        const response = JSON.parse(xhr.response);
                        //console.log('获取数据成功:', response.data);
                        func(response);
                    } else {// 请求失败
                        console.error('请求失败:', xhr.status);
                    }
                };
                xhr.onerror = function() { // 错误处理
                    console.error('请求发生错误');
                };
                xhr.send(); // 发送请求
        }

        // 格式化显示时间
        function formatDisplayTime(date) {
            return date.toLocaleString('zh-CN');
        }

        document.addEventListener('DOMContentLoaded', function () {
        	document.getElementById('carID').textContent = '车牌号：'+carID;
            // 设置默认时间（当前时间的前1小时）
            const now = new Date();
            const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);

            // 格式化时间为本地日期时间字符串
            function formatDateTime(date) {
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                const hours = String(date.getHours()).padStart(2, '0');
                const minutes = String(date.getMinutes()).padStart(2, '0');

                return `${year}-${month}-${day}T${hours}:${minutes}`;
            }

            document.getElementById('startTime').value = formatDateTime(oneHourAgo);
            document.getElementById('endTime').value = formatDateTime(now);

            // 视频流相关变量
            let streamInterval = null; 
            let isPlaying = false;
            let videoFrames = [];
            let currentFrame= null;//当前正在播放的视频流下标
            let currentTag=null;
            let currentChannel = '';
            let videoImgUrl=null;
            //const playPauseBtn = document.getElementById('playPauseBtn');

            
            // 左侧控制面板【查看录像】按钮点击事件
            document.getElementById('viewBtn').addEventListener('click', function () {
                const startTime = document.getElementById('startTime').value;
                const endTime = document.getElementById('endTime').value;
                currentChannel = document.getElementById('channel').value;

                if (!startTime || !endTime) {
                    updateStatus('错误：请选择开始时间和结束时间');
                    return;
                }

                const startDate = new Date(startTime);
                const endDate = new Date(endTime);

                if (startDate >= endDate) {
                    updateStatus('错误：结束时间必须晚于开始时间');
                    return;
                }

                // 重置播放状态
                resetPlayback();

                updateStatus(`已找到通道 ${currentChannel} 的录像 时间范围: ${formatDisplayTime(startDate)} 至 ${formatDisplayTime(endDate)}`);

                // 显示加载中
                document.getElementById('loading').style.display = 'block';
                document.getElementById('noVideo').style.display = 'none';
                document.getElementById('videoStream').style.display = 'none';
                document.getElementById('timestamp').style.display = 'none';
                document.getElementById('videoInfo').textContent = '正在加载视频信息...';
                document.getElementById('streamTableContainer').style.display = 'block';
                // 远程服务URL
                //const url = 'http://localhost:8001/videoList/%E8%8B%8FF25689/2025-04-01T00:00:00/2025-04-15T00:00:00/1';
                const url="http://*************:8001/videoList/"+carID+"/"+startTime+"/"+endTime+"/"+currentChannel;
                console.log("url="+url);
                ajax(url, displayStreamTable);
            });

            

            // 重置播放状态
            function resetPlayback() {
                clearInterval(streamInterval);
                isPlaying = false;
                //playPauseBtn.textContent = '播放';
                //playPauseBtn.className = '';
            }

            // 显示可播放的图像流表格
            function displayStreamTable(res) {
                // 隐藏加载中
                document.getElementById('loading').style.display = 'none';
                document.getElementById('noVideo').style.display = 'block';
                document.getElementById('noVideo').textContent = '请点击表格每行"播放"按钮开始播放';
                
                var data=res.data;
                var N=data.length;
                videoFrames = data;
                updateStatus("已检索到"+N+"条视频记录");
                //document.getElementById('videoInfo').textContent ="已检索到"+N+"条视频记录";
                const tableBody = document.querySelector('#streamTable tbody');
                tableBody.innerHTML = '';

                if (videoFrames.length > 0) {
                    videoFrames.forEach((frame, index) => {
                        const row = document.createElement('tr');

                        if (frame.isEllipsis) {
                            const cell = document.createElement('td');
                            cell.colSpan = 5;
                            cell.style.textAlign = 'center';
                            cell.style.fontStyle = 'italic';
                            cell.textContent = frame.message;
                            row.appendChild(cell);
                        } else {
                            row.innerHTML = `
                                <td>${index+1}</td>
                                <td>${frame.channelNo}</td>
                                <td>${frame.startTime}</td>
                                <td>${frame.endTime}</td>
                                <td>${frame.fileSize}</td>
                                <td title="点击播放视频" class="play-me" style="cursor:pointer">播放</td>
                            `;
                        }

                        tableBody.appendChild(row);
                    });
                    // 为每行的【播放】操作 添加click事件
                    document.body.addEventListener('click', (e) => {
                        if (e.target.classList.contains('play-me')) {
                            const p = e.target.parentElement;
                            tds = p.children;
                            let idx=parseInt(tds[0].textContent-1);
                            let c = tds[1].textContent;
                            let ts = tds[2].textContent;
                            let tsT = ts.replace(" ", "T");
                            let te = tds[3].textContent;
                            let teT = te.replace(" ", "T");
                            let f = tds[4].textContent;

                            //let video = document.getElementById("videoStream");
                            videoImgUrl = "http://*************:8001/videoReplay/" + carID + "/" + ts + "/" + te + "/" + f + "/" + c;
                            currentFrame=idx;
                            //console.log("==>" + p.innerHTML); // 输出父元素
                            //alert(video.src);
                            currentTag=`播放时刻: ${ts} 到 ${te} 的录像 （序号：${currentFrame+1}）`;
                            updateStatus('开始'+currentTag);
                            startPlayback(videoImgUrl, idx, currentTag);
                        }
                    });
                }
            }

            // 播放/暂停/继续按钮点击事件
            /*
            playPauseBtn.addEventListener('click', function () {
                if (videoFrames.length === 0) {
                    updateStatus('错误：没有可播放的视频流！');
                    return;
                }
                if(videoImgUrl==null){
                    updateStatus('错误：请先点击下述表格中每行视频流后面的【播放】按钮！');
                    return;
                }

                if (this.textContent === '播放') {
                    // 开始播放
                    startPlayback();
                } else  {
                    // 刷新
                    if(videoImgUrl){
                        document.getElementById('videoStream').src = videoImgUrl;
                        document.getElementById('videoStream').style.display = 'block';
                        console.log("刷新视频地址："+videoImgUrl);
                    }
                } 
            });
            */
            // 开始播放
            function startPlayback(imgUrl=null, trIdx=-1, tag=null) {
                console.log("videoImgUrl="+videoImgUrl);
                clearInterval(streamInterval);
                if(imgUrl){
                    videoImgUrl=imgUrl;
                    isPlaying = true;
                    currentFrame = 0;
                    document.getElementById('noVideo').style.display = 'none';
                    //updateStatus(`开始播放通道 ${currentChannel} 的录像`);

                    // 更新按钮状态
                    //playPauseBtn.textContent = '刷新';
                    //playPauseBtn.className = 'playing';

                    // 开始播放
                    document.getElementById('videoStream').src = videoUrlDefault;
                    streamInterval = setInterval(playNextFrame, 5000);
                }
                if (trIdx >= 0) {
                    currentFrame = trIdx;
                    const tableBody = document.querySelector('#streamTable tbody');
                    const rows = tableBody.querySelectorAll('tr');
                    rows.forEach((row, index) => { // 遍历所有行
                        if (index === currentFrame) {
                            row.style.fontWeight = 'bold'; // 当前行加粗
                        } else {
                            // 其他行恢复正常
                            row.style.fontWeight = 'normal';
                        }
                    });
                }
                if(tag){
                    currentTag=tag;
                }
            }

            // 暂停播放
            function pausePlayback() {
                isPlaying = false;
                clearInterval(streamInterval);
                updateStatus("已暂停"+currentTag);
                //document.getElementById('videoStream').src =null;

                // 更新按钮状态
                playPauseBtn.textContent = '继续播放';
                playPauseBtn.className = 'continue';
            }

            // 继续播放
            function continuePlayback() {
                isPlaying = true;
                updateStatus("继续"+currentTag);

                // 更新按钮状态
                playPauseBtn.textContent = '暂停';
                playPauseBtn.className = 'playing';

                // 继续播放
                streamInterval = setInterval(playNextFrame, 1000);
            }

            // 播放下一帧
            function playNextFrame() {
                if (!isPlaying) {
                    // 播放完毕
                    playbackComplete();
                    return;
                }

                if(videoImgUrl){
                    document.getElementById('videoStream').src = videoImgUrl;
                    document.getElementById('videoStream').style.display = 'block';
                    //document.getElementById('timestamp').textContent = formatDisplayTime(frame.timestamp);
                    //document.getElementById('timestamp').style.display = 'block';
                }
            }

            // 播放完成
            function playbackComplete() {
                clearInterval(streamInterval);
                isPlaying = false;
                updateStatus(currentTag+`完毕`);

                // 重置按钮状态
                playPauseBtn.textContent = '播放';
                playPauseBtn.className = '';
            }

            // 截图按钮点击事件
            /*
            document.getElementById('snapshotBtn').addEventListener('click', function () {
                if (document.getElementById('videoStream').style.display !== 'block') {
                    updateStatus('错误：没有可截图的视频画面');
                    return;
                }

                // 创建虚拟canvas进行截图
                const video = document.getElementById('videoStream');
                const canvas = document.createElement('canvas');
                canvas.width = video.videoWidth || video.naturalWidth;
                canvas.height = video.videoHeight || video.naturalHeight;
                const ctx = canvas.getContext('2d');
                ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

                // 下载截图
                const link = document.createElement('a');
                link.download = `截图_通道${currentChannel}_${formatFileTime(new Date())}.png`;
                link.href = canvas.toDataURL('image/png');
                link.click();

                updateStatus(`已保存当前画面截图 文件名: ${link.download}`);
            });
            */



            // 格式化文件名时间
            function formatFileTime(date) {
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                const hours = String(date.getHours()).padStart(2, '0');
                const minutes = String(date.getMinutes()).padStart(2, '0');
                const seconds = String(date.getSeconds()).padStart(2, '0');

                return `${year}${month}${day}_${hours}${minutes}${seconds}`;
            }

            // 更新状态
            function updateStatus(message) {
                document.getElementById('videoInfo').textContent = message;
            }
        });
    </script>
</body>

</html>