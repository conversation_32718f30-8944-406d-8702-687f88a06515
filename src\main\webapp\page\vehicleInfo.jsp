<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@taglib prefix="s" uri="/struts-tags"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>车辆信息</title>

<link rel="stylesheet" type="text/css"
	href="/808FrontProject/css/user.css">

<script type="text/javascript" src="/808FrontProject/js/lib.jquery.js"></script>
<script type="text/javascript" src="/808FrontProject/js/tools.js"></script>
<script type="text/javascript">
	//var url = window.location.href;
	var p = parseParametersFromURL("p");

	window.onload = function() {
		if (p != null) {
			p = decodeUTF8(p);
			document.getElementById("vehicleId").innerHTML = "车牌号: " + p;
			
			$.ajax({
				url:"/808FrontProject/common/getVehicleInfoAction",
				data:{
					"carNumber":p
				},
				dataType:"json",
				type:"post",
				success:function(data){
					handleHtml(data);
				}
			});
		}
		
		function handleHtml(data){
			$("#carColor").html(data.carColor);
			$("#carIdentificationCode").html(data.carIdentificationCode);
			$("#carType").html(data.carType);
			
			$("#networkEntryTime").html(data.networkEntryTime);
			$("#carArea").html(data.carArea);
			if(data.totalWeight==0)
				$("#totalWeight").html("");
			else 
			    $("#totalWeight").html(data.totalWeight);
			
			if(data.approvedLoadWeight==0)
				$("#approvedLoadWeight").html("");
			else
			    $("#approvedLoadWeight").html(data.approvedLoadWeight);
			if(data.approvedTractionWeight==0)
				$("#approvedTractionWeight").html("");
			else
			    $("#approvedTractionWeight").html(data.approvedTractionWeight);
			if(data.outsideLength==0&data.outsideWidth==0&data.outsideHeight==0)
				$("#outsideSize").html("");
			else{
				var outsideSize="长"+data.outsideLength+"mm 宽"+data.outsideWidth+"mm 高"+data.outsideHeight+"mm";
				$("#outsideSize").html(outsideSize);
			}
			
			if(data.insideLength==0&data.insideLength==0&data.insideLength==0)
				$("#insideSize").html("");
			else{
				var insideSize="长"+data.insideLength+"mm 宽"+data.insideWidth+"mm 高"+data.insideHeight+"mm";
				$("#insideSize").html(insideSize);
			}
			if(data.factoryTime=="1970-01-01")
				$("#factoryTime").html("");
			else
				$("#factoryTime").html(data.factoryTime);
			$("#businessScope").html(data.businessScope);
			
			$("#carOwner").html(data.carOwner);
			$("#contact").html(data.contact);
			$("#contactPhoneNumber").html(data.contactPhoneNumber);
			
			$("#simCardNumber").html(data.simCardNumber);
			$("#endId").html(data.endId);
			$("#endType").html(data.endType);
			
			$("#endManufacturer").html(data.endManufacturer);
			
			
		}
		
	};
</script>
</head>

<body>
	<span class="title1">车辆信息</span>
	<table class="tableCommon">
		<tr>
			<td colspan="6" class="title2 thColor" id="vehicleId">车牌号:</td>
		</tr>
		<tr>
			<td width="150px">车辆颜色</td>
			<td id="carColor"></td>
			<td width="150px">车辆识别代码/车架号</td>
			<td id="carIdentificationCode"></td>
			<td width="150px">车辆类型</td>
			<td id="carType"></td>
		</tr>
		<tr>
			<td>入网时间</td>
			<td id="networkEntryTime"></td>
			<td>所属地区</td>
			<td id="carArea"></td>
			<td>总质量</td>
			<td id="totalWeight"></td>
		</tr>
		<tr>
			<td>核定载质量</td>
			<td id="approvedLoadWeight"></td>
			<td>准牵引总质量</td>
			<td id="approvedTractionWeight"></td>
			<td>车辆外廓尺寸</td>
			<td id="outsideSize"></td>
		</tr>

		<tr>
			<td>货厢内部尺寸</td>
			<td id="insideSize"></td>
			<td>车辆出厂时间</td>
			<td id="factoryTime"></td>
			<td>经营范围</td>
			<td id="businessScope"></td>
		</tr>
		<tr>
			<td>车主</td>
			<td id="carOwner"></td>
			<td>联系人</td>
			<td id="contact"></td>
			<td>联系人手机</td>
			<td id="contactPhoneNumber"></td>
		</tr>
		<tr>
			<td>SIM卡卡号</td>
			<td id="simCardNumber"></td>
			<td>终端id</td>
			<td id="endId"></td>
			<td>终端类型</td>
			<td id="endType"></td>
		</tr>
		<tr>
			<td>终端制造商</td>
			<td id="endManufacturer" colspan=5></td>
		</tr>
	</table>


</body>
</html>