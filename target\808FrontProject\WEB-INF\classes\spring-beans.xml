<beans xmlns="http://www.springframework.org/schema/beans"  
      xmlns:context="http://www.springframework.org/schema/context"  
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"  
      xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.5.xsd  
             http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-2.5.xsd">  
      
      <context:component-scan base-package="cn.edu.ntu" />
      
      <bean id="config" class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
          <property name="locations">
               <list>
                   <value>classpath:datasource.properties</value>
                   <value>classpath:redis.properties</value>
                   <value>classpath:hibernate.properties</value>
               </list>
          </property>
      </bean>
      
      <bean id="jedisPoolConfig" class="redis.clients.jedis.JedisPoolConfig">  
          <property name="maxTotal" value="${redis.pool.maxActive}"/>  
          <property name="maxIdle" value="${redis.pool.maxIdle}"/>  
          <property name="minIdle" value="${redis.pool.minIdle}"/>  
          <property name="maxWaitMillis" value="${redis.pool.maxWait}"/>  
          <property name="testOnBorrow" value="${redis.pool.testOnBorrow}"/>  
          <property name="testOnReturn" value="${redis.pool.testOnReturn}"/>  
      </bean>  
        
      <bean id = "jedisConnectionFactory" class="org.springframework.data.redis.connection.jedis.JedisConnectionFactory">  
          <property name="hostName" value="${redis.host}"/>  
          <property name="port" value="${redis.port}"/>  
          <property name="timeout" value="${redis.timeout}"/>  
          <!--  <property name="password" value="${redis.password}"/> -->
          <property name="poolConfig" ref="jedisPoolConfig" /> 
      </bean>
      
      <bean id = "redisTemplate" class="org.springframework.data.redis.core.RedisTemplate">  
          <property name="connectionFactory" ref="jedisConnectionFactory"/>  
          <property name="keySerializer">  
              <bean class="org.springframework.data.redis.serializer.StringRedisSerializer"/>
          </property>
      </bean> 
      
      <!--以上为redis的配置-->
   
      <bean id="dataSource" class="org.apache.commons.dbcp2.BasicDataSource"  destroy-method="close">
          <property name="driverClassName" value="${driverClassName}"/>
          <property name="url" value="${url}"/>
          <property name="username" value="${username}"/>
          <property name="password" value="${password}"/>
          <property name="maxTotal" value="${maxActive}"/>
          <property name="maxIdle" value="${maxIdle}"/>
          <property name="maxWaitMillis" value="${maxWait}"/>
      </bean>
      
      <bean id="springJdbcTemplate" class="org.springframework.jdbc.core.JdbcTemplate">
          <property name="dataSource" ref ="dataSource" />
      </bean>
      
      <!--以上为springjdbc连接池的配置 -->
      
      <bean id="springDataSource" class="org.springframework.jdbc.datasource.DriverManagerDataSource ">
          <property name="url" value="${url}"/>
          <property name="username" value="${username}"/>
          <property name="password" value="${password}"/>
      </bean>
      
      <bean id="transactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">     
          <property name="dataSource" ref="springDataSource"/>
      </bean>     
      
      <bean id="transactionTemplate" class="org.springframework.transaction.support.TransactionTemplate">
          <property name="transactionManager" ref="transactionManager"/>
      </bean>
      
      <!--以上为springjdbc的配置 -->
      <!--  
      <bean id="sessionFactory" 
        class="org.springframework.orm.hibernate4.LocalSessionFactoryBean">
        <property name="dataSource" ref="dataSource" />
        <property name="packagesToScan">
            <list>
                <value>cn.edu.ntu.entity.common</value>
                <value>cn.edu.ntu.entity.report</value>
            </list>
        </property>
        <property name="hibernateProperties">
            <props>
                <prop key="hibernate.hbm2ddl.auto">${hibernate.hbm2ddl.auto}</prop>
                <prop key="hibernate.dialect">${hibernate.dialect}</prop>
                <prop key="hibernate.show_sql">${hibernate.show_sql}</prop>
            </props>
       </property>
      
     
    </bean>-->
     <!-- 以上为hibernate的配置-->
   </beans>  