<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@taglib prefix="s" uri="/struts-tags"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">

<title>车辆定位</title>
<meta http-equiv="pragma" content="no-cache">
<meta http-equiv="cache-control" content="no-cache">
<meta http-equiv="expires" content="0">
<meta http-equiv="keywords" content="太平洋,北斗,位置服务,云服务,车辆定位,车辆查询,车辆导航">
<meta http-equiv="description" content="太平洋北斗位置云">


<link rel="stylesheet" type="text/css"
	href="/808FrontProject/css/searchres.css">

<script type="text/javascript" src="/808FrontProject/js/lib.jquery.js"></script>
<script type="text/javascript" src="/808FrontProject/js/lib.json2.js"></script>

<script type="text/javascript" src="/808FrontProject/js/frame.js"></script>
<script type="text/javascript" src="/808FrontProject/js/tools.js"></script>
<script type="text/javascript" src="/808FrontProject/js/baidumap.js"></script>
<script type="text/javascript" src="/808FrontProject/js/style.js"></script>
<script type="text/javascript" src="/808FrontProject/js/monitor.js"></script>

<script type="text/javascript" src="/808FrontProject/js/var.js"></script>

<style type="text/css">
body {
	font-family: 宋体;
}

#posDiv {
	position: absolute;
	top: 0px;
	right: 0px;
	height: 0px;
	width: 0px;
	background-color: #fff;
	z-index: 1;
	font-size: 14px;
	line-height: 30px;
	padding-left: 5px;
}

#mapDiv {
	position: absolute;
	top: 0px;
	right: 0px;
	height: 0px;
	width: 0px;
	background-color: #fff;
	z-index: 0;
	font-size: 14px;
}

#infoOpenDiv {
	position: absolute;
	bottom: 0px;
	right: 0px;
	height: 0px;
	width: 0px;
	background-color: #fff;
	z-index: 1;
	font-size: 14px;
	padding: 2px;
	border-width: 1px 0px 0px 1px;
	border-style: solid none none solid;
	border-color: #ccc;
}

#infoCloseDiv, #infoOpenBtnDiv {
	position: absolute;
	bottom: 0px;
	right: 0px;
	height: 15px;
	width: 15px;
	background-color: #eee;
	z-index: 1;
	font-size: 13px;
	padding: 2px;
	border-width: 1px 0px 0px 1px;
	border-style: solid none none solid;
	border-color: #ccc;
}

input {
	font-size: 14px;
}

.commonFontSize {
	font-size: 14px;
}
</style>
<script type="text/javascript">
	//counter for time 
	function runTimer() {
		var maxTime = 10;//sec
		timerIdGV = window.setInterval(function() {
			if (maxTime > 0) {
				$("#posDiv").text(maxTime + "秒后刷新");
				--maxTime;
			} else {
				maxTime = 10;
				//refresh();
			}
		}, 1000);
	}

	function killTimer() {
		if ("undefined" != typeof timerIdGV) {
			window.clearInterval(timerIdGV);
		}
		$("#posDiv").text("秒后刷新");
	}

	function showPosDiv() {
		$("#posDiv").show();// 轨迹
		$("#posDiv").css("height", "30px");
		$("#posDiv").css("width", "90px");
	}

	$(window).load(function() {
		runTimer();
		
		showPosDiv();

		InitInfoTable();
		infoPanelOpen()

		showMapPanel();

	});

	$(window).resize(function() {
		resizeInfoPanelOpen();
		showPosDiv();
		showMapPanel();
	});
</script>
</head>

<body>
	<div id="posDiv">
		<!-- for vehicles refresh -->
		秒后刷新
	</div>

	<div id="mapDiv">
		<jsp:include page="baidu.jsp" />
	</div>
	<!-- 地图右下脚信息栏 [S]-->
	<div id="infoOpenDiv" style="overflow: auto"></div>
	<div id="infoOpenBtnDiv" onmouseover="styleInfoMOver(this)"
		onmouseout="styleInfoMOut(this)" onclick="infoPanelClose()"
		title="隐藏信息栏">
		<img src="/808FrontProject/image/close.png" style="margin-top: 70px;" />
	</div>
	<div id="infoCloseDiv" onmouseover="styleInfoMOver(this)"
		onmouseout="styleInfoMOut(this)" onclick="infoPanelOpen()"
		title="展开信息栏">
		<img src="/808FrontProject/image/open.png" />
	</div>
	<!-- 地图右下脚信息栏 [E]-->

</body>

</html>
