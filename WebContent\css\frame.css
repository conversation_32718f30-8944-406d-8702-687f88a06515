@charset "utf-8";

.open_left {
	top: 0px;
	width: 3px;
	height: 100%;
	position: absolute;
	display: block;
	background: #ccc;
	z-index: 2;
}

a.open_left:hover {
	width: 5px;
	background: #999;
}

.open_right {
	top: 0px;
	width: 3px;
	height: 100%;
	position: absolute;
	display: block;
	background: #ccc;
	z-index: 2;
}

a.open_right:hover {
	width: 5px;
	background: #999;
}

.topFrame {
	top: 0px;
	position: absolute;
	height: 60px;
	text-align: center;
	left: 1px;
	right: 1px;
	background-color: #fff;
	border-left: 0px solid #999;
	border-right: 0px solid #999;
	border-top: 0px #999 solid;
}

.middle_shousuo {
	position: absolute;
	left: 0px;
	width: 2px;
	height: 100%;
	margin-left: 300px;
	z-index: 2;
}

.pageShowCanvas {
	position: absolute !important;
	top: 61px !important;
	height: auto !important;
	top: 61px;
	height: 100%;
	bottom: 0px;
	right: 1px;
	left: 1px;
	overflow: hidden;
}

#leftFrameCanvas {
	left: 0px;
	top: 0px;
	bottom: 0px;
	height: auto !important;
	height: 100%;
	width: 300px;
	position: absolute;
	overflow: none;
	word-wrap: break-word;
	word-break: break-all;
	background-color: #fff;
	border-left: 0px solid #999;
	border-right: 0px solid #999;
	border-bottom: 0px #999 solid;
	border-top: 1px #666 solid;
}

#rightFrameCanvas {
	width: auto !important;
	left: 301px;
	right: 0px;
	position: absolute;
	top: 0px;
	height: auto !important;
	height: 100%;
	bottom: 0px;
	overflow: hidden;
	background-color: #fff;
	border-right: 0px solid #999;
	border-bottom: 0px #999 solid;
	border-top: 1px #666 solid;
}