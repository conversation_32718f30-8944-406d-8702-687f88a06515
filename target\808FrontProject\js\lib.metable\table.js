function MapeyeTable(e,t){this.divId=e;this.tabId=t;var r="#cccccc";var l="#c4ddf6";var n="./lib.metable/img/";var i="auto";var s=2;var o=15;var a=true;var u=document.getElementById(e);var c=document.getElementById(t);if(u==null){alert("表格容器'"+e+"'不存在！");return false}if(c==null){alert("表格'"+t+"'不存在！");return false}var d="divFreeze_"+e;var v="divCommon_"+e;var h="tabFreeze_"+e;var m="tabCommon_"+e;var f="divOpt_"+e;var p="optFreeze_"+e;var g="optUnFreeze_"+e;var w="freeze_"+e;var b="adjustUpUnFreeze_"+e;var y="adjustDownUnFreeze_"+e;var T="unfreeze_"+e;var A="adjustUpFreeze_"+e;var C="adjustDownFreeze_"+e;this.setImgPath=function(e){n=e+n};this.setWidthOffset=function(e){s=e};this.setInitColWidth=function(e){if(e=="auto"||e=="original"){i=e}else if(e==""){i="auto"}else{alert("初始化表格列宽方式'"+e+"'不合法！");return false}};this.setWordCompress=function(e){a=e};this.init=function(){var r=this.getObjWidth(u);var l=this.getObjHeight(u);var M=document.createElement("div");var O=document.createElement("div");M.setAttribute("id",d);O.setAttribute("id",v);u.appendChild(M);u.appendChild(O);O.onscroll=function(){M.scrollTop=O.scrollTop};var x=document.createElement("div");x.setAttribute("id",f);x.innerHTML="<div id='"+p+"' class='optMenu'>"+"<div id='"+w+"'><a href='#'>冻结此列</a></div>"+"<div id='"+b+"'><a href='#'>增加列宽</a></div>"+"<div id='"+y+"'><a href='#'>减少列宽</a></div>"+"</div>"+"<div id='"+g+"' class='optMenu'>"+"<div id='"+T+"'><a href='#'>取消冻结</a></div>"+"<div id='"+A+"'><a href='#'>增加列宽</a></div>"+"<div id='"+C+"'><a href='#'>减少列宽</div></a>"+"</div>";document.body.appendChild(x);document.getElementById(p).style.display="none";document.getElementById(g).style.display="none";c=this.tableCompressByColumn(c);c.setAttribute("class","meTable");var I=c.rows.length;var E=c.rows[0].cells.length;var B=this.getObjWidth(c);if(i=="auto"){var D="";var W=0;var H=0;var L=0;var S=0;var k;for(var U=0;U<E;U++){D=c.rows[0].cells[U].innerHTML;W+=D.length;H=D.length*o;L+=H}for(var U=0;U<E;U++){D=c.rows[0].cells[U].innerHTML;if(L<B){H=B*D.length/W}else{H=D.length*o}S+=H;c.rows[0].cells[U].setAttribute("width",H+"px")}c.setAttribute("width",S+"px")}else{var _=0;for(var U=0;U<E;U++){_=this.getObjWidth(c.rows[0].cells[U]);c.rows[0].cells[U].setAttribute("width",_+"px")}c.setAttribute("width",B+"px")}for(var U=0;U<E;U++){k=document.createElement("input");k.setAttribute("type","hidden");k.setAttribute("id","meCol_"+e+"_"+U);k.setAttribute("value",U);c.rows[0].cells[U].appendChild(k);for(var R=0;R<I;R++){if(R==0){c.rows[R].cells[U].setAttribute("class","meTh")}else{c.rows[R].cells[U].setAttribute("class","meTd")}}}u.setAttribute("style","overflow:auto; height:"+l+"px; width:"+r+"px");var z=c.rows[0];var N=-1;var F=-1;var X="";for(var Y=0;Y<E;Y++){z.cells[Y].onmouseover=function(){this.style.cursor="pointer";this.title="点击选择此列！"};z.cells[Y].onclick=function(){var r=new MapeyeTable(e,t);N=this.cellIndex;r.styleSelectColumn(c,N);var l=document.createElement("img");l.setAttribute("id","opt_"+e+"_"+F);l.setAttribute("title","点击打开表格快捷菜单！");l.setAttribute("src",n+"/a0.png");l.setAttribute("width","14px");l.setAttribute("height","11px");l.onmouseover=function(){this.setAttribute("src",n+"/a1.png")};l.onmouseout=function(){this.setAttribute("src",n+"/a0.png")};l.onclick=function(){var e=e||window.event;r.optMenuShow(e,this,p)};r.imgDelete(c);this.appendChild(l)}}for(var R=1;R<I;R++){var P=c.rows[R];P.onmouseover=function(){var r=new MapeyeTable(e,t);r.styleRowOnMouseOver(this)};P.onmouseout=function(){var r=new MapeyeTable(e,t);r.styleRowOnMouseOut(this)};for(var Y=0;Y<E;Y++){var q=P.cells[Y];q.onclick=function(){var r=new MapeyeTable(e,t);r.styleUnSelectColumn(c);r.imgDelete(c)}}}this.wordCompress(c,a);document.getElementById(w).onclick=function(){var i=new MapeyeTable(e,t);c.style.display="none";var o=c.rows.length;var d=document.getElementById(h);var v=document.getElementById(m);if(d==null){F=N;if(F==-1){alert("请选择要冻结列！");return false}d=i.newTable(h,o,1);M.innerHTML="";M.appendChild(d)}else{i.imgDelete(d)}var f=[];var w=-1;if(v==null){i.imgDelete(c);f=i.getDataByColumn(c,F);w=i.getObjWidth(c.rows[0].cells[F])}else{i.imgDelete(v);f=i.getDataByColumn(v,F);w=i.getObjWidth(v.rows[0].cells[F])}d=i.columnAppend(d,f);d=i.tableCompressByColumn(d);var b=d.rows.length;var y=d.rows[0].cells.length;d.rows[0].cells[y-1].setAttribute("width",w);var T=i.getObjWidth(d);if(T==-1){d.setAttribute("width",parseInt(w)+"px")}else{d.setAttribute("width",parseInt(T)+parseInt(w)+"px")}d.setAttribute("class","meTable");r=i.getObjWidth(u);l=i.getObjHeight(u);var A=parseInt(i.getObjWidth(d))+s;var C=r-A;M.setAttribute("style","float:left; overflow:hidden; height:"+l+"px; width:"+A+"px");O.setAttribute("style","position:absolute; overflow:auto; "+"left:"+A+"px; height:"+l+"px; width:"+C+"px");u.style.overflow="hidden";for(var x=0;x<y;x++){d.rows[0].cells[x].setAttribute("class","meTh");d.rows[0].cells[x].onmouseover=function(){this.style.cursor="pointer";this.title="点击选择此列！"};d.rows[0].cells[x].onclick=function(){F=this.cellIndex;X="freeze";i.styleSelectColumn(d,F);i.styleUnSelectColumn(v);var t=document.createElement("img");t.setAttribute("id","opt_"+e+"_"+F);t.setAttribute("title","点击打开表格快捷菜单！");t.setAttribute("src",n+"/a0.png");t.setAttribute("width","14px");t.setAttribute("height","11px");t.onmouseover=function(){this.setAttribute("src",n+"/a1.png")};t.onmouseout=function(){this.setAttribute("src",n+"/a0.png")};t.onclick=t.onclick=function(){var e=e||window.event;i.optMenuShow(e,this,g)};i.imgDelete(v);i.imgDelete(d);this.appendChild(t)}}if(v==null){v=i.newTable(m,o,E);v=i.copyTable(c,v);for(j=0;j<E;j++){v.rows[0].cells[j].setAttribute("width",c.rows[0].cells[j].getAttribute("width"))}v.setAttribute("width",c.getAttribute("width"));O.innerHTML="";O.appendChild(v)}var I=i.getObjWidth(v.rows[0].cells[F]);v=i.columnDelete(v,F);v.setAttribute("width",parseInt(i.getObjWidth(v))-parseInt(I)+"px");v=i.tableCompressByColumn(v);v.setAttribute("class","meTable");var B=v.rows.length;var D=v.rows[0].cells.length;for(var x=0;x<D;x++){v.rows[0].cells[x].setAttribute("class","meTh");v.rows[0].cells[x].onmouseover=function(){this.style.cursor="pointer";this.title="点击选择此列！"};v.rows[0].cells[x].onclick=function(){F=this.cellIndex;X="common";i.styleSelectColumn(v,F);i.styleUnSelectColumn(d);var t=document.createElement("img");t.setAttribute("id","opt_"+e+"_"+F);t.setAttribute("title","点击打开表格快捷菜单！");t.setAttribute("src",n+"/a0.png");t.setAttribute("width","14px");t.setAttribute("height","11px");t.onmouseover=function(){this.setAttribute("src",n+"/a1.png")};t.onmouseout=function(){this.setAttribute("src",n+"/a0.png")};t.onclick=function(){var e=e||window.event;i.optMenuShow(e,this,p)};i.imgDelete(v);i.imgDelete(d);this.appendChild(t)}}for(var W=1;W<b;W++){var H=d.rows[W];H.onmouseover=function(){var r=new MapeyeTable(e,t);r.styleRowOnMouseOver(this)};H.onmouseout=function(){var r=new MapeyeTable(e,t);r.styleRowOnMouseOut(this)};for(var x=0;x<y;x++){H.cells[x].onclick=function(){var r=new MapeyeTable(e,t);r.styleUnSelectColumn(d);r.imgDelete(d);r.styleUnSelectColumn(v);r.imgDelete(v)}}}for(var W=1;W<B;W++){var H=v.rows[W];H.onmouseover=function(){var r=new MapeyeTable(e,t);r.styleRowOnMouseOver(this)};H.onmouseout=function(){var r=new MapeyeTable(e,t);r.styleRowOnMouseOut(this)};for(var x=0;x<D;x++){H.cells[x].onclick=function(){var r=new MapeyeTable(e,t);r.styleUnSelectColumn(d);r.imgDelete(d);r.styleUnSelectColumn(v);r.imgDelete(v)}}}i.wordCompress(d,a);i.wordCompress(v,a)};document.getElementById(T).onclick=function(){var i=new MapeyeTable(e,t);var o=c.rows.length;var d=document.getElementById(h);var v=document.getElementById(m);var f=-1;if(d==null){alert("无冻结列！");return false}else{i.imgDelete(d);f=i.getObjWidth(d.rows[0].cells[F])}if(v==null){v=i.newTable(m,o,1);O.innerHTML="";O.appendChild(v)}else{i.imgDelete(v)}var g=i.getDataByColumn(d,F);var w=i.getObjWidth(d.rows[0].cells[F]);d=i.columnDelete(d,F);d.setAttribute("width",parseInt(i.getObjWidth(d))-parseInt(w)+"px");var b=v.rows[0].cells.length;var y=document.createElement("td");y.innerHTML=g[0];var T=y.getElementsByTagName("input")[0].value;var A=0;for(var C=0;C<b;C++){var x=v.rows[0].cells[C].getElementsByTagName("input")[0].value;if(parseInt(T)>parseInt(x)){A=C+1}}v=i.columnInsert(v,g,A);v=i.tableCompressByColumn(v);v.setAttribute("class","meTable");var I=v.rows.length;var E=v.rows[0].cells.length;v.rows[0].cells[A].setAttribute("width",f);A=0;var B=i.getObjWidth(v);if(B==-1){v.setAttribute("width",parseInt(f)+"px")}else{v.setAttribute("width",parseInt(B)+parseInt(f)+"px")}r=i.getObjWidth(u);l=i.getObjHeight(u);var j=parseInt(i.getObjWidth(d))+s;var D=r-j;M.setAttribute("style","float:left; overflow:hidden; height:"+l+"px; width:"+j+"px");O.setAttribute("style","position:absolute; overflow:auto; "+"left:"+j+"px; height:"+l+"px; width:"+D+"px");for(var W=0;W<E;W++){v.rows[0].cells[W].setAttribute("class","meTh");v.rows[0].cells[W].onmouseover=function(){this.style.cursor="pointer";this.title="点击选择此列！"};v.rows[0].cells[W].onclick=function(){F=this.cellIndex;X="common";i.styleSelectColumn(v,F);i.styleUnSelectColumn(d);var t=document.createElement("img");t.setAttribute("id","opt_"+e+"_"+F);t.setAttribute("title","点击打开表格快捷菜单！");t.setAttribute("src",n+"/a0.png");t.setAttribute("width","14px");t.setAttribute("height","11px");t.onmouseover=function(){this.setAttribute("src",n+"/a1.png")};t.onmouseout=function(){this.setAttribute("src",n+"/a0.png")};t.onclick=function(){var e=e||window.event;i.optMenuShow(e,this,p)};i.imgDelete(v);i.imgDelete(d);this.appendChild(t)}}for(var C=1;C<I;C++){var H=v.rows[C];H.onmouseover=function(){var r=new MapeyeTable(e,t);r.styleRowOnMouseOver(this)};H.onmouseout=function(){var r=new MapeyeTable(e,t);r.styleRowOnMouseOut(this)};for(var W=0;W<E;W++){H.cells[W].onclick=function(){var r=new MapeyeTable(e,t);r.styleUnSelectColumn(d);r.imgDelete(d);r.styleUnSelectColumn(v);r.imgDelete(v)}}}i.wordCompress(d,a);i.wordCompress(v,a)};document.getElementById(A).onclick=function(){var n=new MapeyeTable(e,t);var i="";if(X=="freeze"){i=h}else if(X=="common"){i=m}else{i=t;F=N}var o=document.getElementById(i);n.adjustColumnWidth(o,F,"+");r=n.getObjWidth(u);l=n.getObjHeight(u);var c=document.getElementById(h);var d=parseInt(n.getObjWidth(c))+s;var v=r-d;M.setAttribute("style","float:left; overflow:hidden; height:"+l+"px; width:"+d+"px");O.setAttribute("style","position:absolute; overflow:auto; "+"left:"+d+"px; height:"+l+"px; width:"+v+"px");n.wordCompress(o,a)};document.getElementById(C).onclick=function(){var n=new MapeyeTable(e,t);var i="";if(X=="freeze"){i=h}else if(X=="common"){i=m}else{i=t;F=N}var o=document.getElementById(i);n.adjustColumnWidth(o,F,"-");r=n.getObjWidth(u);l=n.getObjHeight(u);var c=document.getElementById(h);var d=parseInt(n.getObjWidth(c))+s;var v=r-d;M.setAttribute("style","float:left; overflow:hidden; height:"+l+"px; width:"+d+"px");O.setAttribute("style","position:absolute; overflow:auto;"+"left:"+d+"px; height:"+l+"px; width:"+v+"px");n.wordCompress(o,a)};document.getElementById(b).onclick=function(){var r=new MapeyeTable(e,t);var l="";if(X=="freeze"){l=h}else if(X=="common"){l=m}else{l=t;F=N}var n=document.getElementById(l);r.adjustColumnWidth(n,F,"+");r.wordCompress(n,a)};document.getElementById(y).onclick=function(){var r=new MapeyeTable(e,t);var l="";if(X=="freeze"){l=h}else if(X=="common"){l=m}else{l=t;F=N}var n=document.getElementById(l);r.adjustColumnWidth(n,F,"-");r.wordCompress(n,a)}};this.wordCompress=function(e,t){if(t){var r=e.rows[0];var l=e.rows.length;var n=r.cells.length;var i=null;var s="";var a=0;var u=0;for(var c=1;c<l;c++){for(var d=0;d<n;d++){i=e.rows[c].cells[d];tdTitle=i.getAttribute("title");a=this.getObjWidth(r.cells[d]);if(a==-1){a=r.cells[d].innerHTML.length*o}u=Math.round(parseInt(a)/o);if(tdTitle!=null){i.innerHTML=tdTitle.substring(0,u)}else{s=i.innerHTML;i.setAttribute("title",s);i.innerHTML=s.substring(0,u)}}}}};this.appendDataTest=function(){var r=document.createElement("tr");var l="<td>2008</td><td>1</td><td>2</td><td>3</td><td>4</td><td></td>";r.innerHTML=l;r.onmouseover=function(){var r=new MapeyeTable(e,t);r.styleRowOnMouseOver(this)};r.onmouseout=function(){var r=new MapeyeTable(e,t);r.styleRowOnMouseOut(this)};c.getElementsByTagName("tbody")[0].appendChild(r);var n=r.cells.length;var i=document.getElementById(h);var s=document.getElementById(m);var o=-1;var a=-1;if(i!=null){o=i.rows[0].cells.length}if(s!=null){a=s.rows[0].cells.length}var u=-1;var d=-1;if(i!=null){this.styleUnSelectColumn(i);this.imgDelete(i)}if(s!=null){this.styleUnSelectColumn(s);this.imgDelete(s)}var v="";var f=document.createElement("tr");var p=0;for(var g=0;g<o;g++){u=i.rows[0].cells[g].getElementsByTagName("input")[0].value;for(var w=0;w<n;w++){if(u==w){v+="<td>"+r.cells[w].innerHTML+"</td>";p++}}}if(p>0){f.innerHTML=v;i.appendChild(f)}var b="";var y=document.createElement("tr");var T=0;for(var A=0;A<a;A++){d=s.rows[0].cells[A].getElementsByTagName("input")[0].value;for(var w=0;w<n;w++){if(d==w){b+="<td>"+r.cells[w].innerHTML+"</td>";T++}}if(T>0){y.innerHTML=b;s.appendChild(y)}}};this.appendData=function(r){r.onmouseover=function(){var r=new MapeyeTable(e,t);r.styleRowOnMouseOver(this)};r.onmouseout=function(){var r=new MapeyeTable(e,t);r.styleRowOnMouseOut(this)};var l=r.cells.length;var n=document.getElementById(h);var i=document.getElementById(m);if(n==null&&i==null){c.getElementsByTagName("tbody")[0].appendChild(r);this.wordCompress(c,a);for(var s=0;s<l;s++){var o=r.cells[s];o.onclick=function(){var r=new MapeyeTable(e,t);r.styleUnSelectColumn(c);r.imgDelete(c)}}}var u=-1;var d=-1;if(n!=null){u=n.rows[0].cells.length}if(i!=null){d=i.rows[0].cells.length}var v=-1;var f=-1;if(n!=null){this.styleUnSelectColumn(n);this.imgDelete(n)}if(i!=null){this.styleUnSelectColumn(i);this.imgDelete(i)}var p="";var g=document.createElement("tr");var w=0;for(var b=0;b<u;b++){v=n.rows[0].cells[b].getElementsByTagName("input")[0].value;for(var y=0;y<l;y++){if(v==y){p+="<td>"+r.cells[y].innerHTML+"</td>";w++}}}if(w>0){g.innerHTML=p;g.onmouseover=function(){var r=new MapeyeTable(e,t);r.styleRowOnMouseOver(this)};g.onmouseout=function(){var r=new MapeyeTable(e,t);r.styleRowOnMouseOut(this)};n.appendChild(g);this.wordCompress(n,a);for(var s=0;s<u;s++){var o=g.cells[s];o.onclick=function(){var r=new MapeyeTable(e,t);r.styleUnSelectColumn(n);r.imgDelete(n);if(i!=null){r.styleUnSelectColumn(i);r.imgDelete(i)}}}}var T="";var A=document.createElement("tr");var C=0;for(var M=0;M<d;M++){f=i.rows[0].cells[M].getElementsByTagName("input")[0].value;for(var y=0;y<l;y++){if(f==y){T+="<td>"+r.cells[y].innerHTML+"</td>";C++}}}if(C>0){A.innerHTML=T;A.onmouseover=function(){var r=new MapeyeTable(e,t);r.styleRowOnMouseOver(this)};A.onmouseout=function(){var r=new MapeyeTable(e,t);r.styleRowOnMouseOut(this)};i.appendChild(A);this.wordCompress(i,a);for(var s=0;s<d;s++){var o=A.cells[s];o.onclick=function(){var r=new MapeyeTable(e,t);r.styleUnSelectColumn(i);r.imgDelete(i);if(n!=null){r.styleUnSelectColumn(n);r.imgDelete(n)}}}}};this.deleteRows=function(e){var t=e.rows.length-1;while(t>=1){e.deleteRow(t);t=e.rows.length-1}};this.removeData=function(){var e=document.getElementById(h);var t=document.getElementById(m);this.deleteRows(c);if(e!=null){this.deleteRows(e)}if(t!=null){this.deleteRows(t)}};this.adjustColumnWidth=function(e,t,r){var l=this.getObjWidth(e);var n=e.rows[0].cells[t];var i=this.getObjWidth(n);var s=10;var o=parseInt(l);var a=parseInt(i);if(r=="+"){o+=s;a+=s}else{o-=s;a-=s}n.setAttribute("width",a+"px");e.setAttribute("width",o+"px")};this.optMenuShow=function(r,l,n){var i=new MapeyeTable(e,t);var s=document.getElementById(n);var o=this.getMouseX(r);var a=this.getMouseY(r);var u=l.getAttribute("id");var d=u.substring(u.lastIndexOf("_")+1,u.length);var v=document.getElementById(m);var f=document.getElementById(h);var p=65;var g=80;if(v!=null){var w=v.rows[0].cells.length;if(d==w-1){o=o-p}}var b=l.parentNode.getElementsByTagName("input")[0].value;if(v==null&&f==null){var y=c.rows[0].cells.length;if(b==y-1){o=o-p}}s.style.width=p+"px";s.style.height=g+"px";s.style.top=a+"px";s.style.left=o+"px";s.style.display="block";s.onmouseout=function(){i.optMenuHide(n)};i.delayTime=0;s.onmouseover=function(){i.clearTime()}};this.delayTime=0;this.optMenuHide=function(e){this.delayTime=setTimeout(function(){var t=document.getElementById(e);t.style.display="none"},100)};this.clearTime=function(){clearTimeout(this.delayTime)};this.resizeTable=function(){var e=document.getElementById(d);var t=document.getElementById(v);var r=document.getElementById(m);var l=document.getElementById(h);var n=parseInt(this.getObjWidth(u));var i=parseInt(this.getObjHeight(u));var o=parseInt(this.getObjWidth(c));var a=0;var f=0;var p=0;var g=0;var w=0;if(l!=null){a=parseInt(this.getObjWidth(l))}if(r!=null){f=parseInt(this.getObjWidth(r))}if(l!=null||r!=null){g=parseInt(n*a/(a+f))}if(l==null&&r==null){p=n;if(p>o){var b=c.rows[0];var y=b.cells.length;var T=0;for(var A=0;A<y;A++){T=parseInt(this.getObjWidth(b.cells[A]));T=p*T/o;b.cells[A].setAttribute("width",T+"px")}c.setAttribute("width",p+"p")}u.setAttribute("style","overflow:auto; height:"+i+"px; width:"+p+"px")}if(l!=null){if(g>a){var b=l.rows[0];var y=b.cells.length;var T=0;for(var A=0;A<y;A++){T=parseInt(this.getObjWidth(b.cells[A]));T=g*T/a;b.cells[A].setAttribute("width",T+"px")}l.setAttribute("width",g+"p")}else{g=a}e.setAttribute("style","float:left; overflow:hidden; height:"+i+"px; width:"+g+"px")}if(r!=null){w=n-g;if(w>f){var b=r.rows[0];var y=b.cells.length;var T=0;for(var A=0;A<y;A++){T=parseInt(this.getObjWidth(b.cells[A]));T=w*T/f;b.cells[A].setAttribute("width",T+"px")}r.setAttribute("width",w+"p")}t.setAttribute("style","position:absolute; overflow:auto;"+"left:"+(g+s)+"px; height:"+i+"px; width:"+w+"px")}};this.getMouseX=function(e){if(e.pageX)return e.pageX;else if(e.clientX)return e.clientX+(document.documentElement.scrollLeft?document.documentElement.scrollLeft:document.body.scrollLeft);else return null};this.getMouseY=function(e){if(e.pageY)return e.pageY;else if(e.clientY)return e.clientY+(document.documentElement.scrollTop?document.documentElement.scrollTop:document.body.scrollTop);else return null};this.getObjHeight=function(e){var t=e.getAttribute("height");if(t!=null){if(t.indexOf("px")>0){t=t.substring(0,t.indexOf("px"))}}else{t=e.style.height;if(t==""){t=-1}else{if(t.indexOf("px")>0){t=t.substring(0,t.indexOf("px"))}}}return t};this.getObjWidth=function(e){var t=e.getAttribute("width");if(t!=null){if(t.indexOf("px")>0){t=t.substring(0,t.indexOf("px"))}if(t.indexOf("%")>0){t=t.substring(0,t.indexOf("%"));t=parseInt(this.getObjWidth(e.parentNode))*parseInt(t)/100}}else{t=e.style.width;if(t==""){t=e.offsetWidth}else{if(t.indexOf("px")>0){t=t.substring(0,t.indexOf("px"))}if(t.indexOf("%")>0){t=t.substring(0,t.indexOf("%"));t=parseInt(this.getObjWidth(e.parentNode))*parseInt(t)/100}}}return t};this.getObjPosLeft=function(e){};this.getObjPosTop=function(e){};this.copyTable=function(e,t){var r=e.rows.length;var l=e.rows[0].cells.length;var n=t.rows.length;var i=t.rows[0].cells.length;if(r!=n){alert("复制数据始表和末表行数不一致");return false}if(l!=i){alert("复制数据始表和末表列数不一致");return false}var s=r;var o=l;var a="";for(var u=0;u<s;u++){for(var c=0;c<o;c++){var d=e.rows[u].cells[c];var v=t.rows[u].cells[c];var h=d.getAttribute("title");if(u!=0&&h!=null){v.innerHTML=h;v.setAttribute("title",h)}else{v.innerHTML=d.innerHTML;v.setAttribute("title",d.innerHTML)}}}return t};this.newTable=function(e,t,r){var l=document.createElement("table");l.setAttribute("id",e);l.setAttribute("border","1");l.setAttribute("width","0px");for(var n=0;n<t;n++){var i=document.createElement("tr");for(var s=0;s<r;s++){var o=document.createElement("td");o.innerHTML="";i.appendChild(o)}l.appendChild(i)}return l};this.printArray1D=function(e){var t=e.length;var r="Array size: "+t+"\n";for(var l=0;l<t;l++){r+=e[l]+"\n"}alert(r)};this.getDataByColumn=function(e,t){var r=e.rows.length;var l=e.rows[0].cells.length;var n=t;var i=[];var s=null;var o=null;for(var a=0;a<r;a++){for(var u=0;u<l;u++){s=e.rows[a].cells[u];if(u==n){if(a==0){i[a]=s.innerHTML}else{o=s.getAttribute("title");if(o!=null){i[a]=o}else{i[a]=s.innerHTML}}}}}return i};this.tableCompressByColumn=function(e){var t=e.rows.length;var r=e.rows[0].cells.length;for(var l=0;l<r;l++){var n=0;var i="";var s=null;for(var o=0;o<t;o++){s=e.rows[o].cells[l];if(s){i=s.innerHTML;if(i.length==0){n++}}}if(n==t){e=this.columnDelete(e,l);this.tableCompressByColumn(e)}}return e};this.columnDelete=function(e,t){var r=t;var l=e.rows.length;var n=e.rows[0].cells.length;for(var i=0;i<l;i++){for(var s=0;s<n;s++){if(s==r){e.rows[i].removeChild(e.rows[i].cells[s])}}}return e};this.columnAppend=function(e,t){var r=e.rows.length;var l=t.length;if(r<=l){var n=document.createElement("tr");for(var i=0;i<l-r;i++){e.appendChild(n)}}else{alert("表格大小与追加的数据不一致！");return false}for(var s=0;s<l;s++){var o=document.createElement("td");o.innerHTML=t[s];e.rows[s].appendChild(o)}return e};this.columnInsert=function(e,t,r){var l=e.rows.length;var n=t.length;if(l<=n){var i=document.createElement("tr");for(var s=0;s<n-l;s++){e.appendChild(i)}}else{alert("表格大小与追加的数据不一致！");return false}var o=e.rows[0].length;if(r<0||r>o-1){alert("待插入数据的列指针超出表格列数！");return false}for(var a=0;a<n;a++){var u=e.rows[a].insertCell(r);u.innerHTML=t[a]}return e};this.imgDelete=function(t){var r=t.getElementsByTagName("img");for(var l=0;l<r.length;l++){if(r[l].getAttribute("id").indexOf("opt_"+e)==0){r[l].parentNode.removeChild(r[l])}}};this.styleSelectColumn=function(e,t){var l=e.rows.length;var n=e.rows[0].cells.length;var i=t;for(var s=0;s<l;s++){if(s==0){for(var o=0;o<n;o++){if(o==i){e.rows[s].cells[o].style.backgroundColor=r}else{e.rows[s].cells[o].setAttribute("style","")}}}else{for(var o=0;o<n;o++){if(o==i){e.rows[s].cells[o].style.backgroundColor=r}else{e.rows[s].cells[o].setAttribute("style","")}}}}};this.styleUnSelectColumn=function(e){var t=e.rows.length;var r=e.rows[0].cells.length;for(var l=0;l<t;l++){for(var n=0;n<r;n++){e.rows[l].cells[n].setAttribute("style","")}}};this.styleRowOnMouseOver=function(e){var t=e.rowIndex;var r=document.getElementById(h);var n=document.getElementById(m);if(r==null&&n==null){c.rows[t].style.backgroundColor=l}if(r!=null){r.rows[t].style.backgroundColor=l}if(n!=null){n.rows[t].style.backgroundColor=l}};this.styleRowOnMouseOut=function(e){var t=e.rowIndex;var r=document.getElementById(h);var l=document.getElementById(m);if(r==null&&l==null){c.rows[t].setAttribute("style","")}if(r!=null){r.rows[t].setAttribute("style","")}if(l!=null){l.rows[t].setAttribute("style","")}}}