package cn.edu.ntu.action;

import java.security.PrivilegedActionException;
import java.util.ArrayList;
import java.util.List;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import org.apache.struts2.ServletActionContext;
import com.opensymphony.xwork2.ActionSupport;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import cn.edu.ntu.entity.common.PolygonAreaSettingParam;
import cn.edu.ntu.entity.common.RectangleAreaSettingParam;
import cn.edu.ntu.entity.common.RoundAreaSettingParam;
import cn.edu.ntu.service.common.interfaces.EFenceService;

@Controller("eFenceAction")
@Scope("prototype")
public class EFenceAction extends ActionSupport {
	
	private int areaType;
	private String areaName;
	private String areaAlias;
	private String startTime;
	private String endTime;
	private int maxSpeed;
	private int lastTimeOverSpeed;
	private int radius;
	private float centerLat;
	private float centerLng;
	
	private float latOnLeftAndTop;
	private float lngOnLeftAndTop;
	private float latOnRightAndBottom;
	private float lngOnRightAndBottom;
	private int width;
	private int height;
	
	private int numberOfVertex;
	private ArrayList<Float> latArr;
	private ArrayList<Float> lonArr;
	
	@Autowired
	private EFenceService eFenceService;
	
	public int getAreaType() {
		return areaType;
	}
	public void setAreaType(int areaType) {
		this.areaType = areaType;
	}
	public String getAreaName() {
		return areaName;
	}
	public void setAreaName(String areaName) {
		this.areaName = areaName;
	}
	public String getAreaAlias() {
		return areaAlias;
	}
	public void setAreaAlias(String areaAlias) {
		this.areaAlias = areaAlias;
	}
	public String getStartTime() {
		return startTime;
	}
	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}
	public String getEndTime() {
		return endTime;
	}
	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}
	public int getMaxSpeed() {
		return maxSpeed;
	}
	public void setMaxSpeed(int maxSpeed) {
		this.maxSpeed = maxSpeed;
	}
	public int getLastTimeOverSpeed() {
		return lastTimeOverSpeed;
	}
	public void setLastTimeOverSpeed(int lastTimeOverSpeed) {
		this.lastTimeOverSpeed = lastTimeOverSpeed;
	}
	public int getRadius() {
		return radius;
	}
	public void setRadius(int radius) {
		this.radius = radius;
	}
	public float getCenterLat() {
		return centerLat;
	}
	public void setCenterLat(float centerLat) {
		this.centerLat = centerLat;
	}
	public float getCenterLng() {
		return centerLng;
	}
	public void setCenterLng(float centerLng) {
		this.centerLng = centerLng;
	}
	public float getLatOnLeftAndTop() {
		return latOnLeftAndTop;
	}
	public void setLatOnLeftAndTop(float latOnLeftAndTop) {
		this.latOnLeftAndTop = latOnLeftAndTop;
	}
	public float getLngOnLeftAndTop() {
		return lngOnLeftAndTop;
	}
	public void setLngOnLeftAndTop(float lngOnLeftAndTop) {
		this.lngOnLeftAndTop = lngOnLeftAndTop;
	}
	public float getLatOnRightAndBottom() {
		return latOnRightAndBottom;
	}
	public void setLatOnRightAndBottom(float latOnRightAndBottom) {
		this.latOnRightAndBottom = latOnRightAndBottom;
	}
	public float getLngOnRightAndBottom() {
		return lngOnRightAndBottom;
	}
	public void setLngOnRightAndBottom(float lngOnRightAndBottom) {
		this.lngOnRightAndBottom = lngOnRightAndBottom;
	}
	public int getWidth() {
		return width;
	}
	public void setWidth(int width) {
		this.width = width;
	}
	public int getHeight() {
		return height;
	}
	public void setHeight(int height) {
		this.height = height;
	}
	
	public int getNumberOfVertex() {
		return numberOfVertex;
	}
	public void setNumberOfVertex(int numberOfVertex) {
		this.numberOfVertex = numberOfVertex;
	}
	public ArrayList<Float> getLatArr() {
		return latArr;
	}
	public void setLatArr(ArrayList<Float> latArr) {
		this.latArr = latArr;
	}
	public ArrayList<Float> getLonArr() {
		return lonArr;
	}
	public void setLonArr(ArrayList<Float> lonArr) {
		this.lonArr = lonArr;
	}
	public String addRoundAreaSetting(){
		
		RoundAreaSettingParam param=new RoundAreaSettingParam();
		param.setAreaType(areaType);
		param.setAreaName(areaName);
		param.setAreaAlias(areaAlias);
		param.setStartTime(startTime);
		param.setEndTime(endTime);
	    param.setMaxSpeed(maxSpeed);
	    param.setLastTimeOverSpeed(lastTimeOverSpeed);
	    param.setRadius(radius);
	    param.setCenterLat(centerLat);
	    param.setCenterLng(centerLng);
		
	    String name ="";
		HttpServletRequest request=ServletActionContext.getRequest();
		Cookie[] cookies=request.getCookies();
		for(Cookie cookie:cookies){
			if(cookie.getName().equals("accountName"))
				name=cookie.getValue();
		}
		
		eFenceService.addRoundAreaSetting(param,name);
		
		return null;
	}
	
	public String addRectangleAreaSetting(){
		
		RectangleAreaSettingParam param=new RectangleAreaSettingParam();
		param.setAreaType(areaType);
		param.setAreaName(areaName);
		param.setAreaAlias(areaAlias);
		param.setStartTime(startTime);
		param.setEndTime(endTime);
	    param.setMaxSpeed(maxSpeed);
	    param.setLastTimeOverSpeed(lastTimeOverSpeed);
	    param.setLatOnLeftAndTop(latOnLeftAndTop);
	    param.setLngOnLeftAndTop(lngOnLeftAndTop);
	    param.setLatOnRightAndBottom(latOnRightAndBottom);
	    param.setLngOnRightAndBottom(lngOnRightAndBottom);
	    param.setHeight(height);
	    param.setWidth(width);
	    
	    String name ="";
		HttpServletRequest request=ServletActionContext.getRequest();
		Cookie[] cookies=request.getCookies();
		for(Cookie cookie:cookies){
			if(cookie.getName().equals("accountName"))
				name=cookie.getValue();
		}
		
		eFenceService.addRectangleAreaSetting(param,name);
		
		return null;
	}
	
	public String addPolygonAreaSetting(){
		
		PolygonAreaSettingParam param=new PolygonAreaSettingParam();
		param.setAreaName(areaName);
		param.setAreaAlias(areaAlias);
		param.setAreaType(areaType);
		param.setStartTime(startTime);
		param.setEndTime(endTime);
		param.setMaxSpeed(maxSpeed);
		param.setLastTimeOverSpeed(lastTimeOverSpeed);
		param.setNumberOfVertex(numberOfVertex);
		param.setLatArr(latArr);
		param.setLonArr(lonArr);
		
		String name = "";
		HttpServletRequest request = ServletActionContext.getRequest();
		Cookie[] cookies = request.getCookies();
		for (Cookie cookie : cookies) {
			if (cookie.getName().equals("accountName"))
				name = cookie.getValue();
		}
		
		eFenceService.addPolygonAreaSetting(param,name);
			
		return null;
	}

}
