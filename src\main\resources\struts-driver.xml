<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE struts PUBLIC
	"-//Apache Software Foundation//DTD Struts Configuration 2.3//EN"
	"http://struts.apache.org/dtds/struts-2.3.dtd">

<struts>

	<constant name="struts.devMode" value="true" />
	
	<constant name="struts.objectFactory" value="org.apache.struts2.spring.StrutsSpringObjectFactory" />

	<package name="driver" namespace="/driver" extends="struts-default">
        <action name="listAction" class="driverAction" method="list">
            <result>/page/driverManage.jsp</result>
        </action>
        
        <action name="addAction" class="driverAction" method="add">
            <result>/page/driverEdit.jsp</result>
        </action>
        
        <action name="addStoreAction" class="driverAction" method="addStore">
            <result type="redirect">listAction.action</result>
        </action>
        
        <action name="editAction" class="driverAction" method="edit">
            <result>/page/driverEdit.jsp</result>
        </action>
        
        <action name="editStoreAction" class="driverAction" method="editStore">
            <result type="redirect">listAction.action</result>
        </action>
        
        <action name="deleteAction" class="driverAction" method="delete">
            <result type="redirect">listAction.action</result>
        </action>
        
        <action name="queryAction" class="driverAction" method="query">
            <result>/page/driverManage.jsp</result>
        </action>
    </package>
    
</struts>