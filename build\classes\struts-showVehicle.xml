<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE struts PUBLIC
	"-//Apache Software Foundation//DTD Struts Configuration 2.3//EN"
	"http://struts.apache.org/dtds/struts-2.3.dtd">

<struts>

	<constant name="struts.devMode" value="true" />
	
	<constant name="struts.objectFactory" value="org.apache.struts2.spring.StrutsSpringObjectFactory" />
	
	<package name="showVehicle" namespace="/showVehicle" extends="json-default">
    
	    <action name="showAllGroupAction" class="vehicleGroupTreeAction"
		    method="showAllGroup">
		</action>
		<action name="showSearchedGroupAction" class="vehicleGroupTreeAction"
		    method="showSearchedGroup">
		</action>
		
	    <action name="showAllVehiclesAction" class="vehicleGpsAction"
	        method="showAllVehicles">
	        <result name="all" type="json">
	            <param name="root">vehicleTotalInfo</param>
	        </result>    
	    </action>
	    
	     <action name="showSingleVehicleAction" class="vehicleGpsAction"
	        method="showSingleVehicle">
	        <result name="single" type="json">
	            <param name="root">vehicleGpsInfo</param>
	        </result>
	    </action>
	    
		<!-- added by Dawn Che here -->
		<action name="viewTrackAction" class="trackAction" >
			<result type="json">
				<param name="root">result</param>
			</result>
		</action>
		
		<action name="exportExcelAction" class="trackAction" method="exportExcel">
		</action>
	</package>
	
	
	
</struts>