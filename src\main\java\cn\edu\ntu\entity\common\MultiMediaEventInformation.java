package cn.edu.ntu.entity.common;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.apache.struts2.json.annotations.JSON;

/**
 *
 * 
 * <AUTHOR>
 * @date 2016��8��30�� ����10:02:50 
 */

public class MultiMediaEventInformation {
    @Id
    @GeneratedValue(strategy=GenerationType.AUTO)
	private int id;
	private String carNumber;
    @Column(name="simcardnumber",length=20)
	private String simCardNumber;
    @Column(name="commandserialnumber")
	private int commandSerialNumber;
    @Column(name="eventtype",length=50)
	private String eventType;
    @Column(name="mediatype",length=10)
	private String mediaType;
    @Column(name="filetype",length=10)
	private String fileType;
    @Column(name="channelid")
	private int channelId;
    @Column(name="createtime")
    @Temporal(TemporalType.TIMESTAMP)
	private Date createTime;
	
	public String getCarNumber() {
		return carNumber;
	}
	public void setCarNumber(String carNumber) {
		this.carNumber = carNumber;
	}
	public String getSimCardNumber() {
		return simCardNumber;
	}
	public void setSimCardNumber(String simCardNumber) {
		this.simCardNumber = simCardNumber;
	}
	public int getCommandSerialNumber() {
		return commandSerialNumber;
	}
	public void setCommandSerialNumber(int commandSerialNumber) {
		this.commandSerialNumber = commandSerialNumber;
	}
	public String getEventType() {
		return eventType;
	}
	public void setEventType(String eventType) {
		this.eventType = eventType;
	}
	public String getMediaType() {
		return mediaType;
	}
	public void setMediaType(String mediaType) {
		this.mediaType = mediaType;
	}
	public String getFileType() {
		return fileType;
	}
	public void setFileType(String fileType) {
		this.fileType = fileType;
	}
	public int getChannelId() {
		return channelId;
	}
	public void setChannelId(int channelId) {
		this.channelId = channelId;
	}
	@JSON(format="yyyy-MM-dd HH:mm:ss")
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	
	
}
