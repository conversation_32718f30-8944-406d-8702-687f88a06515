<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>六通道视频监控系统</title>
    <style>
        body {
            margin: 0;
            padding: 10px;
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
            box-sizing: border-box;
        }
        
        .header {
            background-color: #333;
            color: white;
            padding: 15px;
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            border-radius: 8px;
        }
        
        .status-bar {
            background-color: #007bff;
            color: white;
            padding: 8px 15px;
            text-align: center;
            font-size: 14px;
            margin-bottom: 15px;
            border-radius: 4px;
        }
        
        .video-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: repeat(2, 1fr);
            gap: 15px;
            max-width: 1500px;
            margin: 0 auto;
            justify-content: center;
        }
        
        .video-container {
            width: 480px;
            height: 360px;
            position: relative;
            background-color: #000;
            border: 2px solid #333;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        
        .video-header {
            background-color: #333;
            color: white;
            padding: 8px 12px;
            font-size: 14px;
            font-weight: bold;
            text-align: center;
            height: 20px;
            line-height: 20px;
        }
        
        .video-box {
            width: 100%;
            height: 320px;
            position: relative;
        }
        
        video {
            width: 480px;
            height: 320px;
            object-fit: fill;
            background-color: #000;
            display: block;
        }
        
        video::-webkit-media-controls-play-button {
            display: none;
        }
        
        video::-webkit-media-controls-current-time-display {
            display: none;
        }
        
        video::-webkit-media-controls-timeline {
            display: none;
        }
        
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 14px;
            color: #fff;
            background-color: rgba(0, 0, 0, 0.8);
            padding: 8px 16px;
            border-radius: 4px;
            text-align: center;
        }
        
        .controls {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 10px 16px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        
        .btn:hover {
            background-color: #0056b3;
        }
        
        .btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        
        .btn-group {
            display: flex;
            gap: 5px;
        }
        
        .channel-status {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 10px;
            margin: 15px 0;
            max-width: 1500px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .channel-indicator {
            background-color: #dc3545;
            color: white;
            padding: 5px;
            text-align: center;
            border-radius: 4px;
            font-size: 12px;
            transition: background-color 0.3s;
        }
        
        .channel-indicator.connected {
            background-color: #28a745;
        }
        
        .channel-indicator.connecting {
            background-color: #ffc107;
            color: #000;
        }
        
        /* 保持3x2布局，但在小屏幕上缩放视频尺寸 */
        @media (max-width: 1500px) {
            .video-container {
                width: 400px;
                height: 300px;
            }

            .video-box {
                height: 260px;
            }

            video {
                width: 400px;
                height: 260px;
            }
        }

        @media (max-width: 1300px) {
            .video-container {
                width: 320px;
                height: 240px;
            }

            .video-box {
                height: 200px;
            }

            video {
                width: 320px;
                height: 200px;
            }
        }

        /* 只有在非常小的屏幕上才改变布局 */
        @media (max-width: 1000px) {
            .video-grid {
                grid-template-columns: repeat(2, 1fr);
                grid-template-rows: repeat(3, 1fr);
            }

            .video-container {
                width: 300px;
                height: 220px;
            }

            .video-box {
                height: 180px;
            }

            video {
                width: 300px;
                height: 180px;
            }
        }

        @media (max-width: 650px) {
            .video-grid {
                grid-template-columns: 1fr;
                grid-template-rows: repeat(6, 1fr);
            }

            .video-container {
                width: 100%;
                max-width: 400px;
                height: 280px;
                margin: 0 auto;
            }

            .video-box {
                height: 240px;
            }

            video {
                width: 100%;
                height: 240px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        六通道视频监控系统
    </div>
    
    <div class="status-bar">
        <span id="status">系统准备就绪 - 正在初始化...</span>
    </div>
    
    <div class="channel-status">
        <div class="channel-indicator" id="indicator1">通道1: 断开</div>
        <div class="channel-indicator" id="indicator2">通道2: 断开</div>
        <div class="channel-indicator" id="indicator3">通道3: 断开</div>
        <div class="channel-indicator" id="indicator4">通道4: 断开</div>
        <div class="channel-indicator" id="indicator5">通道5: 断开</div>
        <div class="channel-indicator" id="indicator6">通道6: 断开</div>
    </div>
    
    <div class="video-grid">
        <!-- 通道1 -->
        <div class="video-container">
            <div class="video-header">通道 1</div>
            <div class="video-box">
                <video controls="controls" class="demo-video" id="player1" muted></video>
                <div id="loading1" class="loading" style="display: block;">加载中...</div>
            </div>
        </div>
        
        <!-- 通道2 -->
        <div class="video-container">
            <div class="video-header">通道 2</div>
            <div class="video-box">
                <video controls="controls" class="demo-video" id="player2" muted></video>
                <div id="loading2" class="loading" style="display: block;">加载中...</div>
            </div>
        </div>
        
        <!-- 通道3 -->
        <div class="video-container">
            <div class="video-header">通道 3</div>
            <div class="video-box">
                <video controls="controls" class="demo-video" id="player3" muted></video>
                <div id="loading3" class="loading" style="display: block;">加载中...</div>
            </div>
        </div>
        
        <!-- 通道4 -->
        <div class="video-container">
            <div class="video-header">通道 4</div>
            <div class="video-box">
                <video controls="controls" class="demo-video" id="player4" muted></video>
                <div id="loading4" class="loading" style="display: block;">加载中...</div>
            </div>
        </div>
        
        <!-- 通道5 -->
        <div class="video-container">
            <div class="video-header">通道 5</div>
            <div class="video-box">
                <video controls="controls" class="demo-video" id="player5" muted></video>
                <div id="loading5" class="loading" style="display: block;">加载中...</div>
            </div>
        </div>
        
        <!-- 通道6 -->
        <div class="video-container">
            <div class="video-header">通道 6</div>
            <div class="video-box">
                <video controls="controls" class="demo-video" id="player6" muted></video>
                <div id="loading6" class="loading" style="display: block;">加载中...</div>
            </div>
        </div>
    </div>
    
    <div class="controls">
        <button class="btn" onclick="reconnectAll()">🔄 重新连接所有通道</button>
        <div class="btn-group">
            <button class="btn" onclick="reconnectChannel(1)">重连通道1</button>
            <button class="btn" onclick="reconnectChannel(2)">重连通道2</button>
            <button class="btn" onclick="reconnectChannel(3)">重连通道3</button>
        </div>
        <div class="btn-group">
            <button class="btn" onclick="reconnectChannel(4)">重连通道4</button>
            <button class="btn" onclick="reconnectChannel(5)">重连通道5</button>
            <button class="btn" onclick="reconnectChannel(6)">重连通道6</button>
        </div>
    </div>

    <!-- 引入 flv.js 库 -->
    <script src="./flv.js"></script>
    
    <script>
        const host="localhost"
        const port=8888

        class VideoPlayer {
            constructor(channel) {
                this.channel = channel;
                this.player = null;
                this.loading = true;
                this.videoElement = document.getElementById(`player${channel}`);
                this.loadingElement = document.getElementById(`loading${channel}`);
                this.rtspUrl = "ws://"+host+":"+port+"/rtsp?deviceId=16065442039&channel=${channel}";
           

                this.init();
            }

            init() {
                // 绑定双击全屏事件
                this.videoElement.addEventListener('dblclick', () => {
                    this.fullScreen();
                });

                // 等待 flv.js 加载完成后播放视频
                this.waitForFlvjs();

                // 页面卸载时清理资源
                window.addEventListener('beforeunload', () => {
                    this.cleanup();
                });
            }

            waitForFlvjs() {
                // 检查 flv.js 是否已加载
                if (typeof flvjs !== 'undefined') {
                    this.playVideo();
                } else {
                    // 如果还没加载，等待 100ms 后重试
                    setTimeout(() => {
                        this.waitForFlvjs();
                    }, 100);
                }
            }
            
            fullScreen() {
                const video = this.videoElement;
                if (video.requestFullscreen) {
                    video.requestFullscreen();
                } else if (video.mozRequestFullScreen) {
                    video.mozRequestFullScreen();
                } else if (video.webkitRequestFullScreen) {
                    video.webkitRequestFullScreen();
                } else if (video.msRequestFullscreen) {
                    video.msRequestFullscreen();
                }
            }

            playVideo() {
                const time1 = new Date().getTime();
                this.updateChannelStatus('connecting');

                if (flvjs.isSupported()) {
                    const video = this.videoElement;

                    if (video) {
                        // 如果已有播放器，先清理
                        if (this.player) {
                            this.player.unload();
                            this.player.destroy();
                            this.player = null;
                            this.loading = true;
                            this.loadingElement.style.display = 'block';
                        }

                        // 创建新的播放器
                        this.player = flvjs.createPlayer({
                            type: 'flv',
                            isLive: true,
                            url: this.rtspUrl
                        });

                        this.player.attachMediaElement(video);

                        try {
                            this.player.load();
                            this.player.play().then(() => {
                                console.log(`通道${this.channel}播放开始，耗时:`, new Date().getTime() - time1, 'ms');
                                this.loading = false;
                                this.loadingElement.style.display = 'none';
                                this.updateChannelStatus('connected');
                                this.updateGlobalStatus(`通道${this.channel}连接成功`);
                            }).catch((error) => {
                                console.error(`通道${this.channel}播放失败:`, error);
                                this.loadingElement.textContent = '播放失败';
                                this.updateChannelStatus('disconnected');
                                this.updateGlobalStatus(`通道${this.channel}播放失败`);
                            });
                        } catch (error) {
                            console.error(`通道${this.channel}加载失败:`, error);
                            this.loadingElement.textContent = '加载失败';
                            this.updateChannelStatus('disconnected');
                            this.updateGlobalStatus(`通道${this.channel}加载失败`);
                        }
                    }
                } else {
                    console.error('当前浏览器不支持 FLV.js');
                    this.loadingElement.textContent = '当前浏览器不支持 FLV 播放';
                    this.updateChannelStatus('disconnected');
                    this.updateGlobalStatus('浏览器不支持 FLV 播放');
                }
            }

            updateChannelStatus(status) {
                const indicator = document.getElementById(`indicator${this.channel}`);
                indicator.className = 'channel-indicator';

                switch(status) {
                    case 'connected':
                        indicator.classList.add('connected');
                        indicator.textContent = `通道${this.channel}: 已连接`;
                        break;
                    case 'connecting':
                        indicator.classList.add('connecting');
                        indicator.textContent = `通道${this.channel}: 连接中...`;
                        break;
                    case 'disconnected':
                    default:
                        indicator.textContent = `通道${this.channel}: 断开`;
                        break;
                }
            }

            updateGlobalStatus(message) {
                const statusElement = document.getElementById('status');
                const timestamp = new Date().toLocaleTimeString();
                statusElement.textContent = `[${timestamp}] ${message}`;
            }

            // 重新播放（用于重连等场景）
            replay() {
                this.playVideo();
            }

            // 清理资源
            cleanup() {
                if (this.player) {
                    this.player.unload();
                    this.player.destroy();
                    this.player = null;
                }
            }
        }

        // 全局变量存储播放器实例
        let videoPlayers = {};

        // 确保 flv.js 加载完成后再初始化
        function initVideoPlayers() {
            if (typeof flvjs !== 'undefined') {
                // 初始化六个通道的播放器
                for (let i = 1; i <= 6; i++) {
                    videoPlayers[i] = new VideoPlayer(i);
                }

                console.log('六通道视频播放器初始化完成');
                updateGlobalStatus('六通道视频播放器初始化完成');

                // 键盘快捷键
                document.addEventListener('keydown', function(event) {
                    switch(event.key.toLowerCase()) {
                        case 'r':
                            reconnectAll();
                            break;
                        case '1':
                        case '2':
                        case '3':
                        case '4':
                        case '5':
                        case '6':
                            reconnectChannel(parseInt(event.key));
                            break;
                        case 'a':
                            // A键：重连所有通道
                            reconnectAll();
                            break;
                    }
                });
            } else {
                // 如果 flv.js 还没加载，等待 100ms 后重试
                setTimeout(initVideoPlayers, 100);
            }
        }

        // 重连所有通道
        function reconnectAll() {
            console.log('重新连接所有通道...');
            updateGlobalStatus('正在重新连接所有通道...');

            Object.values(videoPlayers).forEach((player, index) => {
                if (player) {
                    // 延迟启动，避免同时发起太多连接
                    setTimeout(() => {
                        player.replay();
                    }, index * 500);
                }
            });
        }

        // 重连指定通道
        function reconnectChannel(channel) {
            console.log(`重新连接通道${channel}...`);
            if (videoPlayers[channel]) {
                videoPlayers[channel].replay();
                updateGlobalStatus(`正在重新连接通道${channel}...`);
            }
        }

        // 更新全局状态
        function updateGlobalStatus(message) {
            const statusElement = document.getElementById('status');
            const timestamp = new Date().toLocaleTimeString();
            statusElement.textContent = `[${timestamp}] ${message}`;
        }

        // 获取连接统计
        function getConnectionStats() {
            const indicators = document.querySelectorAll('.channel-indicator');
            let connected = 0;
            let connecting = 0;
            let disconnected = 0;

            indicators.forEach(indicator => {
                if (indicator.classList.contains('connected')) {
                    connected++;
                } else if (indicator.classList.contains('connecting')) {
                    connecting++;
                } else {
                    disconnected++;
                }
            });

            return { connected, connecting, disconnected };
        }

        // 定期更新连接统计
        setInterval(() => {
            const stats = getConnectionStats();
            if (stats.connected > 0 || stats.connecting > 0) {
                updateGlobalStatus(`连接状态: ${stats.connected}个已连接, ${stats.connecting}个连接中, ${stats.disconnected}个断开`);
            }
        }, 5000);

        // 页面加载完成后初始化播放器
        document.addEventListener('DOMContentLoaded', initVideoPlayers);
    </script>
</body>
</html>
