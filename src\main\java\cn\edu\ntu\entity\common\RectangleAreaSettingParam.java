package cn.edu.ntu.entity.common;

/**
 *
 * 
 * <AUTHOR>
 * @date 2017��6��11�� ����4:05:54 
 */
public class RectangleAreaSettingParam {
	private int areaType;
	private String areaName;
	private String areaAlias;
	private String startTime;
	private String endTime;
	private int maxSpeed;
	private int lastTimeOverSpeed;
	private int width;
	private int height;
	private float latOnLeftAndTop;
	private float lngOnLeftAndTop;
	private float latOnRightAndBottom;
	private float lngOnRightAndBottom;
	
	public int getAreaType() {
		return areaType;
	}
	public void setAreaType(int areaType) {
		this.areaType = areaType;
	}
	public String getAreaName() {
		return areaName;
	}
	public void setAreaName(String areaName) {
		this.areaName = areaName;
	}
	public String getAreaAlias() {
		return areaAlias;
	}
	public void setAreaAlias(String areaAlias) {
		this.areaAlias = areaAlias;
	}
	public String getStartTime() {
		return startTime;
	}
	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}
	public String getEndTime() {
		return endTime;
	}
	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}
	public int getMaxSpeed() {
		return maxSpeed;
	}
	public void setMaxSpeed(int maxSpeed) {
		this.maxSpeed = maxSpeed;
	}
	public int getLastTimeOverSpeed() {
		return lastTimeOverSpeed;
	}
	public void setLastTimeOverSpeed(int lastTimeOverSpeed) {
		this.lastTimeOverSpeed = lastTimeOverSpeed;
	}
	public int getWidth() {
		return width;
	}
	public void setWidth(int width) {
		this.width = width;
	}
	public int getHeight() {
		return height;
	}
	public void setHeight(int height) {
		this.height = height;
	}
	public float getLatOnLeftAndTop() {
		return latOnLeftAndTop;
	}
	public void setLatOnLeftAndTop(float latOnLeftAndTop) {
		this.latOnLeftAndTop = latOnLeftAndTop;
	}
	public float getLngOnLeftAndTop() {
		return lngOnLeftAndTop;
	}
	public void setLngOnLeftAndTop(float lngOnLeftAndTop) {
		this.lngOnLeftAndTop = lngOnLeftAndTop;
	}
	public float getLatOnRightAndBottom() {
		return latOnRightAndBottom;
	}
	public void setLatOnRightAndBottom(float latOnRightAndBottom) {
		this.latOnRightAndBottom = latOnRightAndBottom;
	}
	public float getLngOnRightAndBottom() {
		return lngOnRightAndBottom;
	}
	public void setLngOnRightAndBottom(float lngOnRightAndBottom) {
		this.lngOnRightAndBottom = lngOnRightAndBottom;
	}
	
	
}
