package cn.edu.ntu.entity.common;


public class RoundAreaSettingParam {

	private int areaType;
	private String areaName;
	private String areaAlias;
	private String startTime;
	private String endTime;
	private int maxSpeed;
	private int lastTimeOverSpeed;
	private int radius;
	private float centerLat;
	private float centerLng;
	
	public int getAreaType() {
		return areaType;
	}
	public void setAreaType(int areaType) {
		this.areaType = areaType;
	}
	public String getAreaName() {
		return areaName;
	}
	public void setAreaName(String areaName) {
		this.areaName = areaName;
	}
	public String getAreaAlias() {
		return areaAlias;
	}
	public void setAreaAlias(String areaAlias) {
		this.areaAlias = areaAlias;
	}
	public String getStartTime() {
		return startTime;
	}
	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}
	public String getEndTime() {
		return endTime;
	}
	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}
	public int getMaxSpeed() {
		return maxSpeed;
	}
	public void setMaxSpeed(int maxSpeed) {
		this.maxSpeed = maxSpeed;
	}
	public int getLastTimeOverSpeed() {
		return lastTimeOverSpeed;
	}
	public void setLastTimeOverSpeed(int lastTimeOverSpeed) {
		this.lastTimeOverSpeed = lastTimeOverSpeed;
	}
	public int getRadius() {
		return radius;
	}
	public void setRadius(int radius) {
		this.radius = radius;
	}
	public float getCenterLat() {
		return centerLat;
	}
	public void setCenterLat(float centerLat) {
		this.centerLat = centerLat;
	}
	public float getCenterLng() {
		return centerLng;
	}
	public void setCenterLng(float centerLng) {
		this.centerLng = centerLng;
	}
	
	
}
