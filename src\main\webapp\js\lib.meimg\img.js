function MapeyeImg(e,t,n){this.divId=e;this.picUrlArr=t;this.picNoteArr=n;var i=document.getElementById(e);i.innerHTML="";var r=t.length;var a=document.createElement("img");var s=0;var c=0;var u=document.createElement("div");var d=document.createElement("a");var p=document.createElement("a");var l=document.createElement("a");var b=document.createElement("a");var m=document.createElement("span");var h=document.createElement("span");var o=document.createElement("span");var A=document.createElement("span");var v=document.createElement("span");var H=document.createElement("div");d.setAttribute("id","pageFirst");p.setAttribute("id","pagePrior");l.setAttribute("id","pageNext");b.setAttribute("id","pageLast");d.setAttribute("href","#");p.setAttribute("href","#");l.setAttribute("href","#");b.setAttribute("href","#");d.innerHTML="首页";p.innerHTML="上一页";l.innerHTML="下一页";b.innerHTML="尾页";m.innerHTML="&nbsp;&nbsp;";h.innerHTML="&nbsp;&nbsp;";o.innerHTML="&nbsp;&nbsp;";A.innerHTML="&nbsp;&nbsp;";u.appendChild(d);u.appendChild(m);u.appendChild(p);u.appendChild(h);u.appendChild(l);u.appendChild(o);u.appendChild(b);u.appendChild(A);u.appendChild(v);this.setWidth=function(e){s=e};this.setHeight=function(e){c=e};this.init=function(){i.appendChild(H);i.appendChild(a);a.setAttribute("id","meImg_0");a.setAttribute("width",s+"px");a.setAttribute("height",c+"px");a.setAttribute("src",t[0]);v.innerHTML="(1/"+r+")";H.innerHTML=n[0];u.setAttribute("width",s+"px");u.setAttribute("height","30px");u.setAttribute("class","footMenu");H.setAttribute("width",s+"px");H.setAttribute("height","30px");H.setAttribute("class","noteHead")};this.show=function(){i.appendChild(u);d.onclick=function(){event.cancelBubble=true;a.setAttribute("src",t[0]);a.setAttribute("id","meImg_0");v.innerHTML="(1/"+r+")";H.innerHTML=n[0]};b.onclick=function(){event.cancelBubble=true;a.setAttribute("src",t[r-1]);a.setAttribute("id","meImg_"+(r-1));v.innerHTML="("+r+"/"+r+")";H.innerHTML=n[r-1]};p.onclick=function(){var e=parseInt(a.getAttribute("id").split("_")[1]);event.cancelBubble=true;if(e>0){e=e-1;a.setAttribute("src",t[e]);a.setAttribute("id","meImg_"+e);v.innerHTML="("+(e+1)+"/"+r+")";H.innerHTML=n[e]}else{alert("已到达首页！")}};l.onclick=function(){var e=parseInt(a.getAttribute("id").split("_")[1]);event.cancelBubble=true;if(e<r-1){e=e+1;a.setAttribute("src",t[e]);a.setAttribute("id","meImg_"+e);v.innerHTML="("+(e+1)+"/"+r+")";H.innerHTML=n[e]}else{alert("已到达尾页！")}}}}